{"apps": [{"name": "bama-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4000}}, {"name": "beol-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4001}}, {"name": "feol-dev", "script": "dist/server/server.mjs", "cwd": "/var/www/front-end/feol", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4000}}, {"name": "veol-dev", "script": "dist/server/server.mjs", "cwd": "/var/www/front-end/veol", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4001}}, {"name": "erdon-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4001}}, {"name": "bama-beta", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4083}}, {"name": "beol-beta", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4084}}, {"name": "kpi-beta", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4085}}, {"name": "bama-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/bamafe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30013}, "out_file": "/content/logs/bamafe/out.log", "err_file": "/content/logs/bamafe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "kpi-prod", "script": "dist/server/server.mjs", "max_memory_restart": "450M", "cwd": "/content/apps/kpife/app", "exec_mode": "cluster", "instances": "3", "max_restarts": 10, "env": {"PORT": 30011}, "out_file": "/content/logs/kpife/out.log", "err_file": "/content/logs/kpife/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "feol-beta", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4087}}, {"name": "veol-beta", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4089}}, {"name": "baon-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/baonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30019}, "out_file": "/content/logs/baonfe/out.log", "err_file": "/content/logs/baonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "beol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/beolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30015}, "out_file": "/content/logs/beolfe/out.log", "err_file": "/content/logs/beolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "boon-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/boonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30021}, "out_file": "/content/logs/boonfe/out.log", "err_file": "/content/logs/boonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "delmagyar-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/delmagyarfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30023}, "out_file": "/content/logs/delmagyarfe/out.log", "err_file": "/content/logs/delmagyarfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "duol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/duolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30025}, "out_file": "/content/logs/duolfe/out.log", "err_file": "/content/logs/duolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "erdon-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/erdonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30027}, "out_file": "/content/logs/erdonfe/out.log", "err_file": "/content/logs/erdonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "feol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/feolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30029}, "out_file": "/content/logs/feolfe/out.log", "err_file": "/content/logs/feolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "haon-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/haonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30031}, "out_file": "/content/logs/haonfe/out.log", "err_file": "/content/logs/haonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "heol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/heolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30033}, "out_file": "/content/logs/heolfe/out.log", "err_file": "/content/logs/heolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "kemma-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/kemmafe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30035}, "out_file": "/content/logs/kemmafe/out.log", "err_file": "/content/logs/kemmafe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "kisalfold-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/kisalfoldfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30037}, "out_file": "/content/logs/kisalfoldfe/out.log", "err_file": "/content/logs/kisalfoldfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "nool-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/noolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30017}, "out_file": "/content/logs/noolfe/out.log", "err_file": "/content/logs/noolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "sonline-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/sonlinefe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30039}, "out_file": "/content/logs/sonlinefe/out.log", "err_file": "/content/logs/sonlinefe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "szoljon-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/szoljonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30041}, "out_file": "/content/logs/szoljonfe/out.log", "err_file": "/content/logs/szoljonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "szon-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/szonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30043}, "out_file": "/content/logs/szonfe/out.log", "err_file": "/content/logs/szonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "teol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/teolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30045}, "out_file": "/content/logs/teolfe/out.log", "err_file": "/content/logs/teolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "vaol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/vaolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30047}, "out_file": "/content/logs/vaolfe/out.log", "err_file": "/content/logs/vaolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "veol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/veolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30049}, "out_file": "/content/logs/veolfe/out.log", "err_file": "/content/logs/veolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "zaol-prod", "script": "dist/server/server.mjs", "cwd": "/content/apps/zaolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30051}, "out_file": "/content/logs/zaolfe/out.log", "err_file": "/content/logs/zaolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "bama-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/bamafe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30013}, "out_file": "/content/logs/bamafe/out.log", "err_file": "/content/logs/bamafe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "kpi-test", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4107}, "instances": "2"}, {"name": "baon-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/baonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30019}, "out_file": "/content/logs/baonfe/out.log", "err_file": "/content/logs/baonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "beol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/beolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30015}, "out_file": "/content/logs/beolfe/out.log", "err_file": "/content/logs/beolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "boon-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/boonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30021}, "out_file": "/content/logs/boonfe/out.log", "err_file": "/content/logs/boonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "delmagyar-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/delmagyarfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30023}, "out_file": "/content/logs/delmagyarfe/out.log", "err_file": "/content/logs/delmagyarfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "duol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/duolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30025}, "out_file": "/content/logs/duolfe/out.log", "err_file": "/content/logs/duolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "erdon-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/erdonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30027}, "out_file": "/content/logs/erdonfe/out.log", "err_file": "/content/logs/erdonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "feol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/feolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30029}, "out_file": "/content/logs/feolfe/out.log", "err_file": "/content/logs/feolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "haon-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/haonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30031}, "out_file": "/content/logs/haonfe/out.log", "err_file": "/content/logs/haonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "heol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/heolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30033}, "out_file": "/content/logs/heolfe/out.log", "err_file": "/content/logs/heolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "kemma-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/kemmafe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30035}, "out_file": "/content/logs/kemmafe/out.log", "err_file": "/content/logs/kemmafe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "kisalfold-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/kisalfoldfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30037}, "out_file": "/content/logs/kisalfoldfe/out.log", "err_file": "/content/logs/kisalfoldfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "nool-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/noolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30017}, "out_file": "/content/logs/noolfe/out.log", "err_file": "/content/logs/noolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "sonline-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/sonlinefe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30039}, "out_file": "/content/logs/sonlinefe/out.log", "err_file": "/content/logs/sonlinefe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "szoljon-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/szoljonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30041}, "out_file": "/content/logs/szoljonfe/out.log", "err_file": "/content/logs/szoljonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "szon-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/szonfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30043}, "out_file": "/content/logs/szonfe/out.log", "err_file": "/content/logs/szonfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "teol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/teolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30045}, "out_file": "/content/logs/teolfe/out.log", "err_file": "/content/logs/teolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "vaol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/vaolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30047}, "out_file": "/content/logs/vaolfe/out.log", "err_file": "/content/logs/vaolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "veol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/veolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30049}, "out_file": "/content/logs/veolfe/out.log", "err_file": "/content/logs/veolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "zaol-test", "script": "dist/server/server.mjs", "cwd": "/content/apps/zaolfe/app", "exec_mode": "cluster", "instances": "2", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30051}, "out_file": "/content/logs/zaolfe/out.log", "err_file": "/content/logs/zaolfe/err.log", "log_type": "json", "time": true, "merge_logs": true}]}