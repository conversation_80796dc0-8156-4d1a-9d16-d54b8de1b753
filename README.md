# Angular Starter

## Tartalom 

1. [Új projekt létrehozása](#new-project)
2. [Buildelés](#build)
   1. [Portál specialitások](#portal-build)
3. [SCSS struktúra](#scss)
4. [<PERSON>rendency Modul](#trendency-module)
   1. [HttpModule](#http)
   2. [TranslationsModule](#translations)
   3. [FormModule](#form)
   4. [LoadingModule](#loading)
   5. [SocialModule](#social)
   6. [UtilsModule](#utils)
5. [Feladatok](#tasks)
   1. [In<PERSON><PERSON><PERSON> el<PERSON>](#before-start)
   2. [Élesí<PERSON>s előtt](#before-release)
      1. [Fejlesztő feladatai](#before-release-developer)
      2. [Az élesítést végző fejlesztő feladatai](#before-release-developer-lead)
   3. [Angular főverzió frissítés esetén](#after-angular-upgrade)




----

## Új projekt létrehozása <a name="new-project"></a>

1. .git mappa törlése, majd _git init_

    Töröld ki a .git mappát, majd futtass egy _git init_-et, hogy ne az **_angular-starter_** git history-ját vidd magaddal.

2. _git remote <NAME_EMAIL>:`<project-elérése>`.git_
3. _git checkout -b develop_
    
    Hozz létre egy develop branchet

4. _node ./init.js `<project-neve>`_ futtatása

5. _git add ._
6. _git commit -m "init"_
7. _git push -u origin develop_

----

## Buildelés <a name="build"></a>

### ecosystem.config.json

### Dev

- .gitlab-ci.yml

### Beta & Prod

- Jenkins

### Portál specialitások <a name="portal-build"></a>

Minden megyének külön buildre van szüksége a saját beállításaival, amiket a `/src/environments/portal-configs/*.ts` file-ok tartalmaznak.

#### Local

Alapesetben dev-re és local-ban a BAMA készül el, de a teszt és éles telepítésekhez egyedileg kell őket buildelni és telepíteni.

Lokális futtatáshoz és fejlesztéshez elég legeneráltatni a portál konfigját:

```sh
$ npm run portal:<portal-kod>
pl.
$ npm run portal:szoljon
```

Ezután a local devserver már a következő load alkalmával a megadott portál konfiggal fog elindulni.

A szokásos utasítások az éppen aktuális konfigokkal dolgoznak, tehát a

```sh
$ npm run build:ssr
```

A fenti `portal:szoljon` váltás után a szoljon.hu -hoz fogja az ssr-t is generálni.

**!!!FONTOS!!!**

Ha portál konfigot váltasz, figyelj rá, hogy NE commitold be az új `/src/environments/portal-config.js` -t!

#### Dev

Deven a bama a default, de ez változtatható a `.gitlab-ci.yml` -ben:

```yaml
variables:
  ...
  PORTAL_DEFAULT: "bama"
```

**!!!FONTOS!!!**

Ha megváltoztatod a `.gitlab-ci.yml` -ben a default-ot, akkor manuálisan kell leállítani az előzőt és törölni a pm2 configjából a dev containerben, majd újraindítani, mert a névváltozás miatt az új buildben már nem fogja látni a script!

#### Beta & prod

```sh
$ PORTAL_NAME=bama npm run build:env:ssr-beta
```

#### Portál konfig

Ahhoz, hogy különböző neveket tudjon használni a pm2 az instance-ekhez (pl. `bama-dev`, `szoljon-beta`, stb.) az `ecosystem.config.json`-t is generálni kell. Erre külön is van lehetőség, de alapvetően a teljes build-nél van csak szükség rá, amivel egy menetben elvégezhető az összes konfigurációs beállítás az adott portálra.

Az ENV alapú npm scriptek a `$PORTAL_NAME` környezeti változót használják, így futtatásuk előtt ezt be kell állítani:

```sh
# vagy külön:
$ export PORTAL_NAME=bama
$ npm run build:env:ssr-beta

# vagy egyben futtatva
$ PORTAL_NAME=bama npm run build:env:ssr-beta
```

**Config generátor scriptek**

* `build:ecosystem`: `$PORTAL_NAME` alapján legenerálja az `ecosystem.config.json` -t
* `portal:env`: `$PORTAL_NAME` alapján meghívha a megfelelő portál config generátor scriptet

**Build scriptek**

Az alábbi scriptek megegyeznek a `:env` nélküli verziójukkal, kivéve, hogy a fenti két utasítást lefuttatják először, tehát:

1. `$PORTAL_NAME` alapján beállítják a portál configot
2. legenerálják az `ecosystem.config.json`-t
3. lefuttatják a megfelelő build utasítást

* `build:env:ssr`
* `build:env:ssr-dev`
* `build:env:ssr-beta`

----

## SCSS struktúra <a name="scss"></a>

### ################### SAMPLES \###################

```scss
// scss mappán belüli import
    @import 'layout/breakpoints';       # SCSS mappán belül ha használni akarsz breakpointokat
```

```scss
// komponensekhez szükséges importok (ha komponensen belül SCSS variable-t vagy mixin-t akarsz használni)
    @import 'shared';                   # variables + mixins + breakpoints
```

### ################### PATTERN \###################

sass/

- abstracts/
  - _variables.scss   #Sass Variables
  - _functions.scss   # Sass Functions
  - _mixins.scss      # Sass Mixins
  - _helpers.scss     # Class & placeholders helpers
- base/
  - _reset.scss       # Reset/normalize
  - _typography.scss  # Typography rules
  - _fonts.scss       # Fonts
  - ...
- components/
  - _buttons.scss     # Buttons
  - _carousel.scss    # Carousel
  - _cover.scss       # Cover
  - _dropdown.scss    # Dropdown
  - ...
- layout/
  - _navigation.scss  # Navigation
  - _grid.scss        # Grid system
  - _header.scss      # Header
  - _footer.scss      # Footer
  - _sidebar.scss     # Sidebar
  - _forms.scss       # Forms
  - ...
- themes/
  - _theme.scss       # Default theme
  - _admin.scss       # Admin theme
  - ...
- vendors/
  - _bootstrap.scss   # Bootstrap
  - _jquery-ui.scss   # jQuery UI
  - ...

----

## Trendency Modul <a name="trendency-module"></a>

### Http <a name="http"></a>
Modul a Http hívások kezeléséhez
#### Használat
1. apiUrl beállítása az environment.ts fájlban
   - apiUrl-nek meg lehet adni stringet, hogy a böngésző és a Node is ugyanazt használja, vagy ha indokolt, meg lehet adni külön a 
   böngésző oldali (apiUrl.clientApiUrl) és Node oldali (apiUrl.serverApiUrl) lekérésekhez is.
   - Az apiUrl-t érdemes minden esetben _http://_ -vel megadni. Ha a weboldal SSL alatt fut, a böngésző oldali lekérések is ahhoz lesznek igazítva és automatikusan _https://_ -el fog történni a lekérés az API felé, Node oldalon viszont marad _http://_
   Pl.
   ```javascript
    apiUrl: 'http://api.domain.hu/publicapi'
    -- vagy --
    apiurl: {
      clientApiUrl: 'http://api.domain.hu/publicapi'
      serverApiUrl: 'http://api.domain.hu/publicapi'
    }
   ```

<a name="http-interceptor"></a>
2. Http Interceptor bekötése
   A Http Interceptorok feladata, hogy beállítsák azokat a HTTP header-öket (pl. nyelvesítéshez: Locale, user token kezeléshez: Authorization) amit minden lekérésnél küldeni kell a backend felé.
   
   Az interceptor bekötéséhez fel kell venni az AppModule providerei közé:

   ```javascript
    providers: [
      {
        provide: HTTP_INTERCEPTORS,
        useClass: HttpInterceptorImp,
        multi: true
      }
    ],
   ```

#### Fontos információk

### Translations <a name="translations"></a>
Modul a fordítások és a route lokalizáció kezeléséhez
#### Használat
1. az environment.ts fájlokban a translation részben meg kell adni az oldalon használt nyelvekhez szükséges adatot

```javascript
translation: {
    locales: ['hu', 'en'], // Az oldalon használt nyelvek listája (default: ['hu'])
    prefix: 'ROUTES.', // A route fordításokhoz használt előtag (pl. ROUTES.home)
    localesFolder: { // A mappa elérési útja, ahol a {lang}.json formátumú szótár fájlok találhatók
      client: '/assets/locales', // böngésző oldali lekéréshez
      server: '/../browser/assers/locales' // Node oldali lekéréshez (a dist/browser mappából)
```
2. az environment.ts-ben megadott _localesFolder_ helyén létre kell hozni a _locales_-ben megadott nyelvek szótár fájljait (pl. hu.json, en.json stb.). Itt kell megadni az egyes route-okhoz tartozó fordítást az adott nyelvhez, illetve, ha nem API-ról jön, akkor a tartalmi szövegek fordításait is.

```javascript
{
  "ROUTES.example": "pelda", // URL fordítás
  "HOME.example": "Példa fordítás" // Oldalon megjelenő fordítás
}
```

3. Az AppModule-ba be kell importálni a **TranslationsRootModule.forRoot(appRoutes)** modult
4. Minden egyéb (nem lazy load-olt) modulban, ha a **TranslateModule** vagy **LocalizeRouterModule** funckióit akarod használni a fordításokhoz, ezt a két package-et importáld
5. Fordítás
   ```javascript
   --- Tartalom ---
   Html: <p>{{ 'EXAMPLE.example' | translate }}</p>
   TS: this.translateService.instant('EXAMPLE.example')
   
   --- Route ---
   Html: <a [routerLink]="['/', 'home'] | localize">Link</a>
   TS: this.localizeService.translateRoute(['/', 'home']) 
   ```
6. Nyelvváltás
   ```javascript
   constructor(private localizeService: LocalizeRouterService) {}

   this.localizeService.changeLanguage(lang);
   ```
7. Aktuális nyelv küldése a backend felé
   Hogy az aktuális nyelvet minden requestben küldjük a backend felé, be kell állítani az interceptort (Lásd: [Http Interceptor bekötése](#http-interceptor))

8. (Opcionális) Fordítások betöltése API-ról
   - Az oldal betöltésekor lefutó resolverben, ahol az API visszaadja a fordításokat, meg kell hívni ezt a függvényt:

   ```javascript
   constructor(private translateService: TranslateService) {}
    /**
    * @param {string} lang: a nyelv, amihez a fordítás tartozik (pl. 'en') 
    * @param {object} translations: a fordításokat tartlamazó objektum (pl. { 'HOME.example': 'example' })
    * @param {boolean} shouldMerge: az új, fordításokat tartalmazó objektumot összemergelje-e a {lang}.json-ből beolvasott fordításokkal
    */
   this.translateService.setTranslation(lang, translations, true);
   ```

#### Fontos információk
1. A modul az _@ngx-translate/core_ és a _localize-router_ package-ekre épül a szövegek és az URL-ek fordításához
2. A **TranslationsRootModule.forRoot(routes)** -ot CSAK az AppModule-ban kell importálni
3. A **TranslationsChildModule.forChild(routes)**-ot CSAK a lazy-load-olt modulokban kell meghívni
4. A RouterModule konfigurációban az _initialNavigation: 'enabled'_ -el NEM MŰKÖDIK a _localize-router_
5. A **TranslationsRootModule** és **TranslationsChildModule** a **RouterModule** UTÁN szerepeljen az importok között

### Form <a name="form"></a>
Modul a formok kezeléséhez és form kezelést segítő funkciók
#### FormControlErrorDirective
Automatikusan kigenerálja a FormControl hibaüzenetét.
##### Használat
```html
<div appFormControlErrorContainer>
  <input type="text" formControlName="name" [error]="{
    required: 'Kötelező mező',
    pattern: 'Hibás formátum'
  }">
</div>
```
- `[error]="errorObject"`: A hibaüzeneteket tartalmazó objektum, ahol a kulcs a `Validators`-ben is használt lehetségek hibák (pl. required, maxlength, pattern stb.), az érték pedig a kiírt szöveg. 
Csak olyan inputokon használható, ahol van megadva `formControlName` vagy `formControl`.
- `appFormControlErrorContainer`: Alapértelmezett esetben a hibaüzenet az input mező mellé generálódik. Ha ez a direktíva meg van adva az input mező egy szülő elemén, akkor a hibaüzenet ez mellé fog generálódni. Nem fontos hogy a közvetlen szülője legyen, csak a HTML struktúrában feljebb helyezkedjen el.

### Loading <a name="loading"></a>
Modul a betöltést jelző spinnerekhez, progress barokhoz, képek és iframe-ek lazy load-olásához
#### Használat/Fontos információk

### Social <a name="social"></a>
Modul közösségi oldalakon (Facebook, Twitter stb.) történő megosztáshoz, OAuth bejelentkezéshez


### Utils <a name="utils"></a>
Modul a munkát megkönnyítő, több helyen felhasznált apróbb funkciókhoz
#### UtilsService
Apróbb, hasznos funckiók

- `isBrowser`: Ha nincs megadva type, visszaadja hogy böngésző oldalon vagyunk-e. Ha van megadva type, akkor pedig hogy az adott böngésző típuson vagyunk-e (pl. Chrome, IE, Safari stb.)
- `log`: Különböző háttérszínt ad a console logoknak, hogy könnyen elkülöníthetők legyenek egymástól a típusuktól függően (info, success, warning, error).
- `convertYouTubeLinkToEmbed`: Átalakítja a hagyományos YouTube videó linket beágyazható formátumúra.
- `showNotification`: Megjelenít egy alert bar-t az oldal tetején az adott szöveggel és stílusban (info, success, warning, error).

#### StorageService
Adatok mentése és lekérése cookie, session storage, local storage-ból

- `setCookie`: Beállít egy _cookie_-t a kapott adatokkal
- `getCookie`: Lekéri a _cookie_-t _key_ alapján
- `setSessionStorageData`: Beállít egy értéket a _sessionStorage_-ban a kapott adatokkal
- `getSessionStorageData`: Lekéri a _sessionStorage_-ban tárolt adatot _key_ alapján
- `setLocalStorageData`: Beállít egy értéket a _localStorage_-ban a kapott adatokkal
- `getLocalStorageData`: Lekéri a _localStorage_-ban tárolt adatot _key_ alapján

#### SeoService
SEO adatok megadása

- `setTitle`: Beállítja az oldalon a _title_-t.
- `setMetaData`: Beállítja az oldalon a különböző meta adatokat (általános, facebook, twitter)

----
## Feladatok <a name="tasks"></a>

### Indulás előtt <a name="before-start"></a>
  1. Chrome és Firefox mellett milyen egyéb böngészőket kell támogatnia (ha lehet, konkrét veriószámokkal): 
  2. Mikor van a határidő?: 
  3. Mi hostoljuk? Melyik szerverre lesz telepítve?:
  4. Lesz govcert vizsgálat?:

### Élesítés előtt <a name="before-release"></a>

#### Fejlesztő feladatai <a name="before-release-developer"></a>
Általános:
 1. [ ] Van title
 2. [ ] Van favicon
 3. [ ] Vannak metaadatok minden oldalon (ha adott oldalhoz nincs metaadat, akkor default használata)
  ([Utils](#utils) -> SeoService -> setMetaData)
 4. [ ] Van robots.txt és ki is van töltve az éles domain címével
 5. [ ] Van sitemap.xml (ha vannak dinamikus route-ok akkor BE geneálja, egyébként FE)
 6. [ ] Van 404-es oldal
 7. [ ] Van wildcard route az app.routing-ban, ami a 404-es oldalra irányít
 8. [ ] Ha van path: ':slug' route, az app.routing-ban, az a route-ok végén, de még a wildcard route előtt szerepel
 9. [ ] A resolvereken van hibakezelés (pl. hiba esetén 404-re irányít)
 10. [ ] Az alkalmazás betölt és normálisan fut is különböző böngészőken:
      1. [ ] Chrome
      2. [ ] Firefox
      3. [ ] Internet Explorer 11
1.  [ ] Minden observable le van iratkoztatva OnDestroy-ra <a href="https://gitlab.trendency.hu/frontend/angular-starter/-/snippets/7" target="_blank">példa>></a>

SSR:
1. [ ] Az alkalmazás lebuildelődik ÉS normálisan működik is "npm run start:ssr-dev" paranccsal
2. [ ] Az alkalmazás lebuildelődik "npm run build:ssr" paranccsal

#### Az élesítést végző fejlesztő feladatai <a name="before-release-developer-lead"></a>

1. [ ] Információk begyűjtése:
   1. melyik szerverre lesz kitelepítve?: 
   2. Hanyas Node verzió fut rajta?: 
2. [ ] Jenkins job létrehozása az "f-angular-ssr-template"-ből
3. [ ] environment.prod.ts bekonfigurálása (apiUrl, ha van, googleApiKey, facebookApiKey)
4. [ ] ecosystem.config.json-ben port szám átírása
  
### Angular főverzió frissítés esetén <a name="after-angular-upgrade"></a>

1. `package.json`-ben `engines.node` és `engines.npm` verzió ellenőrzése, hogy megfelelő legyen az új angular verzióhoz (LTS preferált)
2. `.nvmrc`-ben a default node verzió ellenőrzése (LTS preferált)
