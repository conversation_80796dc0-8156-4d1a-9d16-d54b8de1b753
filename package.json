{"name": "megyei-lapok", "version": "3.14.0", "license": "proprietary", "repository": {"type": "git", "url": "https://gitlab.trendency.hu/kozponti-cms/megyei-lapok"}, "contributors": [], "scripts": {"ng": "npx ng", "start": "npx ng serve", "start-with-proxy": "ng serve --proxy-config proxy.conf.js", "build": "ng build", "watch": "ng build --watch --configuration development", "watch:local": "ng build --watch --configuration local-ssr", "serve:ssr": "node dist/server/server.mjs", "lint": "npx eslint \"src/**/*.{js,ts,html}\" --quiet --fix", "format": "npx prettier --config ./.prettierrc \"src/**/*.{js,ts,html,scss}\" --write", "copy-env": "mkdir -p dist/server/environments && cp src/environments/portal-config.js dist/server/environments/ && cp src/environments/portal-config.json dist/server/environments/", "config:convert": "node ./convert-config.js ./src/environments/portal-config.js", "build:ssr-local": "ng build megyei-lapok --configuration local-ssr", "build:ssr-dev": "ng build megyei-lapok --configuration development && npm run copy-env", "build:ssr-teszt": "ng build megyei-lapok --configuration test && npm run copy-env", "build:ssr-prod": "ng build megyei-lapok --configuration production && npm run copy-env", "portal:feol-dev": "tsc src/environments/portal-configs-dev/feol.ts && mv src/environments/portal-configs-dev/feol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:feol && npm run robotstxt:feol", "portal:veol-dev": "tsc src/environments/portal-configs-dev/veol.ts && mv src/environments/portal-configs-dev/veol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:veol && npm run robotstxt:veol", "portal:erdon-dev": "tsc src/environments/portal-configs-dev/erdon.ts && mv src/environments/portal-configs-dev/erdon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:erdon && npm run robotstxt:erdon", "portal:bama-teszt": "tsc src/environments/portal-configs-teszt/bama.ts && mv src/environments/portal-configs-teszt/bama.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:bama && npm run robotstxt:bama", "portal:baon-teszt": "tsc src/environments/portal-configs-teszt/baon.ts && mv src/environments/portal-configs-teszt/baon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:baon && npm run robotstxt:baon", "portal:beol-teszt": "tsc src/environments/portal-configs-teszt/beol.ts && mv src/environments/portal-configs-teszt/beol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:beol && npm run robotstxt:beol", "portal:boon-teszt": "tsc src/environments/portal-configs-teszt/boon.ts && mv src/environments/portal-configs-teszt/boon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:boon && npm run robotstxt:boon", "portal:delmagyar-teszt": "tsc src/environments/portal-configs-teszt/delmagyar.ts && mv src/environments/portal-configs-teszt/delmagyar.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:delmagyar && npm run robotstxt:delmagyar", "portal:duol-teszt": "tsc src/environments/portal-configs-teszt/duol.ts && mv src/environments/portal-configs-teszt/duol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:duol && npm run robotstxt:duol", "portal:feol-teszt": "tsc src/environments/portal-configs-teszt/feol.ts && mv src/environments/portal-configs-teszt/feol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:feol && npm run robotstxt:feol", "portal:haon-teszt": "tsc src/environments/portal-configs-teszt/haon.ts && mv src/environments/portal-configs-teszt/haon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:haon && npm run robotstxt:haon", "portal:heol-teszt": "tsc src/environments/portal-configs-teszt/heol.ts && mv src/environments/portal-configs-teszt/heol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:heol && npm run robotstxt:heol", "portal:kemma-teszt": "tsc src/environments/portal-configs-teszt/kemma.ts && mv src/environments/portal-configs-teszt/kemma.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:kemma && npm run robotstxt:kemma", "portal:kisalfold-teszt": "tsc src/environments/portal-configs-teszt/kisalfold.ts && mv src/environments/portal-configs-teszt/kisalfold.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:kisalfold && npm run robotstxt:kisalfold", "portal:nool-teszt": "tsc src/environments/portal-configs-teszt/nool.ts && mv src/environments/portal-configs-teszt/nool.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:nool && npm run robotstxt:nool", "portal:sonline-teszt": "tsc src/environments/portal-configs-teszt/sonline.ts && mv src/environments/portal-configs-teszt/sonline.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:sonline && npm run robotstxt:sonline", "portal:szegedma-teszt": "tsc src/environments/portal-configs-teszt/szegedma.ts && mv src/environments/portal-configs-teszt/szegedma.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:szegedma && npm run robotstxt:szegedma", "portal:szoljon-teszt": "tsc src/environments/portal-configs-teszt/szoljon.ts && mv src/environments/portal-configs-teszt/szoljon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:szoljon && npm run robotstxt:szoljon", "portal:szon-teszt": "tsc src/environments/portal-configs-teszt/szon.ts && mv src/environments/portal-configs-teszt/szon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:szon && npm run robotstxt:szon", "portal:teol-teszt": "tsc src/environments/portal-configs-teszt/teol.ts && mv src/environments/portal-configs-teszt/teol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:teol && npm run robotstxt:teol", "portal:vaol-teszt": "tsc src/environments/portal-configs-teszt/vaol.ts && mv src/environments/portal-configs-teszt/vaol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:vaol && npm run robotstxt:vaol", "portal:veol-teszt": "tsc src/environments/portal-configs-teszt/veol.ts && mv src/environments/portal-configs-teszt/veol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:veol && npm run robotstxt:veol", "portal:zaol-teszt": "tsc src/environments/portal-configs-teszt/zaol.ts && mv src/environments/portal-configs-teszt/zaol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:zaol && npm run robotstxt:zaol", "portal:kpi-teszt": "tsc src/environments/portal-configs-teszt/kpi.ts && mv src/environments/portal-configs-teszt/kpi.js src/environments/portal-config.js && npm run config:convert", "portal:erdon-teszt": "tsc src/environments/portal-configs-teszt/erdon.ts && mv src/environments/portal-configs-teszt/erdon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:erdon && npm run robotstxt:erdon", "portal:bama-prod": "tsc src/environments/portal-configs-prod/bama.ts && mv src/environments/portal-configs-prod/bama.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:bama && npm run robotstxt:bama", "portal:baon-prod": "tsc src/environments/portal-configs-prod/baon.ts && mv src/environments/portal-configs-prod/baon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:baon && npm run robotstxt:baon", "portal:beol-prod": "tsc src/environments/portal-configs-prod/beol.ts && mv src/environments/portal-configs-prod/beol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:beol && npm run robotstxt:beol", "portal:boon-prod": "tsc src/environments/portal-configs-prod/boon.ts && mv src/environments/portal-configs-prod/boon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:boon && npm run robotstxt:boon", "portal:delmagyar-prod": "tsc src/environments/portal-configs-prod/delmagyar.ts && mv src/environments/portal-configs-prod/delmagyar.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:delmagyar && npm run robotstxt:delmagyar", "portal:duol-prod": "tsc src/environments/portal-configs-prod/duol.ts && mv src/environments/portal-configs-prod/duol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:duol && npm run robotstxt:duol", "portal:feol-prod": "tsc src/environments/portal-configs-prod/feol.ts && mv src/environments/portal-configs-prod/feol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:feol && npm run robotstxt:feol", "portal:haon-prod": "tsc src/environments/portal-configs-prod/haon.ts && mv src/environments/portal-configs-prod/haon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:haon && npm run robotstxt:haon", "portal:heol-prod": "tsc src/environments/portal-configs-prod/heol.ts && mv src/environments/portal-configs-prod/heol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:heol && npm run robotstxt:heol", "portal:kemma-prod": "tsc src/environments/portal-configs-prod/kemma.ts && mv src/environments/portal-configs-prod/kemma.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:kemma && npm run robotstxt:kemma", "portal:kisalfold-prod": "tsc src/environments/portal-configs-prod/kisalfold.ts && mv src/environments/portal-configs-prod/kisalfold.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:kisalfold && npm run robotstxt:kisalfold", "portal:nool-prod": "tsc src/environments/portal-configs-prod/nool.ts && mv src/environments/portal-configs-prod/nool.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:nool && npm run robotstxt:nool", "portal:sonline-prod": "tsc src/environments/portal-configs-prod/sonline.ts && mv src/environments/portal-configs-prod/sonline.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:sonline && npm run robotstxt:sonline", "portal:szegedma-prod": "tsc src/environments/portal-configs-prod/szegedma.ts && mv src/environments/portal-configs-prod/szegedma.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:szegedma && npm run robotstxt:szegedma", "portal:szoljon-prod": "tsc src/environments/portal-configs-prod/szoljon.ts && mv src/environments/portal-configs-prod/szoljon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:szoljon && npm run robotstxt:szoljon", "portal:szon-prod": "tsc src/environments/portal-configs-prod/szon.ts && mv src/environments/portal-configs-prod/szon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:szon && npm run robotstxt:szon", "portal:teol-prod": "tsc src/environments/portal-configs-prod/teol.ts && mv src/environments/portal-configs-prod/teol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:teol && npm run robotstxt:teol", "portal:vaol-prod": "tsc src/environments/portal-configs-prod/vaol.ts && mv src/environments/portal-configs-prod/vaol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:vaol && npm run robotstxt:vaol", "portal:veol-prod": "tsc src/environments/portal-configs-prod/veol.ts && mv src/environments/portal-configs-prod/veol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:veol && npm run robotstxt:veol", "portal:zaol-prod": "tsc src/environments/portal-configs-prod/zaol.ts && mv src/environments/portal-configs-prod/zaol.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:zaol && npm run robotstxt:zaol", "portal:kpi-prod": "tsc src/environments/portal-configs-prod/kpi.ts && mv src/environments/portal-configs-prod/kpi.js src/environments/portal-config.js && npm run config:convert", "portal:erdon-prod": "tsc src/environments/portal-configs-prod/erdon.ts && mv src/environments/portal-configs-prod/erdon.js src/environments/portal-config.js && npm run config:convert && npm run adstxt:erdon && npm run robotstxt:erdon", "adstxt:bama": "cp src/environments/ads/ads-bama.txt src/ads.txt", "adstxt:baon": "cp src/environments/ads/ads-baon.txt src/ads.txt", "adstxt:beol": "cp src/environments/ads/ads-beol.txt src/ads.txt", "adstxt:boon": "cp src/environments/ads/ads-boon.txt src/ads.txt", "adstxt:delmagyar": "cp src/environments/ads/ads-delmagyar.txt src/ads.txt", "adstxt:duol": "cp src/environments/ads/ads-duol.txt src/ads.txt", "adstxt:feol": "cp src/environments/ads/ads-feol.txt src/ads.txt", "adstxt:haon": "cp src/environments/ads/ads-haon.txt src/ads.txt", "adstxt:heol": "cp src/environments/ads/ads-heol.txt src/ads.txt", "adstxt:kemma": "cp src/environments/ads/ads-kemma.txt src/ads.txt", "adstxt:kisalfold": "cp src/environments/ads/ads-kisalfold.txt src/ads.txt", "adstxt:nool": "cp src/environments/ads/ads-nool.txt src/ads.txt", "adstxt:sonline": "cp src/environments/ads/ads-sonline.txt src/ads.txt", "adstxt:szegedma": "cp src/environments/ads/ads-szegedma.txt src/ads.txt", "adstxt:szoljon": "cp src/environments/ads/ads-szoljon.txt src/ads.txt", "adstxt:szon": "cp src/environments/ads/ads-szon.txt src/ads.txt", "adstxt:teol": "cp src/environments/ads/ads-teol.txt src/ads.txt", "adstxt:vaol": "cp src/environments/ads/ads-vaol.txt src/ads.txt", "adstxt:veol": "cp src/environments/ads/ads-veol.txt src/ads.txt", "adstxt:zaol": "cp src/environments/ads/ads-zaol.txt src/ads.txt", "adstxt:erdon": "cp src/environments/ads/ads-erdon.txt src/ads.txt", "robotstxt:bama": "cp src/environments/robots/robots-bama.txt src/robots.txt", "robotstxt:baon": "cp src/environments/robots/robots-baon.txt src/robots.txt", "robotstxt:beol": "cp src/environments/robots/robots-beol.txt src/robots.txt", "robotstxt:boon": "cp src/environments/robots/robots-boon.txt src/robots.txt", "robotstxt:delmagyar": "cp src/environments/robots/robots-delmagyar.txt src/robots.txt", "robotstxt:duol": "cp src/environments/robots/robots-duol.txt src/robots.txt", "robotstxt:feol": "cp src/environments/robots/robots-feol.txt src/robots.txt", "robotstxt:haon": "cp src/environments/robots/robots-haon.txt src/robots.txt", "robotstxt:heol": "cp src/environments/robots/robots-heol.txt src/robots.txt", "robotstxt:kemma": "cp src/environments/robots/robots-kemma.txt src/robots.txt", "robotstxt:kisalfold": "cp src/environments/robots/robots-kisalfold.txt src/robots.txt", "robotstxt:nool": "cp src/environments/robots/robots-nool.txt src/robots.txt", "robotstxt:sonline": "cp src/environments/robots/robots-sonline.txt src/robots.txt", "robotstxt:szegedma": "cp src/environments/robots/robots-szegedma.txt src/robots.txt", "robotstxt:szoljon": "cp src/environments/robots/robots-szoljon.txt src/robots.txt", "robotstxt:szon": "cp src/environments/robots/robots-szon.txt src/robots.txt", "robotstxt:teol": "cp src/environments/robots/robots-teol.txt src/robots.txt", "robotstxt:vaol": "cp src/environments/robots/robots-vaol.txt src/robots.txt", "robotstxt:veol": "cp src/environments/robots/robots-veol.txt src/robots.txt", "robotstxt:zaol": "cp src/environments/robots/robots-zaol.txt src/robots.txt", "robotstxt:erdon": "cp src/environments/robots/robots-erdon.txt src/robots.txt", "favicon:bama": "cp src/assets/images/favicons/bama.ico dist/browser/favicon.ico", "favicon:baon": "cp src/assets/images/favicons/baon.ico dist/browser/favicon.ico", "favicon:beol": "cp src/assets/images/favicons/beol.ico dist/browser/favicon.ico", "favicon:boon": "cp src/assets/images/favicons/boon.ico dist/browser/favicon.ico", "favicon:delmagyar": "cp src/assets/images/favicons/delmagyar.ico dist/browser/favicon.ico", "favicon:duol": "cp src/assets/images/favicons/duol.ico dist/browser/favicon.ico", "favicon:feol": "cp src/assets/images/favicons/feol.ico dist/browser/favicon.ico", "favicon:haon": "cp src/assets/images/favicons/haon.ico dist/browser/favicon.ico", "favicon:heol": "cp src/assets/images/favicons/heol.ico dist/browser/favicon.ico", "favicon:kemma": "cp src/assets/images/favicons/kemma.ico dist/browser/favicon.ico", "favicon:kisalfold": "cp src/assets/images/favicons/kisalfold.ico dist/browser/favicon.ico", "favicon:nool": "cp src/assets/images/favicons/nool.ico dist/browser/favicon.ico", "favicon:sonline": "cp src/assets/images/favicons/sonline.ico dist/browser/favicon.ico", "favicon:szegedma": "cp src/assets/images/favicons/szegedma.ico dist/browser/favicon.ico", "favicon:szoljon": "cp src/assets/images/favicons/szoljon.ico dist/browser/favicon.ico", "favicon:szon": "cp src/assets/images/favicons/szon.ico dist/browser/favicon.ico", "favicon:teol": "cp src/assets/images/favicons/teol.ico dist/browser/favicon.ico", "favicon:vaol": "cp src/assets/images/favicons/vaol.ico dist/browser/favicon.ico", "favicon:veol": "cp src/assets/images/favicons/veol.ico dist/browser/favicon.ico", "favicon:zaol": "cp src/assets/images/favicons/zaol.ico dist/browser/favicon.ico", "favicon:erdon": "cp src/assets/images/favicons/erdon.ico dist/browser/favicon.ico", "build:ssr-dev:feol": "npm run portal:feol-dev && npm run build:ssr-dev && npm run favicon:feol", "build:ssr-dev:veol": "npm run portal:veol-dev && npm run build:ssr-dev && npm run favicon:veol", "build:ssr-dev-php81:feol": "npm run build:ssr-dev:feol", "build:ssr-dev-php81:veol": "npm run build:ssr-dev:veol", "build:ssr-teszt:bama": "npm run portal:bama-teszt && npm run build:ssr-teszt && npm run favicon:bama", "build:ssr-teszt:baon": "npm run portal:baon-teszt && npm run build:ssr-teszt && npm run favicon:baon", "build:ssr-teszt:beol": "npm run portal:beol-teszt && npm run build:ssr-teszt && npm run favicon:beol", "build:ssr-teszt:boon": "npm run portal:boon-teszt && npm run build:ssr-teszt && npm run favicon:boon", "build:ssr-teszt:delmagyar": "npm run portal:delmagyar-teszt && npm run build:ssr-teszt && npm run favicon:delmagyar", "build:ssr-teszt:duol": "npm run portal:duol-teszt && npm run build:ssr-teszt && npm run favicon:duol", "build:ssr-teszt:feol": "npm run portal:feol-teszt && npm run build:ssr-teszt && npm run favicon:feol", "build:ssr-teszt:haon": "npm run portal:haon-teszt && npm run build:ssr-teszt && npm run favicon:haon", "build:ssr-teszt:heol": "npm run portal:heol-teszt && npm run build:ssr-teszt && npm run favicon:heol", "build:ssr-teszt:kemma": "npm run portal:kemma-teszt && npm run build:ssr-teszt && npm run favicon:kemma", "build:ssr-teszt:kisalfold": "npm run portal:kisalfold-teszt && npm run build:ssr-teszt && npm run favicon:kisalfold", "build:ssr-teszt:nool": "npm run portal:nool-teszt && npm run build:ssr-teszt && npm run favicon:nool", "build:ssr-teszt:sonline": "npm run portal:sonline-teszt && npm run build:ssr-teszt && npm run favicon:sonline", "build:ssr-teszt:szegedma": "npm run portal:szegedma-teszt && npm run build:ssr-teszt && npm run favicon:szegedma", "build:ssr-teszt:szoljon": "npm run portal:szoljon-teszt && npm run build:ssr-teszt && npm run favicon:szoljon", "build:ssr-teszt:szon": "npm run portal:szon-teszt && npm run build:ssr-teszt && npm run favicon:szon", "build:ssr-teszt:teol": "npm run portal:teol-teszt && npm run build:ssr-teszt && npm run favicon:teol", "build:ssr-teszt:vaol": "npm run portal:vaol-teszt && npm run build:ssr-teszt && npm run favicon:vaol", "build:ssr-teszt:veol": "npm run portal:veol-teszt && npm run build:ssr-teszt && npm run favicon:veol", "build:ssr-teszt:zaol": "npm run portal:zaol-teszt && npm run build:ssr-teszt && npm run favicon:zaol", "build:ssr-teszt:erdon": "npm run portal:erdon-teszt && npm run build:ssr-teszt && npm run favicon:erdon", "build:ssr-teszt:kpi": "npm run portal:kpi-teszt && npm run build:ssr-teszt", "build:ssr-prod:bama": "npm run portal:bama-prod && npm run build:ssr-prod && npm run favicon:bama", "build:ssr-prod:baon": "npm run portal:baon-prod && npm run build:ssr-prod && npm run favicon:baon", "build:ssr-prod:beol": "npm run portal:beol-prod && npm run build:ssr-prod && npm run favicon:beol", "build:ssr-prod:boon": "npm run portal:boon-prod && npm run build:ssr-prod && npm run favicon:boon", "build:ssr-prod:delmagyar": "npm run portal:delmagyar-prod && npm run build:ssr-prod && npm run favicon:delmagyar", "build:ssr-prod:duol": "npm run portal:duol-prod && npm run build:ssr-prod && npm run favicon:duol", "build:ssr-prod:feol": "npm run portal:feol-prod && npm run build:ssr-prod && npm run favicon:feol", "build:ssr-prod:haon": "npm run portal:haon-prod && npm run build:ssr-prod && npm run favicon:haon", "build:ssr-prod:heol": "npm run portal:heol-prod && npm run build:ssr-prod && npm run favicon:heol", "build:ssr-prod:kemma": "npm run portal:kemma-prod && npm run build:ssr-prod && npm run favicon:kemma", "build:ssr-prod:kisalfold": "npm run portal:kisalfold-prod && npm run build:ssr-prod && npm run favicon:kisalfold", "build:ssr-prod:nool": "npm run portal:nool-prod && npm run build:ssr-prod && npm run favicon:nool", "build:ssr-prod:sonline": "npm run portal:sonline-prod && npm run build:ssr-prod && npm run favicon:sonline", "build:ssr-prod:szegedma": "npm run portal:szegedma-prod && npm run build:ssr-prod && npm run favicon:szgedma", "build:ssr-prod:szoljon": "npm run portal:szoljon-prod && npm run build:ssr-prod && npm run favicon:szoljon", "build:ssr-prod:szon": "npm run portal:szon-prod && npm run build:ssr-prod && npm run favicon:szon", "build:ssr-prod:teol": "npm run portal:teol-prod && npm run build:ssr-prod && npm run favicon:teol", "build:ssr-prod:vaol": "npm run portal:vaol-prod && npm run build:ssr-prod && npm run favicon:vaol", "build:ssr-prod:veol": "npm run portal:veol-prod && npm run build:ssr-prod && npm run favicon:veol", "build:ssr-prod:zaol": "npm run portal:zaol-prod && npm run build:ssr-prod && npm run favicon:zaol", "build:ssr-prod:erdon": "npm run portal:erdon-prod && npm run build:ssr-prod && npm run favicon:erdon", "build:ssr-prod:kpi": "npm run portal:kpi-prod && npm run build:ssr-teszt", "prepare": "husky"}, "private": true, "dependencies": {"@angular/animations": "^19.2.3", "@angular/cdk": "^19.2.3", "@angular/common": "^19.2.3", "@angular/compiler": "^19.2.3", "@angular/core": "^19.2.3", "@angular/forms": "^19.2.3", "@angular/platform-browser": "^19.2.3", "@angular/platform-browser-dynamic": "^19.2.3", "@angular/platform-server": "^19.2.3", "@angular/router": "^19.2.3", "@angular/ssr": "^19.2.4", "@ng-select/ng-select": "^14.2.3", "@trendency/kesma-core": "3.3.1", "@trendency/kesma-ui": "3.33.2", "angular-google-tag-manager": "^1.11.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "express": "^4.18.2", "flatpickr": "^4.6.13", "http-proxy-middleware": "^3.0.3", "https-proxy-agent": "^7.0.6", "lodash-es": "^4.17.21", "ngx-captcha": "^13.0.0", "ngx-date-fns": "^12.0.0", "ngx-file-drop": "^16.0.0", "rxjs": "^7.8.2", "swiper": "^11.2.5", "tslib": "^2.8.1", "zone.js": "~0.15.0", "chart.js": "^4.4.9"}, "devDependencies": {"@angular-eslint/builder": "^19.2.1", "@angular-eslint/eslint-plugin": "^19.2.1", "@angular-eslint/eslint-plugin-template": "^19.2.1", "@angular-eslint/schematics": "^19.2.1", "@angular-eslint/template-parser": "^19.2.1", "@angular/build": "^19.2.4", "@angular/cli": "^19.2.4", "@angular/compiler-cli": "^19.2.3", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/express": "^4.17.17", "@types/google.analytics": "^0.0.46", "@types/google.maps": "^3.58.1", "@types/jquery": "^3.5.32", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "@types/slick-carousel": "^1.6.40", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "bootstrap": "^5.3.3", "browser-sync": "^3.0.3", "check-engine": "^1.14.0", "eslint": "^8.57.0", "eslint-plugin-functional": "^6.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-rxjs": "^5.0.2", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "typescript": "~5.5.4"}, "browser": {"fs": false}, "engines": {"node": "^18.13.0 || ^20.9.0", "npm": ">=9.0.0"}}