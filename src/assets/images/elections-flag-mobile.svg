<svg width="142" height="100" viewBox="0 0 142 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1394_20412)">
<g filter="url(#filter0_d_1394_20412)">
<path d="M159.588 -17C-35.787 200.842 -108 64.2755 -108 64.2755V87.404C-101.621 112.946 -68.6319 127 -68.6319 127H-3.06643C51.5857 106.752 134.673 12.7332 160 -17H159.588Z" fill="#CC2A3D"/>
</g>
<g filter="url(#filter1_d_1394_20412)">
<path d="M156.357 -11.4331C157.569 -13.2673 158.781 -15.1256 160 -17H159.19C133.688 12.7332 50.0285 106.752 -5 127H26.4308C62.5719 106.156 106.078 64.7341 156.357 -11.4331Z" fill="#CC2A3D"/>
</g>
<g filter="url(#filter2_d_1394_20412)">
<path d="M158.305 -12.6045C159.866 -15.1305 161.118 -17.2865 162 -19H161.164C159.86 -17.2865 158.431 -15.4282 156.883 -13.4331C106.621 62.7341 63.1291 104.156 27 125H59.0723C103.868 78.4775 140.728 18.3032 158.305 -12.6045Z" fill="white"/>
</g>
<g filter="url(#filter3_d_1394_20412)">
<path d="M161.961 -18.7345C161.967 -18.7908 161.98 -18.8552 161.987 -18.9115V-18.9356C161.987 -18.9356 161.993 -18.9759 162 -19H160.75C159.878 -17.2865 158.641 -15.1305 157.098 -12.6045C139.722 18.3032 103.283 78.4775 59 125H91.8902C115.77 89.5792 151.655 7.63598 161.323 -14.8087C161.538 -16.12 161.746 -17.4152 161.961 -18.7345Z" fill="white"/>
</g>
<g filter="url(#filter4_d_1394_20412)">
<path d="M91 125H131.733C134.464 114.503 149.205 56.5266 161 -15C151.253 7.4754 115.075 89.5308 91 125Z" fill="#3B6345"/>
</g>
<path d="M160.316 -15.4164C148.502 56.3229 133.735 114.472 131 125H161V-17C160.822 -16.5879 160.632 -16.1516 160.428 -15.683C160.389 -15.5941 160.356 -15.5053 160.316 -15.4164Z" fill="#3B6345"/>
</g>
<defs>
<filter id="filter0_d_1394_20412" x="-124" y="-25" width="300" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1394_20412"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1394_20412" result="shape"/>
</filter>
<filter id="filter1_d_1394_20412" x="-21" y="-25" width="197" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1394_20412"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1394_20412" result="shape"/>
</filter>
<filter id="filter2_d_1394_20412" x="11" y="-27" width="167" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1394_20412"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1394_20412" result="shape"/>
</filter>
<filter id="filter3_d_1394_20412" x="43" y="-27" width="135" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1394_20412"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1394_20412" result="shape"/>
</filter>
<filter id="filter4_d_1394_20412" x="75" y="-23" width="102" height="172" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1394_20412"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1394_20412" result="shape"/>
</filter>
<clipPath id="clip0_1394_20412">
<rect width="142" height="100" fill="white"/>
</clipPath>
</defs>
</svg>
