<svg fill='none' height='250' viewBox='0 0 970 250' width='970' xmlns='http://www.w3.org/2000/svg'>
  <g clip-path='url(#clip0_2465_6582)'>
    <g filter='url(#filter0_d_2465_6582)'>
      <path d='M734 287H885.295C895.436 261.658 950.191 121.686 994 -51C957.797 3.26204 823.421 201.367 734 287Z' fill='#3B6345' />
    </g>
    <g filter='url(#filter1_d_2465_6582)'>
      <path
        d='M982.471 -61C257.835 465.452 -10 135.416 -10 135.416V191.31C13.6603 253.036 136.014 287 136.014 287H379.194C581.896 238.066 890.062 10.8552 984 -61H982.471Z'
        fill='#CC2A3D' />
    </g>
    <path
      d='M993.47 -51.1859C949.756 121.595 895.12 261.644 885 287H996V-55C995.343 -54.0076 994.638 -52.9568 993.884 -51.8281C993.738 -51.6141 993.616 -51.4 993.47 -51.1859Z'
      fill='#3B6345' />
    <g filter='url(#filter2_d_2465_6582)'>
      <path
        d='M979.344 -45.5441C985.113 -51.6487 989.738 -56.859 993 -61H989.909C985.089 -56.859 979.806 -52.368 974.086 -47.5466C788.303 136.524 627.544 236.628 494 287H612.549C778.127 174.571 914.373 29.1495 979.344 -45.5441Z'
        fill='white' />
    </g>
    <g filter='url(#filter3_d_2465_6582)'>
      <path
        d='M973.576 -47.5466C978.042 -51.9792 982.509 -56.4702 987 -61H984.014C890.044 10.8552 581.772 238.066 379 287H494.818C627.992 236.628 788.306 136.524 973.576 -47.5466Z'
        fill='#CC2A3D' />
    </g>
    <g filter='url(#filter4_d_2465_6582)'>
      <path
        d='M995.854 -60.3584C995.879 -60.4945 995.927 -60.6501 995.951 -60.7861V-60.8445C995.951 -60.8445 995.976 -60.9417 996 -61H991.34C988.087 -56.859 983.476 -51.6487 977.724 -45.5441C912.943 29.1495 777.095 174.571 612 287H734.62C823.648 201.4 957.432 3.37028 993.476 -50.8711C994.277 -54.04 995.053 -57.1701 995.854 -60.3584Z'
        fill='white' />
    </g>
    <path d='M-10 191V287H136C136 287 13.658 252.926 -10 191Z' fill='#CC2A3D' />
  </g>
  <defs>
    <filter color-interpolation-filters='sRGB' filterUnits='userSpaceOnUse' height='370' id='filter0_d_2465_6582' width='292' x='718'
            y='-59'>
      <feFlood flood-opacity='0' result='BackgroundImageFix' />
      <feColorMatrix in='SourceAlpha' result='hardAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
      <feOffset dy='8' />
      <feGaussianBlur stdDeviation='8' />
      <feComposite in2='hardAlpha' operator='out' />
      <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0' />
      <feBlend in2='BackgroundImageFix' mode='normal' result='effect1_dropShadow_2465_6582' />
      <feBlend in='SourceGraphic' in2='effect1_dropShadow_2465_6582' mode='normal' result='shape' />
    </filter>
    <filter color-interpolation-filters='sRGB' filterUnits='userSpaceOnUse' height='380' id='filter1_d_2465_6582' width='1026' x='-26'
            y='-69'>
      <feFlood flood-opacity='0' result='BackgroundImageFix' />
      <feColorMatrix in='SourceAlpha' result='hardAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
      <feOffset dy='8' />
      <feGaussianBlur stdDeviation='8' />
      <feComposite in2='hardAlpha' operator='out' />
      <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0' />
      <feBlend in2='BackgroundImageFix' mode='normal' result='effect1_dropShadow_2465_6582' />
      <feBlend in='SourceGraphic' in2='effect1_dropShadow_2465_6582' mode='normal' result='shape' />
    </filter>
    <filter color-interpolation-filters='sRGB' filterUnits='userSpaceOnUse' height='380' id='filter2_d_2465_6582' width='531' x='478'
            y='-69'>
      <feFlood flood-opacity='0' result='BackgroundImageFix' />
      <feColorMatrix in='SourceAlpha' result='hardAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
      <feOffset dy='8' />
      <feGaussianBlur stdDeviation='8' />
      <feComposite in2='hardAlpha' operator='out' />
      <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0' />
      <feBlend in2='BackgroundImageFix' mode='normal' result='effect1_dropShadow_2465_6582' />
      <feBlend in='SourceGraphic' in2='effect1_dropShadow_2465_6582' mode='normal' result='shape' />
    </filter>
    <filter color-interpolation-filters='sRGB' filterUnits='userSpaceOnUse' height='380' id='filter3_d_2465_6582' width='640' x='363'
            y='-69'>
      <feFlood flood-opacity='0' result='BackgroundImageFix' />
      <feColorMatrix in='SourceAlpha' result='hardAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
      <feOffset dy='8' />
      <feGaussianBlur stdDeviation='8' />
      <feComposite in2='hardAlpha' operator='out' />
      <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0' />
      <feBlend in2='BackgroundImageFix' mode='normal' result='effect1_dropShadow_2465_6582' />
      <feBlend in='SourceGraphic' in2='effect1_dropShadow_2465_6582' mode='normal' result='shape' />
    </filter>
    <filter color-interpolation-filters='sRGB' filterUnits='userSpaceOnUse' height='380' id='filter4_d_2465_6582' width='416' x='596'
            y='-69'>
      <feFlood flood-opacity='0' result='BackgroundImageFix' />
      <feColorMatrix in='SourceAlpha' result='hardAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
      <feOffset dy='8' />
      <feGaussianBlur stdDeviation='8' />
      <feComposite in2='hardAlpha' operator='out' />
      <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0' />
      <feBlend in2='BackgroundImageFix' mode='normal' result='effect1_dropShadow_2465_6582' />
      <feBlend in='SourceGraphic' in2='effect1_dropShadow_2465_6582' mode='normal' result='shape' />
    </filter>
    <clipPath id='clip0_2465_6582'>
      <rect fill='white' height='250' width='970' />
    </clipPath>
  </defs>
</svg>
