<svg width="439" height="100" viewBox="0 0 439 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1392_20330)">
<g filter="url(#filter0_d_1392_20330)">
<path d="M464.005 -198C-7.66487 328.452 -182 -1.58414 -182 -1.58414V54.3097C-166.599 116.036 -86.9584 150 -86.9584 150H71.3284C203.268 101.066 403.856 -126.145 465 -198H464.005Z" fill="#CC2A3D"/>
</g>
<g filter="url(#filter1_d_1392_20330)">
<path d="M446.078 -186.544C449.847 -192.649 452.869 -197.859 455 -202H452.98C449.831 -197.859 446.38 -193.368 442.643 -188.547C321.27 -4.47597 216.245 95.6275 129 146H206.449C314.622 33.5707 403.633 -111.851 446.078 -186.544Z" fill="white"/>
</g>
<g filter="url(#filter2_d_1392_20330)">
<path d="M285 146H383.342C389.934 120.658 425.524 -19.3145 454 -192C430.468 -137.738 343.124 60.3671 285 146Z" fill="#3B6345"/>
</g>
<g filter="url(#filter3_d_1392_20330)">
<path d="M455.905 -201.358C455.921 -201.495 455.953 -201.65 455.968 -201.786V-201.844C455.968 -201.844 455.984 -201.942 456 -202H452.966C450.849 -197.859 447.846 -192.649 444.101 -186.544C401.926 -111.851 313.484 33.5707 206 146H285.831C343.792 60.3998 430.891 -137.63 454.357 -191.871C454.878 -195.04 455.384 -198.17 455.905 -201.358Z" fill="white"/>
</g>
<g filter="url(#filter4_d_1392_20330)">
<path d="M458.234 -184.547C461.151 -188.979 464.068 -193.47 467 -198H465.05C403.692 -126.145 202.402 101.066 70 150H145.624C232.582 99.6275 337.261 -0.475967 458.234 -184.547Z" fill="#CC2A3D"/>
</g>
<path d="M453.359 -192.186C425.004 -19.4054 389.564 120.644 383 146H455V-196C454.574 -195.008 454.116 -193.957 453.627 -192.828C453.533 -192.614 453.454 -192.4 453.359 -192.186Z" fill="#3B6345"/>
</g>
<defs>
<filter id="filter0_d_1392_20330" x="-198" y="-206" width="679" height="380" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20330"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20330" result="shape"/>
</filter>
<filter id="filter1_d_1392_20330" x="113" y="-210" width="358" height="380" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20330"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20330" result="shape"/>
</filter>
<filter id="filter2_d_1392_20330" x="269" y="-200" width="201" height="370" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20330"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20330" result="shape"/>
</filter>
<filter id="filter3_d_1392_20330" x="190" y="-210" width="282" height="380" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20330"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20330" result="shape"/>
</filter>
<filter id="filter4_d_1392_20330" x="54" y="-206" width="429" height="380" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20330"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20330" result="shape"/>
</filter>
<clipPath id="clip0_1392_20330">
<rect width="439" height="100" fill="white"/>
</clipPath>
</defs>
</svg>
