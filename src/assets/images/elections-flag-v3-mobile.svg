<svg width="300" height="250" viewBox="0 0 300 250" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1392_20295)">
<g filter="url(#filter0_d_1392_20295)">
<path d="M309.154 -14.9694C29.3566 418.388 -74.0348 146.691 -74.0348 146.691L-74.0375 192.703C-64.9055 243.518 -17.668 271.481 -17.668 271.481L76.2202 271.488C154.483 231.212 273.473 44.1795 309.744 -14.9694L309.154 -14.9694Z" fill="#CC2A3D"/>
</g>
<g filter="url(#filter1_d_1392_20295)">
<path d="M307.401 -2.24601C309.631 -7.27117 311.419 -11.5602 312.68 -14.969L311.485 -14.9691C309.622 -11.5603 307.58 -7.86349 305.369 -3.89461C233.555 147.628 171.417 230.029 119.8 271.492L165.619 271.496C229.62 178.948 282.286 59.2404 307.401 -2.24601Z" fill="white"/>
</g>
<g filter="url(#filter2_d_1392_20295)">
<path d="M210.848 271.499L269.526 271.503C273.46 250.712 294.703 135.874 311.702 -5.8025C297.658 38.7152 245.533 201.245 210.848 271.499Z" fill="#3B6345"/>
</g>
<g filter="url(#filter3_d_1392_20295)">
<path d="M313.604 -14.4414C313.613 -14.5535 313.632 -14.6815 313.641 -14.7935L313.641 -14.8415C313.641 -14.8415 313.651 -14.9216 313.66 -14.9696L311.854 -14.9697C310.594 -11.5609 308.806 -7.2719 306.577 -2.24674C281.469 59.2396 228.818 178.947 164.835 271.495L212.353 271.499C246.857 201.035 298.711 38.0194 312.681 -6.63142C312.992 -9.24009 313.293 -11.8168 313.604 -14.4414Z" fill="white"/>
</g>
<g filter="url(#filter4_d_1392_20295)">
<path d="M305.534 -3.89401C307.26 -7.54284 308.987 -11.2397 310.723 -14.9685L309.569 -14.9686C273.251 44.1802 154.108 231.213 75.745 271.489L120.503 271.493C171.97 230.03 233.928 147.629 305.534 -3.89401Z" fill="#CC2A3D"/>
</g>
<path d="M311.699 -7.24159C294.726 135.171 273.516 250.605 269.588 271.504L312.664 271.507L312.681 -10.3852C312.426 -9.56724 312.152 -8.70114 311.859 -7.77087C311.803 -7.59444 311.755 -7.41802 311.699 -7.24159Z" fill="#3B6345"/>
</g>
<defs>
<filter id="filter0_d_1392_20295" x="-90.0371" y="-22.9697" width="415.781" height="318.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20295"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20295" result="shape"/>
</filter>
<filter id="filter1_d_1392_20295" x="103.8" y="-22.9688" width="224.88" height="318.465" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20295"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20295" result="shape"/>
</filter>
<filter id="filter2_d_1392_20295" x="194.848" y="-13.8027" width="132.854" height="309.306" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20295"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20295" result="shape"/>
</filter>
<filter id="filter3_d_1392_20295" x="148.835" y="-22.9697" width="180.825" height="318.469" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20295"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20295" result="shape"/>
</filter>
<filter id="filter4_d_1392_20295" x="59.7451" y="-22.9688" width="266.978" height="318.461" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20295"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20295" result="shape"/>
</filter>
<clipPath id="clip0_1392_20295">
<rect width="300" height="250" fill="white"/>
</clipPath>
</defs>
</svg>
