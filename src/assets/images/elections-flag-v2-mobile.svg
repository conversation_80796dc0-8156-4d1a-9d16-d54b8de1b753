<svg width="300" height="250" viewBox="0 0 300 250" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1392_20277)">
<g filter="url(#filter0_d_1392_20277)">
<path d="M302.486 -104.491C157.552 478.121 -5.49511 249.893 -5.49511 249.893L-1.00031 301.5C10.0781 355.34 111.72 329.585 111.72 329.585L174.565 297.188C223.015 225.003 284.383 -25.8363 302.882 -104.695L302.486 -104.491Z" fill="#CC2A3D"/>
</g>
<g filter="url(#filter1_d_1392_20277)">
<path d="M303.095 -89.8938C304.1 -96.3013 304.88 -101.73 305.393 -105.99L304.591 -105.576C303.675 -101.109 302.666 -96.2561 301.571 -91.0398C268.202 103.745 230.717 226.447 200.142 290.806L229.427 288.023C263.318 162.085 292.256 -12.2449 303.095 -89.8938Z" fill="white"/>
</g>
<g filter="url(#filter2_d_1392_20277)">
<path d="M252.529 305.655L322.661 253.657C321.588 227.453 315.075 83.1537 296.035 -86.7095C291.617 -25.623 274.468 198.155 252.529 305.655Z" fill="#3B6345"/>
</g>
<g filter="url(#filter3_d_1392_20277)">
<path d="M295.342 -61.538C295.338 -61.6669 295.338 -61.817 295.333 -61.9459L295.328 -61.9998C295.328 -61.9998 295.327 -62.0928 295.328 -62.1499L294.117 -61.5251C293.604 -57.2657 292.823 -51.8368 291.818 -45.4293C280.978 32.2202 257.344 184.7 223.45 310.639L255.339 294.2C271.608 203.23 290.475 2.45375 295.487 -52.4594C295.44 -55.4927 295.39 -58.4868 295.342 -61.538Z" fill="white"/>
</g>
<g filter="url(#filter4_d_1392_20277)">
<path d="M301.761 -90.8693C302.558 -95.5566 302.714 -100.237 303.51 -105.018L302.738 -104.62C284.249 -25.7658 222.908 225.059 174.476 297.235L199.454 293.409C229.796 229.171 268.717 103.748 301.761 -90.8693Z" fill="#CC2A3D"/>
</g>
</g>
<defs>
<filter id="filter0_d_1392_20277" x="-21.4951" y="-112.695" width="340.377" height="472.438" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20277"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20277" result="shape"/>
</filter>
<filter id="filter1_d_1392_20277" x="184.142" y="-113.989" width="137.251" height="428.795" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20277"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20277" result="shape"/>
</filter>
<filter id="filter2_d_1392_20277" x="236.529" y="-94.71" width="102.132" height="424.365" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20277"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20277" result="shape"/>
</filter>
<filter id="filter3_d_1392_20277" x="207.45" y="-70.1504" width="104.036" height="404.789" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20277"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20277" result="shape"/>
</filter>
<filter id="filter4_d_1392_20277" x="158.477" y="-113.018" width="161.033" height="434.253" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_20277"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_20277" result="shape"/>
</filter>
<clipPath id="clip0_1392_20277">
<rect width="300" height="250" fill="white"/>
</clipPath>
</defs>
</svg>
