(() => {
  var n = {
    936: (e, t, r) => {
      'use strict';
      r.r(t);
      r.d(t, {
        default: () => Dt,
        componentsToDebugString: () => Pt,
        getComponents: () => Et,
        getFullscreenElement: () => j,
        getScreenFrame: () => fe,
        hashComponents: () => jt,
        isChromium: () => C,
        isDesktopSafari: () => T,
        isEdgeHTML: () => S,
        isGecko: () => E,
        isTrident: () => x,
        isWebKit: () => A,
        load: () => Bt,
        murmurX64Hash128: () => zt,
      });
      var h = r(655);
      function d(e, t) {
        e = [e[0] >>> 16, e[0] & 65535, e[1] >>> 16, e[1] & 65535];
        t = [t[0] >>> 16, t[0] & 65535, t[1] >>> 16, t[1] & 65535];
        var r = [0, 0, 0, 0];
        r[3] += e[3] + t[3];
        r[2] += r[3] >>> 16;
        r[3] &= 65535;
        r[2] += e[2] + t[2];
        r[1] += r[2] >>> 16;
        r[2] &= 65535;
        r[1] += e[1] + t[1];
        r[0] += r[1] >>> 16;
        r[1] &= 65535;
        r[0] += e[0] + t[0];
        r[0] &= 65535;
        return [(r[0] << 16) | r[1], (r[2] << 16) | r[3]];
      }
      function f(e, t) {
        e = [e[0] >>> 16, e[0] & 65535, e[1] >>> 16, e[1] & 65535];
        t = [t[0] >>> 16, t[0] & 65535, t[1] >>> 16, t[1] & 65535];
        var r = [0, 0, 0, 0];
        r[3] += e[3] * t[3];
        r[2] += r[3] >>> 16;
        r[3] &= 65535;
        r[2] += e[2] * t[3];
        r[1] += r[2] >>> 16;
        r[2] &= 65535;
        r[2] += e[3] * t[2];
        r[1] += r[2] >>> 16;
        r[2] &= 65535;
        r[1] += e[1] * t[3];
        r[0] += r[1] >>> 16;
        r[1] &= 65535;
        r[1] += e[2] * t[2];
        r[0] += r[1] >>> 16;
        r[1] &= 65535;
        r[1] += e[3] * t[1];
        r[0] += r[1] >>> 16;
        r[1] &= 65535;
        r[0] += e[0] * t[3] + e[1] * t[2] + e[2] * t[1] + e[3] * t[0];
        r[0] &= 65535;
        return [(r[0] << 16) | r[1], (r[2] << 16) | r[3]];
      }
      function v(e, t) {
        t %= 64;
        if (t === 32) {
          return [e[1], e[0]];
        } else if (t < 32) {
          return [(e[0] << t) | (e[1] >>> (32 - t)), (e[1] << t) | (e[0] >>> (32 - t))];
        } else {
          t -= 32;
          return [(e[1] << t) | (e[0] >>> (32 - t)), (e[0] << t) | (e[1] >>> (32 - t))];
        }
      }
      function p(e, t) {
        t %= 64;
        if (t === 0) {
          return e;
        } else if (t < 32) {
          return [(e[0] << t) | (e[1] >>> (32 - t)), e[1] << t];
        } else {
          return [e[1] << (t - 32), 0];
        }
      }
      function m(e, t) {
        return [e[0] ^ t[0], e[1] ^ t[1]];
      }
      function w(e) {
        e = m(e, [0, e[0] >>> 1]);
        e = f(e, [4283543511, 3981806797]);
        e = m(e, [0, e[0] >>> 1]);
        e = f(e, [3301882366, 444984403]);
        e = m(e, [0, e[0] >>> 1]);
        return e;
      }
      function n(e, t) {
        e = e || '';
        t = t || 0;
        var r = e.length % 16;
        var n = e.length - r;
        var i = [0, t];
        var o = [0, t];
        var a = [0, 0];
        var s = [0, 0];
        var u = [2277735313, 289559509];
        var c = [1291169091, 658871167];
        var l;
        for (l = 0; l < n; l = l + 16) {
          a = [
            (e.charCodeAt(l + 4) & 255) | ((e.charCodeAt(l + 5) & 255) << 8) | ((e.charCodeAt(l + 6) & 255) << 16) | ((e.charCodeAt(l + 7) & 255) << 24),
            (e.charCodeAt(l) & 255) | ((e.charCodeAt(l + 1) & 255) << 8) | ((e.charCodeAt(l + 2) & 255) << 16) | ((e.charCodeAt(l + 3) & 255) << 24),
          ];
          s = [
            (e.charCodeAt(l + 12) & 255) | ((e.charCodeAt(l + 13) & 255) << 8) | ((e.charCodeAt(l + 14) & 255) << 16) | ((e.charCodeAt(l + 15) & 255) << 24),
            (e.charCodeAt(l + 8) & 255) | ((e.charCodeAt(l + 9) & 255) << 8) | ((e.charCodeAt(l + 10) & 255) << 16) | ((e.charCodeAt(l + 11) & 255) << 24),
          ];
          a = f(a, u);
          a = v(a, 31);
          a = f(a, c);
          i = m(i, a);
          i = v(i, 27);
          i = d(i, o);
          i = d(f(i, [0, 5]), [0, 1390208809]);
          s = f(s, c);
          s = v(s, 33);
          s = f(s, u);
          o = m(o, s);
          o = v(o, 31);
          o = d(o, i);
          o = d(f(o, [0, 5]), [0, 944331445]);
        }
        a = [0, 0];
        s = [0, 0];
        switch (r) {
          case 15:
            s = m(s, p([0, e.charCodeAt(l + 14)], 48));
          case 14:
            s = m(s, p([0, e.charCodeAt(l + 13)], 40));
          case 13:
            s = m(s, p([0, e.charCodeAt(l + 12)], 32));
          case 12:
            s = m(s, p([0, e.charCodeAt(l + 11)], 24));
          case 11:
            s = m(s, p([0, e.charCodeAt(l + 10)], 16));
          case 10:
            s = m(s, p([0, e.charCodeAt(l + 9)], 8));
          case 9:
            s = m(s, [0, e.charCodeAt(l + 8)]);
            s = f(s, c);
            s = v(s, 33);
            s = f(s, u);
            o = m(o, s);
          case 8:
            a = m(a, p([0, e.charCodeAt(l + 7)], 56));
          case 7:
            a = m(a, p([0, e.charCodeAt(l + 6)], 48));
          case 6:
            a = m(a, p([0, e.charCodeAt(l + 5)], 40));
          case 5:
            a = m(a, p([0, e.charCodeAt(l + 4)], 32));
          case 4:
            a = m(a, p([0, e.charCodeAt(l + 3)], 24));
          case 3:
            a = m(a, p([0, e.charCodeAt(l + 2)], 16));
          case 2:
            a = m(a, p([0, e.charCodeAt(l + 1)], 8));
          case 1:
            a = m(a, [0, e.charCodeAt(l)]);
            a = f(a, u);
            a = v(a, 31);
            a = f(a, c);
            i = m(i, a);
        }
        i = m(i, [0, e.length]);
        o = m(o, [0, e.length]);
        i = d(i, o);
        o = d(o, i);
        i = w(i);
        o = w(o);
        i = d(i, o);
        o = d(o, i);
        return (
          ('00000000' + (i[0] >>> 0).toString(16)).slice(-8) +
          ('00000000' + (i[1] >>> 0).toString(16)).slice(-8) +
          ('00000000' + (o[0] >>> 0).toString(16)).slice(-8) +
          ('00000000' + (o[1] >>> 0).toString(16)).slice(-8)
        );
      }
      var i = '3.1.1';
      function b(t, r) {
        return new Promise(function (e) {
          return setTimeout(e, t, r);
        });
      }
      function o(e, t) {
        if (t === void 0) {
          t = Infinity;
        }
        var r = window.requestIdleCallback;
        if (r) {
          return new Promise(function (e) {
            return r(
              function () {
                return e();
              },
              { timeout: t }
            );
          });
        } else {
          return b(Math.min(e, t));
        }
      }
      function a(e) {
        var t;
        return (0, h.__assign)({ name: e.name, message: e.message, stack: (t = e.stack) === null || t === void 0 ? void 0 : t.split('\n') }, e);
      }
      function s(e, t) {
        for (var r = 0, n = e.length; r < n; ++r) {
          if (e[r] === t) {
            return true;
          }
        }
        return false;
      }
      function g(e, t) {
        return !s(e, t);
      }
      function u(e) {
        return parseInt(e);
      }
      function c(e) {
        return parseFloat(e);
      }
      function l(e, t) {
        return typeof e === 'number' && isNaN(e) ? t : e;
      }
      function y(e) {
        return e.reduce(function (e, t) {
          return e + (t ? 1 : 0);
        }, 0);
      }
      function k(e, t) {
        if (t === void 0) {
          t = 1;
        }
        if (Math.abs(t) >= 1) {
          return Math.round(e / t) * t;
        } else {
          var r = 1 / t;
          return Math.round(e * r) / r;
        }
      }
      function _(e) {
        var t, r;
        var n = "Unexpected syntax '" + e + "'";
        var i = /^\s*([a-z-]*)(.*)$/i.exec(e);
        var o = i[1] || undefined;
        var a = {};
        var s = /([.:#][\w-]+|\[.+?\])/gi;
        var u = function (e, t) {
          a[e] = a[e] || [];
          a[e].push(t);
        };
        for (;;) {
          var c = s.exec(i[2]);
          if (!c) {
            break;
          }
          var l = c[0];
          switch (l[0]) {
            case '.':
              u('class', l.slice(1));
              break;
            case '#':
              u('id', l.slice(1));
              break;
            case '[': {
              var d = /^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(l);
              if (d) {
                u(d[1], (r = (t = d[4]) !== null && t !== void 0 ? t : d[5]) !== null && r !== void 0 ? r : '');
              } else {
                throw new Error(n);
              }
              break;
            }
            default:
              throw new Error(n);
          }
        }
        return [o, a];
      }
      function x() {
        var e = window;
        var t = navigator;
        return y(['MSCSSMatrix' in e, 'msSetImmediate' in e, 'msIndexedDB' in e, 'msMaxTouchPoints' in t, 'msPointerEnabled' in t]) >= 4;
      }
      function S() {
        var e = window;
        var t = navigator;
        return y(['msWriteProfilerMark' in e, 'MSStream' in e, 'msLaunchUri' in t, 'msSaveBlob' in t]) >= 3 && !x();
      }
      function C() {
        var e = window;
        var t = navigator;
        return (
          y([
            'webkitPersistentStorage' in t,
            'webkitTemporaryStorage' in t,
            t.vendor.indexOf('Google') === 0,
            'webkitResolveLocalFileSystemURL' in e,
            'BatteryManager' in e,
            'webkitMediaStream' in e,
            'webkitSpeechGrammar' in e,
          ]) >= 5
        );
      }
      function A() {
        var e = window;
        var t = navigator;
        return (
          y([
            'ApplePayError' in e,
            'CSSPrimitiveValue' in e,
            'Counter' in e,
            t.vendor.indexOf('Apple') === 0,
            'getStorageUpdates' in t,
            'WebKitMediaKeys' in e,
          ]) >= 4
        );
      }
      function T() {
        var e = window;
        return y(['safari' in e, !('DeviceMotionEvent' in e), !('ongestureend' in e), !('standalone' in navigator)]) >= 3;
      }
      function E() {
        var e, t;
        var r = window;
        return (
          y([
            'buildID' in navigator,
            'MozAppearance' in ((t = (e = document.documentElement) === null || e === void 0 ? void 0 : e.style) !== null && t !== void 0 ? t : {}),
            'MediaRecorderErrorEvent' in r,
            'mozInnerScreenX' in r,
            'CSSMozDocumentRule' in r,
            'CanvasCaptureMediaStream' in r,
          ]) >= 4
        );
      }
      function O() {
        var e = window;
        return y([!('MediaSettingsRange' in e), 'RTCEncodedAudioFrame' in e, '' + e.Intl === '[object Intl]', '' + e.Reflect === '[object Reflect]']) >= 3;
      }
      function M() {
        var e = window;
        return y(['DOMRectList' in e, 'RTCPeerConnectionIceEvent' in e, 'SVGGeometryElement' in e, 'ontransitioncancel' in e]) >= 3;
      }
      function P() {
        if (navigator.platform === 'iPad') {
          return true;
        }
        var e = screen;
        var t = e.width / e.height;
        return y(['MediaSource' in window, !!Element.prototype.webkitRequestFullscreen, t > 2 / 3 && t < 3 / 2]) >= 2;
      }
      function j() {
        var e = document;
        return e.fullscreenElement || e.msFullscreenElement || e.mozFullScreenElement || e.webkitFullscreenElement || null;
      }
      function I() {
        var e = document;
        return (e.exitFullscreen || e.msExitFullscreen || e.mozCancelFullScreen || e.webkitExitFullscreen).call(e);
      }
      function R() {
        var e = C();
        var t = E();
        if (!e && !t) {
          return false;
        }
        var r = window;
        return y(['onorientationchange' in r, 'orientation' in r, e && 'SharedWorker' in r, t && /android/i.test(navigator.appVersion)]) >= 2;
      }
      function B() {
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var t, r, n, i, o, a, s, u, c;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                t = window;
                r = t.OfflineAudioContext || t.webkitOfflineAudioContext;
                if (!r) {
                  return [2, -2];
                }
                if (L()) {
                  return [2, -1];
                }
                n = 4500;
                i = 5e3;
                o = new r(1, i, 44100);
                a = o.createOscillator();
                a.type = 'triangle';
                a.frequency.value = 1e4;
                s = o.createDynamicsCompressor();
                s.threshold.value = -50;
                s.knee.value = 40;
                s.ratio.value = 12;
                s.attack.value = 0;
                s.release.value = 0.25;
                a.connect(s);
                s.connect(o.destination);
                a.start(0);
                e.label = 1;
              case 1:
                e.trys.push([1, 3, , 4]);
                return [4, z(o)];
              case 2:
                u = e.sent();
                return [3, 4];
              case 3:
                c = e.sent();
                if (c.name === 'timeout' || c.name === 'suspended') {
                  return [2, -3];
                }
                throw c;
              case 4:
                return [2, D(u.getChannelData(0).subarray(n))];
            }
          });
        });
      }
      function L() {
        return A() && !T() && !M();
      }
      function z(i) {
        var o = 3;
        var a = 500;
        var s = 1e3;
        return new Promise(function (t, e) {
          i.oncomplete = function (e) {
            return t(e.renderedBuffer);
          };
          var r = o;
          var n = function () {
            i.startRendering();
            switch (i.state) {
              case 'running':
                setTimeout(function () {
                  return e(F('timeout'));
                }, s);
                break;
              case 'suspended':
                if (!document.hidden) {
                  r--;
                }
                if (r > 0) {
                  setTimeout(n, a);
                } else {
                  e(F('suspended'));
                }
                break;
            }
          };
          n();
        });
      }
      function D(e) {
        var t = 0;
        for (var r = 0; r < e.length; ++r) {
          t += Math.abs(e[r]);
        }
        return t;
      }
      function F(e) {
        var t = new Error(e);
        t.name = e;
        return t;
      }
      function N(t, o, r) {
        var a, s;
        if (r === void 0) {
          r = 50;
        }
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var n, i;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                n = document;
                e.label = 1;
              case 1:
                if (!!n.body) return [3, 3];
                return [4, b(r)];
              case 2:
                e.sent();
                return [3, 1];
              case 3:
                i = n.createElement('iframe');
                e.label = 4;
              case 4:
                e.trys.push([4, , 10, 11]);
                return [
                  4,
                  new Promise(function (e, t) {
                    i.onload = e;
                    i.onerror = t;
                    var r = i.style;
                    r.setProperty('display', 'block', 'important');
                    r.position = 'absolute';
                    r.top = '0';
                    r.left = '0';
                    r.visibility = 'hidden';
                    n.body.appendChild(i);
                    if (o && 'srcdoc' in i) {
                      i.srcdoc = o;
                    } else {
                      i.src = 'about:blank';
                    }
                  }),
                ];
              case 5:
                e.sent();
                e.label = 6;
              case 6:
                if (!!((a = i.contentWindow) === null || a === void 0 ? void 0 : a.document.body)) return [3, 8];
                return [4, b(r)];
              case 7:
                e.sent();
                return [3, 6];
              case 8:
                return [4, t(i, i.contentWindow)];
              case 9:
                return [2, e.sent()];
              case 10:
                (s = i.parentNode) === null || s === void 0 ? void 0 : s.removeChild(i);
                return [7];
              case 11:
                return [2];
            }
          });
        });
      }
      function q(e) {
        var t = _(e),
          r = t[0],
          n = t[1];
        var i = document.createElement(r !== null && r !== void 0 ? r : 'div');
        for (var o = 0, a = Object.keys(n); o < a.length; o++) {
          var s = a[o];
          i.setAttribute(s, n[s].join(' '));
        }
        return i;
      }
      var G = 'mmMwWLliI0O&1';
      var U = '48px';
      var H = ['monospace', 'sans-serif', 'serif'];
      var V = [
        'sans-serif-thin',
        'ARNO PRO',
        'Agency FB',
        'Arabic Typesetting',
        'Arial Unicode MS',
        'AvantGarde Bk BT',
        'BankGothic Md BT',
        'Batang',
        'Bitstream Vera Sans Mono',
        'Calibri',
        'Century',
        'Century Gothic',
        'Clarendon',
        'EUROSTILE',
        'Franklin Gothic',
        'Futura Bk BT',
        'Futura Md BT',
        'GOTHAM',
        'Gill Sans',
        'HELV',
        'Haettenschweiler',
        'Helvetica Neue',
        'Humanst521 BT',
        'Leelawadee',
        'Letter Gothic',
        'Levenim MT',
        'Lucida Bright',
        'Lucida Sans',
        'Menlo',
        'MS Mincho',
        'MS Outlook',
        'MS Reference Specialty',
        'MS UI Gothic',
        'MT Extra',
        'MYRIAD PRO',
        'Marlett',
        'Meiryo UI',
        'Microsoft Uighur',
        'Minion Pro',
        'Monotype Corsiva',
        'PMingLiU',
        'Pristina',
        'SCRIPTINA',
        'Segoe UI Light',
        'Serifa',
        'SimHei',
        'Small Fonts',
        'Staccato222 BT',
        'TRAJAN PRO',
        'Univers CE 55 Medium',
        'Vrinda',
        'ZWAdobeF',
      ];
      function W() {
        return N(function (e, t) {
          var n = t.document;
          var r = n.body;
          r.style.fontSize = U;
          var i = n.createElement('div');
          var o = {};
          var a = {};
          var s = function (e) {
            var t = n.createElement('span');
            var r = t.style;
            r.position = 'absolute';
            r.top = '0';
            r.left = '0';
            r.fontFamily = e;
            t.textContent = G;
            i.appendChild(t);
            return t;
          };
          var u = function (e, t) {
            return s("'" + e + "'," + t);
          };
          var c = function () {
            return H.map(s);
          };
          var l = function () {
            var e = {};
            var t = function (t) {
              e[t] = H.map(function (e) {
                return u(t, e);
              });
            };
            for (var r = 0, n = V; r < n.length; r++) {
              var i = n[r];
              t(i);
            }
            return e;
          };
          var d = function (r) {
            return H.some(function (e, t) {
              return r[t].offsetWidth !== o[e] || r[t].offsetHeight !== a[e];
            });
          };
          var f = c();
          var h = l();
          r.appendChild(i);
          for (var v = 0; v < H.length; v++) {
            o[H[v]] = f[v].offsetWidth;
            a[H[v]] = f[v].offsetHeight;
          }
          return V.filter(function (e) {
            return d(h[e]);
          });
        });
      }
      function J() {
        var e = navigator.plugins;
        if (!e) {
          return undefined;
        }
        var t = [];
        for (var r = 0; r < e.length; ++r) {
          var n = e[r];
          if (!n) {
            continue;
          }
          var i = [];
          for (var o = 0; o < n.length; ++o) {
            var a = n[o];
            i.push({ type: a.type, suffixes: a.suffixes });
          }
          t.push({ name: n.name, description: n.description, mimeTypes: i });
        }
        return t;
      }
      function $() {
        var e = Z(),
          t = e[0],
          r = e[1];
        if (!Y(t, r)) {
          return { winding: false, geometry: '', text: '' };
        }
        return { winding: K(r), geometry: Q(t, r), text: X(t, r) };
      }
      function Z() {
        var e = document.createElement('canvas');
        e.width = 1;
        e.height = 1;
        return [e, e.getContext('2d')];
      }
      function Y(e, t) {
        return !!(t && e.toDataURL);
      }
      function K(e) {
        e.rect(0, 0, 10, 10);
        e.rect(2, 2, 6, 6);
        return !e.isPointInPath(5, 5, 'evenodd');
      }
      function X(e, t) {
        e.width = 240;
        e.height = 60;
        t.textBaseline = 'alphabetic';
        t.fillStyle = '#f60';
        t.fillRect(100, 1, 62, 20);
        t.fillStyle = '#069';
        t.font = '11pt "Times New Roman"';
        var r = 'Cwm fjordbank gly ' + String.fromCharCode(55357, 56835);
        t.fillText(r, 2, 15);
        t.fillStyle = 'rgba(102, 204, 0, 0.2)';
        t.font = '18pt Arial';
        t.fillText(r, 4, 45);
        return ee(e);
      }
      function Q(e, t) {
        e.width = 122;
        e.height = 110;
        t.globalCompositeOperation = 'multiply';
        for (
          var r = 0,
            n = [
              ['#f2f', 40, 40],
              ['#2ff', 80, 40],
              ['#ff2', 60, 80],
            ];
          r < n.length;
          r++
        ) {
          var i = n[r],
            o = i[0],
            a = i[1],
            s = i[2];
          t.fillStyle = o;
          t.beginPath();
          t.arc(a, s, 40, 0, Math.PI * 2, true);
          t.closePath();
          t.fill();
        }
        t.fillStyle = '#f9c';
        t.arc(60, 60, 60, 0, Math.PI * 2, true);
        t.arc(60, 60, 20, 0, Math.PI * 2, true);
        t.fill('evenodd');
        return ee(e);
      }
      function ee(e) {
        return e.toDataURL();
      }
      function te() {
        var e = navigator;
        var t = 0;
        var r;
        if (e.maxTouchPoints !== undefined) {
          t = u(e.maxTouchPoints);
        } else if (e.msMaxTouchPoints !== undefined) {
          t = e.msMaxTouchPoints;
        }
        try {
          document.createEvent('TouchEvent');
          r = true;
        } catch (e) {
          r = false;
        }
        var n = 'ontouchstart' in window;
        return { maxTouchPoints: t, touchEvent: r, touchStart: n };
      }
      function re() {
        return navigator.oscpu;
      }
      function ne() {
        var e = navigator;
        var t = [];
        var r = e.language || e.userLanguage || e.browserLanguage || e.systemLanguage;
        if (r !== undefined) {
          t.push([r]);
        }
        if (Array.isArray(e.languages)) {
          if (!(C() && O())) {
            t.push(e.languages);
          }
        } else if (typeof e.languages === 'string') {
          var n = e.languages;
          if (n) {
            t.push(n.split(','));
          }
        }
        return t;
      }
      function ie() {
        return window.screen.colorDepth;
      }
      function oe() {
        return l(c(navigator.deviceMemory), undefined);
      }
      function ae() {
        var e = screen;
        var t = [u(e.width), u(e.height)];
        t.sort().reverse();
        return t;
      }
      var se = 2500;
      var ue = 10;
      var ce;
      var le;
      function de() {
        if (le !== undefined) {
          return;
        }
        var t = function () {
          var e = ve();
          if (pe(e)) {
            le = setTimeout(t, se);
          } else {
            ce = e;
            le = undefined;
          }
        };
        t();
      }
      function fe() {
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var t;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                t = ve();
                if (!pe(t)) return [3, 2];
                if (ce) {
                  return [2, (0, h.__spreadArrays)(ce)];
                }
                if (!j()) return [3, 2];
                return [4, I()];
              case 1:
                e.sent();
                t = ve();
                e.label = 2;
              case 2:
                if (!pe(t)) {
                  ce = t;
                }
                return [2, t];
            }
          });
        });
      }
      function he() {
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var t, r;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                t = function (e) {
                  return e === null ? null : k(e, ue);
                };
                return [4, fe()];
              case 1:
                r = e.sent();
                return [2, [t(r[0]), t(r[1]), t(r[2]), t(r[3])]];
            }
          });
        });
      }
      function ve() {
        var e = screen;
        return [
          l(c(e.availTop), null),
          l(c(e.width) - c(e.availWidth) - l(c(e.availLeft), 0), null),
          l(c(e.height) - c(e.availHeight) - l(c(e.availTop), 0), null),
          l(c(e.availLeft), null),
        ];
      }
      function pe(e) {
        for (var t = 0; t < 4; ++t) {
          if (e[t]) {
            return false;
          }
        }
        return true;
      }
      function me() {
        return l(u(navigator.hardwareConcurrency), undefined);
      }
      function we() {
        var e;
        var t = (e = window.Intl) === null || e === void 0 ? void 0 : e.DateTimeFormat;
        if (t) {
          var r = new t().resolvedOptions().timeZone;
          if (r) {
            return r;
          }
        }
        var n = -be();
        return 'UTC' + (n >= 0 ? '+' : '') + Math.abs(n);
      }
      function be() {
        var e = new Date().getFullYear();
        return Math.max(c(new Date(e, 0, 1).getTimezoneOffset()), c(new Date(e, 6, 1).getTimezoneOffset()));
      }
      function ge() {
        try {
          return !!window.sessionStorage;
        } catch (e) {
          return true;
        }
      }
      function ye() {
        try {
          return !!window.localStorage;
        } catch (e) {
          return true;
        }
      }
      function ke() {
        if (x() || S()) {
          return undefined;
        }
        try {
          return !!window.indexedDB;
        } catch (e) {
          return true;
        }
      }
      function _e() {
        return !!window.openDatabase;
      }
      function xe() {
        return navigator.cpuClass;
      }
      function Se() {
        var e = navigator.platform;
        if (e === 'MacIntel') {
          if (A() && !T()) {
            return P() ? 'iPad' : 'iPhone';
          }
        }
        return e;
      }
      function Ce() {
        return navigator.vendor || '';
      }
      function Ae() {
        var e = [];
        for (
          var t = 0,
            r = [
              'chrome',
              'safari',
              '__crWeb',
              '__gCrWeb',
              'yandex',
              '__yb',
              '__ybro',
              '__firefox__',
              '__edgeTrackingPreventionStatistics',
              'webkit',
              'oprt',
              'samsungAr',
              'ucweb',
              'UCShellJava',
              'puffinDevice',
            ];
          t < r.length;
          t++
        ) {
          var n = r[t];
          var i = window[n];
          if (i && typeof i === 'object') {
            e.push(n);
          }
        }
        return e.sort();
      }
      function Te() {
        var e = document;
        try {
          e.cookie = 'cookietest=1; SameSite=Strict;';
          var t = e.cookie.indexOf('cookietest=') !== -1;
          e.cookie = 'cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT';
          return t;
        } catch (e) {
          return false;
        }
      }
      var Ee = {
        abpIndo: ['#Iklan-Melayang', '#Kolom-Iklan-728', '#SidebarIklan-wrapper', 'a[title="7naga poker" i]', 'img[title="ALIENBOLA" i]'],
        abpvn: ['#quangcaomb', '.i-said-no-thing-can-stop-me-warning.dark', '.quangcao', '[href^="https://r88.vn/"]', '[href^="https://zbet.vn/"]'],
        adBlockFinland: ['.mainostila', '.sponsorit', '.ylamainos', 'a[href*="/clickthrgh.asp?"]', 'a[href^="https://app.readpeak.com/ads"]'],
        adBlockPersian: [
          '.widget_arvins_ad_randomizer',
          'a[href^="https://iqoption.com/lp/mobile-partner/?aff="]',
          'a[href*="fastclick.net/ad/"]',
          'TABLE[width="140px"]',
          '.facebook_shows_ad_cale',
        ],
        adBlockWarningRemoval: ['#adblock_message', '.adblockInfo', '.deadblocker-header-bar', '.no-ad-reminder', '#AdBlockDialog'],
        adGuardAnnoyances: ['amp-embed[type="zen"]', '.hs-sosyal', '#cookieconsentdiv', 'div[class^="app_gdpr"]', '.as-oil'],
        adGuardBase: ['.ad-disclaimer-container', '#content_ad_container', '#ad_wp_base', '#adxtop', '#bannerfloat22'],
        adGuardChinese: ['a[href*=".123ch.cn"]', 'a[href*=".acuxrecv.cn"]', 'a[href*=".bayiyy.com/download."]', 'a[href*=".quankan.tv"]', '#j-new-ad'],
        adGuardFrench: [
          '#div_banniere_pub',
          '#sp-entete-pour-la-pub',
          'a[href*="fducks.com/"]',
          'a[href^="http://promo.vador.com/"]',
          'a[href^="https://syndication.exdynsrv.com/"]',
        ],
        adGuardGerman: [
          '.banneritemwerbung_head_1',
          '.boxstartwerbung',
          '.werbung3',
          'a[href^="http://www.firstload.de/index.php?set_lang=de&log="]',
          'a[href^="http://www.sendung-now.de/tick/click.php?id="]',
        ],
        adGuardJapanese: [
          '.ad-text-blockA01',
          '._popIn_infinite_video',
          '[class^=blogroll_wrapper]',
          'a[href^="http://ad2.trafficgate.net/"]',
          'a[href^="http://www.rssad.jp/"]',
        ],
        adGuardMobile: ['amp-auto-ads', '#mgid_iframe', '.amp_ad', 'amp-sticky-ad', '.plugin-blogroll'],
        adGuardRussian: [
          'a[href^="https://ya-distrib.ru/r/"]',
          'a[href*=".twkv.ru"]',
          'div[data-adv-type="dfp"]',
          '.b-journalpromo-container',
          'div[id^="AdFox_banner_"]',
        ],
        adGuardSocial: [
          'a[href^="//www.stumbleupon.com/submit?url="]',
          'a[href^="//telegram.me/share/url?"]',
          '#___plusone_0',
          '#inlineShare',
          '.popup-social',
        ],
        adGuardSpanishPortuguese: ['.esp_publicidad', '#Publicidade', '#publiEspecial', '#queTooltip', '[href^="http://ads.glispa.com/"]'],
        adGuardTrackingProtection: [
          'amp-embed[type="taboola"]',
          '#qoo-counter',
          'a[href^="http://click.hotlog.ru/"]',
          'a[href^="http://hitcounter.ru/top/stat.php"]',
          'a[href^="http://top.mail.ru/jump"]',
        ],
        adGuardTurkish: [
          '#backkapat',
          '#reklam',
          'a[href^="http://adserv.ontek.com.tr/"]',
          'a[href^="http://izlenzi.com/campaign/"]',
          'a[href^="http://www.installads.net/"]',
        ],
        bulgarian: ['#adbody', '#newAd', '#ea_intext_div', '.lapni-pop-over', '#xenium_hot_offers'],
        easyList: ['[lazy-ad="leftthin_banner"]', '.smart_ads_bom_title', '.slide-advert_float', '.six-ads-wrapper', '.showcaseAd'],
        easyListChina: [
          '#fuo_top_float',
          '.kf_qycn_com_cckf_welcomebox',
          'a[href*=".caohang.com.cn/"]',
          'a[href*=".yuanmengbi.com/"]',
          '.layui-row[style="border-radius:10px;background-color:#ff0000;padding:15px;margin:15px;"]',
        ],
        easyListCookie: ['#cookieBgOverlay', '#alerte-cookies', '#cookieLY', '#dlgCookies', '.Section-Cookie'],
        easyListCzechSlovak: ['#onlajny-stickers', '#reklamni-box', '.reklama-megaboard', '.sklik', '[id^="sklikReklama"]'],
        easyListDutch: [
          '#advertentie',
          '#vipAdmarktBannerBlock',
          '.adstekst',
          'a[href^="http://adserver.webads.nl/adclick/"]',
          'a[href^="http://www.site-id.nl/servlet/___?"]',
        ],
        easyListGermany: ['.werb_textlink', '#ad-qm-sidebar-oben', '.adguru-content-html', '.nfy-sebo-ad', '.textlinkwerbung'],
        easyListItaly: [
          '.box_adv_annunci',
          '.sb-box-pubbliredazionale',
          'a[href^="http://affiliazioniads.snai.it/"]',
          'a[href^="https://adserver.html.it/"]',
          'a[href^="https://affiliazioniads.snai.it/"]',
        ],
        easyListLithuania: [
          '.reklamos_tarpas',
          'a[href="http://igrovoi-klub.org/fair-land"]',
          'a[href="http://www.moteris.lt/didieji-grozio-pokyciai/"]',
          'img[alt="Dedikuoti.lt serveriai"]',
          'img[alt="Hostingas Serveriai.lt"]',
        ],
        estonian: ['.flex--align-items-center.flex--justify-content-center.flex.section-branding__digipakett-contents', 'A[href*="http://pay4results24.eu"]'],
        fanboyAnnoyances: ['#feedback-tab', '#ruby-back-top', '.feedburnerFeedBlock', '.widget-feedburner-counter', '[title="Subscribe to our blog"]'],
        fanboyAntiFacebook: ['.util-bar-module-firefly-visible'],
        fanboyEnhancedTrackers: [
          '.open.pushModal',
          '#issuem-leaky-paywall-articles-zero-remaining-nag',
          'div[style*="box-shadow: rgb(136, 136, 136) 0px 0px 12px; color: "]',
          'div[class$="-hide"][zoompage-fontsize][style="display: block;"]',
          '.BlockNag__Card',
        ],
        fanboySocial: [
          '.td-tags-and-social-wrapper-box',
          '.twitterContainer',
          '.youtube-social',
          'a[title^="Like us on Facebook"]',
          'img[alt^="Share on Digg"]',
        ],
        frellwitSwedish: [
          'a[href*="casinopro.se"][target="_blank"]',
          'a[href*="doktor-se.onelink.me"]',
          'article.category-samarbete',
          'img[alt="Leovegas"]',
          'ul.adsmodern',
        ],
        greekAdBlock: [
          'A[href*="adman.otenet.gr/click?"]',
          'A[href*="http://axiabanners.exodus.gr/"]',
          'A[href*="http://interactive.forthnet.gr/click?"]',
          'DIV.agores300',
          'TABLE.advright',
        ],
        hungarian: ['A[href*="ad.eval.hu"]', 'A[href*="ad.netmedia.hu"]', 'A[href*="daserver.ultraweb.hu"]', '#cemp_doboz', '.optimonk-iframe-container'],
        iDontCareAboutCookies: [
          '.alert-info[data-block-track*="CookieNotice"]',
          '.ModuleTemplateCookieIndicator',
          '.o--cookies--container',
          '.cookie-msg-info-container',
          '#cookies-policy-sticky',
        ],
        icelandicAbp: ['A[href^="/framework/resources/forms/ads.aspx"]'],
        latvian: [
          'a[href="http://www.salidzini.lv/"][style="display: block; width: 120px; height: 40px; overflow: hidden; position: relative;"]',
          'a[href="http://www.salidzini.lv/"][style="display: block; width: 88px; height: 31px; overflow: hidden; position: relative;"]',
        ],
        listKr: [
          'a[href*="//kingtoon.slnk.kr"]',
          'a[href*="//playdsb.com/kr"]',
          'a[href*="//simba-kor.com"]',
          'div[data-widget_id="ml6EJ074"]',
          'ins.daum_ddn_area',
        ],
        listeAr: ['.geminiLB1Ad', '.right-and-left-sponsers', 'a[href*=".aflam.info"]', 'a[href*="booraq.org"]', 'a[href*="dubizzle.com/ar/?utm_source="]'],
        listeFr: [
          'a[href^="http://look.djfiln.com/"]',
          '#adcontainer_recherche',
          'a[href*="weborama.fr/fcgi-bin/"]',
          'a[href^="https://secure.securitetotale.fr/"]',
          'div[id^="crt-"][data-criteo-id]',
        ],
        officialPolish: [
          '#ceneo-placeholder-ceneo-12',
          '[href^="https://aff.sendhub.pl/"]',
          'a[href^="http://advmanager.techfun.pl/redirect/"]',
          'a[href^="http://www.trizer.pl/?utm_source"]',
          'div#skapiec_ad',
        ],
        ro: [
          'a[href^="//afftrk.altex.ro/Counter/Click"',
          'a[href^="/magazin/"',
          'a[href^="https://blackfridaysales.ro/trk/shop/"',
          'a[href^="https://event.2performant.com/events/click"',
          'a[href^="https://l.profitshare.ro/"]',
        ],
        ruAd: ['a[href*="//febrare.ru/"]', 'a[href*="//utimg.ru/"]', 'a[href*="://chikidiki.ru"]', '#pgeldiz', '.yandex-rtb-block'],
        thaiAds: ['a[href*=macau-uta-popup]', '#ads-google-middle_rectangle-group', '.ads300s', '.bumq', '.img-kosana'],
        webAnnoyancesUltralist: ['#mod-social-share-2', '#social-tools', '.ctpl-fullbanner', '.j-share-bar-left', '.yt.btn-link.btn-md.btn'],
      };
      var Oe = Object.keys(Ee);
      function Me(e) {
        var o = (e === void 0 ? {} : e).debug;
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var t, n, r;
          var i;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                if (!Pe()) {
                  return [2, undefined];
                }
                t = (i = []).concat.apply(
                  i,
                  Oe.map(function (e) {
                    return Ee[e];
                  })
                );
                return [4, je(t)];
              case 1:
                n = e.sent();
                if (o) {
                  Re(n);
                }
                r = Oe.filter(function (e) {
                  var t = Ee[e];
                  var r = y(
                    t.map(function (e) {
                      return n[e];
                    })
                  );
                  return r > t.length * 0.5;
                });
                r.sort();
                return [2, r];
            }
          });
        });
      }
      function Pe() {
        return A() || R();
      }
      function je(d) {
        var f;
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var t, r, n, i, o, a, s, u, c, l;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                t = document;
                r = t.createElement('div');
                n = [];
                i = {};
                Ie(r);
                for (o = 0, a = d; o < a.length; o++) {
                  s = a[o];
                  u = q(s);
                  c = t.createElement('div');
                  Ie(c);
                  c.appendChild(u);
                  r.appendChild(c);
                  n.push(u);
                }
                e.label = 1;
              case 1:
                if (!!t.body) return [3, 3];
                return [4, b(100)];
              case 2:
                e.sent();
                return [3, 1];
              case 3:
                t.body.appendChild(r);
                try {
                  for (l = 0; l < d.length; ++l) {
                    if (!n[l].offsetParent) {
                      i[d[l]] = true;
                    }
                  }
                } finally {
                  (f = r.parentNode) === null || f === void 0 ? void 0 : f.removeChild(r);
                }
                return [2, i];
            }
          });
        });
      }
      function Ie(e) {
        e.style.setProperty('display', 'block', 'important');
      }
      function Re(e) {
        var t = 'DOM blockers debug:\n```';
        for (var r = 0, n = Oe; r < n.length; r++) {
          var i = n[r];
          t += '\n' + i + ':';
          for (var o = 0, a = Ee[i]; o < a.length; o++) {
            var s = a[o];
            t += '\n  ' + s + ' ' + (e[s] ? 'đŤ' : 'âĄď¸');
          }
        }
        console.log(t + '\n```');
      }
      function Be() {
        for (var e = 0, t = ['rec2020', 'p3', 'srgb']; e < t.length; e++) {
          var r = t[e];
          if (matchMedia('(color-gamut: ' + r + ')').matches) {
            return r;
          }
        }
        return undefined;
      }
      function Le() {
        if (ze('inverted')) {
          return true;
        }
        if (ze('none')) {
          return false;
        }
        return undefined;
      }
      function ze(e) {
        return matchMedia('(inverted-colors: ' + e + ')').matches;
      }
      function De() {
        if (Fe('active')) {
          return true;
        }
        if (Fe('none')) {
          return false;
        }
        return undefined;
      }
      function Fe(e) {
        return matchMedia('(forced-colors: ' + e + ')').matches;
      }
      var Ne = 100;
      function qe() {
        if (!matchMedia('(min-monochrome: 0)').matches) {
          return undefined;
        }
        for (var e = 0; e <= Ne; ++e) {
          if (matchMedia('(max-monochrome: ' + e + ')').matches) {
            return e;
          }
        }
        throw new Error('Too high value');
      }
      function Ge() {
        if (Ue('no-preference')) {
          return 0;
        }
        if (Ue('high') || Ue('more')) {
          return 1;
        }
        if (Ue('low') || Ue('less')) {
          return -1;
        }
        if (Ue('forced')) {
          return 10;
        }
        return undefined;
      }
      function Ue(e) {
        return matchMedia('(prefers-contrast: ' + e + ')').matches;
      }
      function He() {
        if (Ve('reduce')) {
          return true;
        }
        if (Ve('no-preference')) {
          return false;
        }
        return undefined;
      }
      function Ve(e) {
        return matchMedia('(prefers-reduced-motion: ' + e + ')').matches;
      }
      function We() {
        if (Je('high')) {
          return true;
        }
        if (Je('standard')) {
          return false;
        }
        return undefined;
      }
      function Je(e) {
        return matchMedia('(dynamic-range: ' + e + ')').matches;
      }
      var $e = Math;
      var Ze = function () {
        return 0;
      };
      var Ye = $e.acos || Ze;
      var Ke = $e.acosh || Ze;
      var Xe = $e.asin || Ze;
      var Qe = $e.asinh || Ze;
      var et = $e.atanh || Ze;
      var tt = $e.atan || Ze;
      var rt = $e.sin || Ze;
      var nt = $e.sinh || Ze;
      var it = $e.cos || Ze;
      var ot = $e.cosh || Ze;
      var at = $e.tan || Ze;
      var st = $e.tanh || Ze;
      var ut = $e.exp || Ze;
      var ct = $e.expm1 || Ze;
      var lt = $e.log1p || Ze;
      var dt = function (e) {
        return $e.pow($e.PI, e);
      };
      var ft = function (e) {
        return $e.log(e + $e.sqrt(e * e - 1));
      };
      var ht = function (e) {
        return $e.log(e + $e.sqrt(e * e + 1));
      };
      var vt = function (e) {
        return $e.log((1 + e) / (1 - e)) / 2;
      };
      var pt = function (e) {
        return $e.exp(e) - 1 / $e.exp(e) / 2;
      };
      var mt = function (e) {
        return ($e.exp(e) + 1 / $e.exp(e)) / 2;
      };
      var wt = function (e) {
        return $e.exp(e) - 1;
      };
      var bt = function (e) {
        return ($e.exp(2 * e) - 1) / ($e.exp(2 * e) + 1);
      };
      var gt = function (e) {
        return $e.log(1 + e);
      };
      function yt() {
        return {
          acos: Ye(0.12312423423423424),
          acosh: Ke(1e308),
          acoshPf: ft(1e154),
          asin: Xe(0.12312423423423424),
          asinh: Qe(1),
          asinhPf: ht(1),
          atanh: et(0.5),
          atanhPf: vt(0.5),
          atan: tt(0.5),
          sin: rt(-1e300),
          sinh: nt(1),
          sinhPf: pt(1),
          cos: it(10.000000000123),
          cosh: ot(1),
          coshPf: mt(1),
          tan: at(-1e300),
          tanh: st(1),
          tanhPf: bt(1),
          exp: ut(1),
          expm1: ct(1),
          expm1Pf: wt(1),
          log1p: lt(10),
          log1pPf: gt(10),
          powPI: dt(-100),
        };
      }
      var kt = 'mmMwWLliI0fiflO&1';
      var _t = {
        default: [],
        apple: [{ font: '-apple-system-body' }],
        serif: [{ fontFamily: 'serif' }],
        sans: [{ fontFamily: 'sans-serif' }],
        mono: [{ fontFamily: 'monospace' }],
        min: [{ fontSize: '1px' }],
        system: [{ fontFamily: 'system-ui' }],
      };
      function xt() {
        return St(function (e, t) {
          var r = {};
          var n = {};
          for (var i = 0, o = Object.keys(_t); i < o.length; i++) {
            var a = o[i];
            var s = _t[a],
              u = s[0],
              c = u === void 0 ? {} : u,
              l = s[1],
              d = l === void 0 ? kt : l;
            var f = e.createElement('span');
            f.textContent = d;
            f.style.whiteSpace = 'nowrap';
            for (var h = 0, v = Object.keys(c); h < v.length; h++) {
              var p = v[h];
              var m = c[p];
              if (m !== undefined) {
                f.style[p] = m;
              }
            }
            r[a] = f;
            t.appendChild(e.createElement('br'));
            t.appendChild(f);
          }
          for (var w = 0, b = Object.keys(_t); w < b.length; w++) {
            var a = b[w];
            n[a] = r[a].getBoundingClientRect().width;
          }
          return n;
        });
      }
      function St(a, s) {
        if (s === void 0) {
          s = 4e3;
        }
        return N(function (e, t) {
          var r = t.document;
          var n = r.body;
          var i = n.style;
          i.width = s + 'px';
          i.webkitTextSizeAdjust = i.textSizeAdjust = 'none';
          if (C()) {
            n.style.zoom = '' + 1 / t.devicePixelRatio;
          } else if (A()) {
            n.style.zoom = 'reset';
          }
          var o = r.createElement('div');
          o.textContent = (0, h.__spreadArrays)(Array((s / 20) << 0))
            .map(function () {
              return 'word';
            })
            .join(' ');
          n.appendChild(o);
          return a(r, n);
        }, '<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">');
      }
      var Ct = {
        fonts: W,
        domBlockers: Me,
        fontPreferences: xt,
        audio: B,
        screenFrame: he,
        osCpu: re,
        languages: ne,
        colorDepth: ie,
        deviceMemory: oe,
        screenResolution: ae,
        hardwareConcurrency: me,
        timezone: we,
        sessionStorage: ge,
        localStorage: ye,
        indexedDB: ke,
        openDatabase: _e,
        cpuClass: xe,
        platform: Se,
        plugins: J,
        canvas: $,
        touchSupport: te,
        vendor: Ce,
        vendorFlavors: Ae,
        cookiesEnabled: Te,
        colorGamut: Be,
        invertedColors: Le,
        forcedColors: De,
        monochrome: qe,
        contrast: Ge,
        reducedMotion: He,
        hdr: We,
        math: yt,
      };
      function At(e) {
        return e && typeof e === 'object' && 'message' in e ? e : { message: e };
      }
      function Tt(o, a) {
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var t, r, n;
          var i;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                r = Date.now();
                e.label = 1;
              case 1:
                e.trys.push([1, 3, , 4]);
                i = {};
                return [4, o(a)];
              case 2:
                t = ((i.value = e.sent()), i);
                return [3, 4];
              case 3:
                n = e.sent();
                t = { error: At(n) };
                return [3, 4];
              case 4:
                return [2, (0, h.__assign)((0, h.__assign)({}, t), { duration: Date.now() - r })];
            }
          });
        });
      }
      function Et(c, l, d) {
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          var n, i, o, a, t, r, s, u;
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                n = [];
                i = {};
                o = 16;
                a = Date.now();
                t = function (t) {
                  var r;
                  return (0, h.__generator)(this, function (e) {
                    switch (e.label) {
                      case 0:
                        if (!g(d, t)) {
                          return [2, 'continue'];
                        }
                        i[t] = undefined;
                        n.push(
                          Tt(c[t], l).then(function (e) {
                            i[t] = e;
                          })
                        );
                        r = Date.now();
                        if (!(r >= a + o)) return [3, 2];
                        a = r;
                        return [
                          4,
                          new Promise(function (e) {
                            return setTimeout(e);
                          }),
                        ];
                      case 1:
                        e.sent();
                        return [3, 4];
                      case 2:
                        return [4, undefined];
                      case 3:
                        e.sent();
                        e.label = 4;
                      case 4:
                        return [2];
                    }
                  });
                };
                (r = 0), (s = Object.keys(c));
                e.label = 1;
              case 1:
                if (!(r < s.length)) return [3, 4];
                u = s[r];
                return [5, t(u)];
              case 2:
                e.sent();
                e.label = 3;
              case 3:
                r++;
                return [3, 1];
              case 4:
                return [4, Promise.all(n)];
              case 5:
                e.sent();
                return [2, i];
            }
          });
        });
      }
      function Ot(e) {
        return Et(Ct, e, []);
      }
      function Mt(e) {
        var t = '';
        for (var r = 0, n = Object.keys(e).sort(); r < n.length; r++) {
          var i = n[r];
          var o = e[i];
          var a = o.error ? 'error' : JSON.stringify(o.value);
          t += '' + (t ? '|' : '') + i.replace(/([:|\\])/g, '\\$1') + ':' + a;
        }
        return t;
      }
      function Pt(e) {
        return JSON.stringify(
          e,
          function (e, t) {
            if (t instanceof Error) {
              return a(t);
            }
            return t;
          },
          2
        );
      }
      function jt(e) {
        return n(Mt(e));
      }
      function It(e) {
        var t;
        return {
          components: e,
          get visitorId() {
            if (t === undefined) {
              t = jt(this.components);
            }
            return t;
          },
          set visitorId(e) {
            t = e;
          },
          version: i,
        };
      }
      var Rt = (function () {
        function e() {
          de();
        }
        e.prototype.get = function (n) {
          if (n === void 0) {
            n = {};
          }
          return (0, h.__awaiter)(this, void 0, void 0, function () {
            var t, r;
            return (0, h.__generator)(this, function (e) {
              switch (e.label) {
                case 0:
                  return [4, Ot(n)];
                case 1:
                  t = e.sent();
                  r = It(t);
                  if (n.debug) {
                    console.log(
                      'Copy the text below to get the debug data:\n\n```\nversion: ' +
                        r.version +
                        '\nuserAgent: ' +
                        navigator.userAgent +
                        '\ngetOptions: ' +
                        JSON.stringify(n, undefined, 2) +
                        '\nvisitorId: ' +
                        r.visitorId +
                        '\ncomponents: ' +
                        Pt(t) +
                        '\n```'
                    );
                  }
                  return [2, r];
              }
            });
          });
        };
        return e;
      })();
      function Bt(e) {
        var t = (e === void 0 ? {} : e).delayFallback,
          r = t === void 0 ? 50 : t;
        return (0, h.__awaiter)(this, void 0, void 0, function () {
          return (0, h.__generator)(this, function (e) {
            switch (e.label) {
              case 0:
                return [4, o(r, r * 2)];
              case 1:
                e.sent();
                return [2, new Rt()];
            }
          });
        });
      }
      var Lt = { load: Bt, hashComponents: jt, componentsToDebugString: Pt };
      var zt = n;
      const Dt = Lt;
    },
    780: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: true });
      t.ipLocationServices = t.requestInterval = t.idleTimeout = t.api = void 0;
      t.api = { send: 'https://adat.' + window.location.hostname.replace(/^www\./, '') + '/api/user-tracking/create' };
      t.idleTimeout = 3e3;
      t.requestInterval = 30 * 60 * 1e3;
      t.ipLocationServices = [
        'https://ipinfo.io/json',
        'https://ipgeolocation.abstractapi.com/v1/?api_key=********************************',
        'https://geolocation-db.com/json',
      ];
    },
    696: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: true });
      t.idleMeasure = t.IdleHandler = void 0;
      var r = (function () {
        function e(e) {
          this.timerRunning = false;
          this.observers = [];
          this.timeout = e;
          this.active = false;
          this.waiting();
          this.init();
        }
        Object.defineProperty(e.prototype, 'active', {
          get: function () {
            return this.currentlyActive;
          },
          set: function (e) {
            this.currentlyActive = e;
            this.notify(e);
          },
          enumerable: false,
          configurable: true,
        });
        e.prototype.init = function () {
          var t = this;
          ['mousemove', 'mousedown', 'wheel', 'keydown', 'touchstart', 'touchmove'].forEach(function (e) {
            document.addEventListener(e, function (e) {
              if (!t.active) {
                t.active = true;
              }
              clearTimeout(t.timer);
              t.timerRunning = false;
              t.waiting();
            });
          });
        };
        e.prototype.waiting = function () {
          var e = this;
          if (!this.timerRunning) {
            this.timerRunning = true;
            this.timer = setTimeout(function () {
              e.active = false;
            }, this.timeout);
          }
        };
        e.prototype.notify = function (t) {
          this.observers.forEach(function (e) {
            if (typeof e === 'function') {
              e(t);
            }
          });
        };
        e.prototype.subscribe = function (e) {
          this.observers.push(e);
          e(this.active);
          return this.observers.length - 1;
        };
        e.prototype.unsubscribe = function (e) {
          this.observers[e] = undefined;
        };
        return e;
      })();
      t.IdleHandler = r;
      var n = function (e) {
        var o = { base: null, periods: [] };
        var a = new r(e);
        var s = a.subscribe(function (e) {
          if (o.periods.length === 0) {
            o.base = Date.now();
          }
          o.periods.push({ timeElapsed: Date.now() - o.base, active: e });
        });
        return function () {
          var e = 0;
          var t = 0;
          o.periods.push({ timeElapsed: Date.now() - o.base, active: null });
          for (var r = 0; r < o.periods.length; r++) {
            var n = o.periods[r];
            var i = o.periods[r + 1];
            if (i) {
              if (n.active) {
                e += i.timeElapsed - n.timeElapsed;
              } else {
                t += i.timeElapsed - n.timeElapsed;
              }
            }
          }
          a.unsubscribe(s);
          return { active: e, idle: t };
        };
      };
      t.idleMeasure = n;
    },
    760: (e, s, t) => {
      'use strict';
      Object.defineProperty(s, '__esModule', { value: true });
      s.getFingerprint =
        s.getLocation =
        s.fallbackRequest =
        s.tryRequest =
        s.promiseSequence =
        s.putCurrentState =
        s.isDoNotTrack =
        s.isUserAgentBot =
        s.getCookies =
        s.getCurrentDate =
        s.delay =
          void 0;
      var u = t(655);
      var r = t(936);
      var n = function (t, r) {
        if (r === void 0) {
          r = null;
        }
        return new Promise(function (e) {
          window.setTimeout(function () {
            e(r);
          }, t);
        });
      };
      s.delay = n;
      var i = function () {
        var e = new Date();
        var t = function (e, t) {
          if (t === void 0) {
            t = 2;
          }
          return String(e).padStart(t, '0');
        };
        return {
          Y: String(e.getFullYear()),
          m: t(e.getMonth() + 1),
          d: t(e.getDate()),
          H: t(e.getHours()),
          i: t(e.getMinutes()),
          s: t(e.getSeconds()),
          ms: t(e.getMilliseconds(), 3),
        };
      };
      s.getCurrentDate = i;
      var o = function () {
        var e = document.cookie
          .split('; ')
          .map(function (e) {
            return e.split('=');
          })
          .reduce(function (e, t) {
            var r;
            return u.__assign(u.__assign({}, e), ((r = {}), (r[t[0]] = t[1]), r));
          }, {});
        return e;
      };
      s.getCookies = o;
      var a = function (e) {
        return /((?:google|bing|msn)bot(?:-[imagevdo]{5})?)\/([\w.]+)/i.test(e);
      };
      s.isUserAgentBot = a;
      var c = function () {
        return navigator.doNotTrack === '1' || window.doNotTrack === '1';
      };
      s.isDoNotTrack = c;
      var l = function (e, t, r) {
        e.loads.push({ activeTime: t, inactiveTime: r, pageTitle: document.title, requestUrl: location.href, referrerUrl: window.top.document.referrer });
        e.userIsCookieEnabled.push(navigator.cookieEnabled);
        e.userIsDoNotTrack.push(s.isDoNotTrack());
      };
      s.putCurrentState = l;
      var d = function (e) {
        return e.reduce(function (e, t) {
          return e.then(function (e) {
            return t(e);
          });
        }, Promise.resolve());
      };
      s.promiseSequence = d;
      var f = function (e) {
        var t = e.times,
          r = e.url,
          n = e.options,
          i = n === void 0 ? {} : n;
        var o = function (e) {
          if (e.ok) {
            return e;
          } else {
            return fetch(r, i);
          }
        };
        return s
          .promiseSequence(
            u.__spreadArray(
              [
                function () {
                  return fetch(r, i);
                },
              ],
              u.__read(new Array(t - 1).fill(o))
            )
          )
          .then(function (e) {
            if (e.ok) {
              return e;
            } else {
              throw e;
            }
          });
      };
      s.tryRequest = f;
      var h = function (e) {
        var r = e.urls,
          t = e.options,
          n = t === void 0 ? {} : t,
          i = e.mapping,
          a = i === void 0 ? [] : i;
        var o = function (t) {
          return function (e) {
            if (e.ok) {
              return e.json().then(function (i) {
                if (a[t - 1]) {
                  var o = {};
                  Object.entries(a[t - 1]).forEach(function (e) {
                    var t = u.__read(e, 2),
                      r = t[0],
                      n = t[1];
                    o[n] = i[r];
                  });
                  return o;
                } else {
                  return i;
                }
              });
            } else {
              return fetch(r[t], n);
            }
          };
        };
        return s
          .promiseSequence(
            u.__spreadArray(
              [
                function () {
                  return fetch(r[0], n);
                },
              ],
              u.__read(
                new Array(r.length - 1).map(function (e, t) {
                  return o(t);
                })
              )
            )
          )
          .then(function (e) {
            if (e.ok) {
              return e.json();
            } else {
              throw e;
            }
          });
      };
      s.fallbackRequest = h;
      var v = function () {
        return new Promise(function (t, r) {
          navigator.geolocation.getCurrentPosition(
            function (e) {
              t({ error: null, latitude: e.coords.latitude, longitude: e.coords.longitude });
            },
            function (e) {
              r({ error: e, latitude: null, longitude: null });
            }
          );
        });
      };
      s.getLocation = v;
      var p = function () {
        return r.default
          .load()
          .then(function (e) {
            return e.get({ debug: false });
          })
          .then(function (e) {
            return e.visitorId;
          })
          .catch(function (e) {
            console.warn('Fingerprint error', e);
            return '';
          });
      };
      s.getFingerprint = p;
    },
    655: (e, t, r) => {
      'use strict';
      r.r(t);
      r.d(t, {
        __extends: () => i,
        __assign: () => o,
        __rest: () => a,
        __decorate: () => s,
        __param: () => u,
        __metadata: () => c,
        __awaiter: () => l,
        __generator: () => d,
        __createBinding: () => f,
        __exportStar: () => h,
        __values: () => v,
        __read: () => p,
        __spread: () => m,
        __spreadArrays: () => w,
        __spreadArray: () => b,
        __await: () => g,
        __asyncGenerator: () => y,
        __asyncDelegator: () => k,
        __asyncValues: () => _,
        __makeTemplateObject: () => x,
        __importStar: () => C,
        __importDefault: () => A,
        __classPrivateFieldGet: () => T,
        __classPrivateFieldSet: () => E,
      });
      /*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ var n = function (e, t) {
        n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (e, t) {
              e.__proto__ = t;
            }) ||
          function (e, t) {
            for (var r in t) if (Object.prototype.hasOwnProperty.call(t, r)) e[r] = t[r];
          };
        return n(e, t);
      };
      function i(e, t) {
        if (typeof t !== 'function' && t !== null) throw new TypeError('Class extends value ' + String(t) + ' is not a constructor or null');
        n(e, t);
        function r() {
          this.constructor = e;
        }
        e.prototype = t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
      }
      var o = function () {
        o =
          Object.assign ||
          function e(t) {
            for (var r, n = 1, i = arguments.length; n < i; n++) {
              r = arguments[n];
              for (var o in r) if (Object.prototype.hasOwnProperty.call(r, o)) t[o] = r[o];
            }
            return t;
          };
        return o.apply(this, arguments);
      };
      function a(e, t) {
        var r = {};
        for (var n in e) if (Object.prototype.hasOwnProperty.call(e, n) && t.indexOf(n) < 0) r[n] = e[n];
        if (e != null && typeof Object.getOwnPropertySymbols === 'function')
          for (var i = 0, n = Object.getOwnPropertySymbols(e); i < n.length; i++) {
            if (t.indexOf(n[i]) < 0 && Object.prototype.propertyIsEnumerable.call(e, n[i])) r[n[i]] = e[n[i]];
          }
        return r;
      }
      function s(e, t, r, n) {
        var i = arguments.length,
          o = i < 3 ? t : n === null ? (n = Object.getOwnPropertyDescriptor(t, r)) : n,
          a;
        if (typeof Reflect === 'object' && typeof Reflect.decorate === 'function') o = Reflect.decorate(e, t, r, n);
        else for (var s = e.length - 1; s >= 0; s--) if ((a = e[s])) o = (i < 3 ? a(o) : i > 3 ? a(t, r, o) : a(t, r)) || o;
        return i > 3 && o && Object.defineProperty(t, r, o), o;
      }
      function u(r, n) {
        return function (e, t) {
          n(e, t, r);
        };
      }
      function c(e, t) {
        if (typeof Reflect === 'object' && typeof Reflect.metadata === 'function') return Reflect.metadata(e, t);
      }
      function l(e, a, r, s) {
        function u(t) {
          return t instanceof r
            ? t
            : new r(function (e) {
                e(t);
              });
        }
        return new (r || (r = Promise))(function (t, r) {
          function n(e) {
            try {
              o(s.next(e));
            } catch (e) {
              r(e);
            }
          }
          function i(e) {
            try {
              o(s['throw'](e));
            } catch (e) {
              r(e);
            }
          }
          function o(e) {
            e.done ? t(e.value) : u(e.value).then(n, i);
          }
          o((s = s.apply(e, a || [])).next());
        });
      }
      function d(e, r) {
        var n = {
            label: 0,
            sent: function () {
              if (a[0] & 1) throw a[1];
              return a[1];
            },
            trys: [],
            ops: [],
          },
          i,
          o,
          a,
          t;
        return (
          (t = { next: s(0), throw: s(1), return: s(2) }),
          typeof Symbol === 'function' &&
            (t[Symbol.iterator] = function () {
              return this;
            }),
          t
        );
        function s(t) {
          return function (e) {
            return u([t, e]);
          };
        }
        function u(t) {
          if (i) throw new TypeError('Generator is already executing.');
          while (n)
            try {
              if (
                ((i = 1), o && (a = t[0] & 2 ? o['return'] : t[0] ? o['throw'] || ((a = o['return']) && a.call(o), 0) : o.next) && !(a = a.call(o, t[1])).done)
              )
                return a;
              if (((o = 0), a)) t = [t[0] & 2, a.value];
              switch (t[0]) {
                case 0:
                case 1:
                  a = t;
                  break;
                case 4:
                  n.label++;
                  return { value: t[1], done: false };
                case 5:
                  n.label++;
                  o = t[1];
                  t = [0];
                  continue;
                case 7:
                  t = n.ops.pop();
                  n.trys.pop();
                  continue;
                default:
                  if (!((a = n.trys), (a = a.length > 0 && a[a.length - 1])) && (t[0] === 6 || t[0] === 2)) {
                    n = 0;
                    continue;
                  }
                  if (t[0] === 3 && (!a || (t[1] > a[0] && t[1] < a[3]))) {
                    n.label = t[1];
                    break;
                  }
                  if (t[0] === 6 && n.label < a[1]) {
                    n.label = a[1];
                    a = t;
                    break;
                  }
                  if (a && n.label < a[2]) {
                    n.label = a[2];
                    n.ops.push(t);
                    break;
                  }
                  if (a[2]) n.ops.pop();
                  n.trys.pop();
                  continue;
              }
              t = r.call(e, n);
            } catch (e) {
              t = [6, e];
              o = 0;
            } finally {
              i = a = 0;
            }
          if (t[0] & 5) throw t[1];
          return { value: t[0] ? t[1] : void 0, done: true };
        }
      }
      var f = Object.create
        ? function (e, t, r, n) {
            if (n === undefined) n = r;
            Object.defineProperty(e, n, {
              enumerable: true,
              get: function () {
                return t[r];
              },
            });
          }
        : function (e, t, r, n) {
            if (n === undefined) n = r;
            e[n] = t[r];
          };
      function h(e, t) {
        for (var r in e) if (r !== 'default' && !Object.prototype.hasOwnProperty.call(t, r)) f(t, e, r);
      }
      function v(e) {
        var t = typeof Symbol === 'function' && Symbol.iterator,
          r = t && e[t],
          n = 0;
        if (r) return r.call(e);
        if (e && typeof e.length === 'number')
          return {
            next: function () {
              if (e && n >= e.length) e = void 0;
              return { value: e && e[n++], done: !e };
            },
          };
        throw new TypeError(t ? 'Object is not iterable.' : 'Symbol.iterator is not defined.');
      }
      function p(e, t) {
        var r = typeof Symbol === 'function' && e[Symbol.iterator];
        if (!r) return e;
        var n = r.call(e),
          i,
          o = [],
          a;
        try {
          while ((t === void 0 || t-- > 0) && !(i = n.next()).done) o.push(i.value);
        } catch (e) {
          a = { error: e };
        } finally {
          try {
            if (i && !i.done && (r = n['return'])) r.call(n);
          } finally {
            if (a) throw a.error;
          }
        }
        return o;
      }
      function m() {
        for (var e = [], t = 0; t < arguments.length; t++) e = e.concat(p(arguments[t]));
        return e;
      }
      function w() {
        for (var e = 0, t = 0, r = arguments.length; t < r; t++) e += arguments[t].length;
        for (var n = Array(e), i = 0, t = 0; t < r; t++) for (var o = arguments[t], a = 0, s = o.length; a < s; a++, i++) n[i] = o[a];
        return n;
      }
      function b(e, t) {
        for (var r = 0, n = t.length, i = e.length; r < n; r++, i++) e[i] = t[r];
        return e;
      }
      function g(e) {
        return this instanceof g ? ((this.v = e), this) : new g(e);
      }
      function y(e, t, r) {
        if (!Symbol.asyncIterator) throw new TypeError('Symbol.asyncIterator is not defined.');
        var i = r.apply(e, t || []),
          o,
          a = [];
        return (
          (o = {}),
          n('next'),
          n('throw'),
          n('return'),
          (o[Symbol.asyncIterator] = function () {
            return this;
          }),
          o
        );
        function n(n) {
          if (i[n])
            o[n] = function (r) {
              return new Promise(function (e, t) {
                a.push([n, r, e, t]) > 1 || s(n, r);
              });
            };
        }
        function s(e, t) {
          try {
            u(i[e](t));
          } catch (e) {
            d(a[0][3], e);
          }
        }
        function u(e) {
          e.value instanceof g ? Promise.resolve(e.value.v).then(c, l) : d(a[0][2], e);
        }
        function c(e) {
          s('next', e);
        }
        function l(e) {
          s('throw', e);
        }
        function d(e, t) {
          if ((e(t), a.shift(), a.length)) s(a[0][0], a[0][1]);
        }
      }
      function k(n) {
        var e, i;
        return (
          (e = {}),
          t('next'),
          t('throw', function (e) {
            throw e;
          }),
          t('return'),
          (e[Symbol.iterator] = function () {
            return this;
          }),
          e
        );
        function t(t, r) {
          e[t] = n[t]
            ? function (e) {
                return (i = !i) ? { value: g(n[t](e)), done: t === 'return' } : r ? r(e) : e;
              }
            : r;
        }
      }
      function _(i) {
        if (!Symbol.asyncIterator) throw new TypeError('Symbol.asyncIterator is not defined.');
        var e = i[Symbol.asyncIterator],
          t;
        return e
          ? e.call(i)
          : ((i = typeof v === 'function' ? v(i) : i[Symbol.iterator]()),
            (t = {}),
            r('next'),
            r('throw'),
            r('return'),
            (t[Symbol.asyncIterator] = function () {
              return this;
            }),
            t);
        function r(n) {
          t[n] =
            i[n] &&
            function (r) {
              return new Promise(function (e, t) {
                (r = i[n](r)), o(e, t, r.done, r.value);
              });
            };
        }
        function o(t, e, r, n) {
          Promise.resolve(n).then(function (e) {
            t({ value: e, done: r });
          }, e);
        }
      }
      function x(e, t) {
        if (Object.defineProperty) {
          Object.defineProperty(e, 'raw', { value: t });
        } else {
          e.raw = t;
        }
        return e;
      }
      var S = Object.create
        ? function (e, t) {
            Object.defineProperty(e, 'default', { enumerable: true, value: t });
          }
        : function (e, t) {
            e['default'] = t;
          };
      function C(e) {
        if (e && e.__esModule) return e;
        var t = {};
        if (e != null) for (var r in e) if (r !== 'default' && Object.prototype.hasOwnProperty.call(e, r)) f(t, e, r);
        S(t, e);
        return t;
      }
      function A(e) {
        return e && e.__esModule ? e : { default: e };
      }
      function T(e, t, r, n) {
        if (r === 'a' && !n) throw new TypeError('Private accessor was defined without a getter');
        if (typeof t === 'function' ? e !== t || !n : !t.has(e))
          throw new TypeError('Cannot read private member from an object whose class did not declare it');
        return r === 'm' ? n : r === 'a' ? n.call(e) : n ? n.value : t.get(e);
      }
      function E(e, t, r, n, i) {
        if (n === 'm') throw new TypeError('Private method is not writable');
        if (n === 'a' && !i) throw new TypeError('Private accessor was defined without a setter');
        if (typeof t === 'function' ? e !== t || !i : !t.has(e)) throw new TypeError('Cannot write private member to an object whose class did not declare it');
        return n === 'a' ? i.call(e, r) : i ? (i.value = r) : t.set(e, r), r;
      }
    },
    238: function (M, P, j) {
      var I;
      /*!@license
       * UAParser.js v0.7.28
       * Lightweight JavaScript-based User-Agent string parser
       * https://github.com/faisalman/ua-parser-js
       *
       * Copyright ÂŠ 2012-2021 Faisal Salman <<EMAIL>>
       * Licensed under MIT License
       */
      (function (i, d) {
        'use strict';
        var e = '0.7.28',
          o = '',
          a = '?',
          f = 'function',
          r = 'undefined',
          h = 'object',
          s = 'string',
          t = 'major',
          n = 'model',
          u = 'name',
          c = 'type',
          l = 'vendor',
          v = 'version',
          p = 'architecture',
          m = 'console',
          w = 'mobile',
          b = 'tablet',
          g = 'smarttv',
          y = 'wearable',
          k = 'embedded',
          _ = 255;
        var x = {
          extend: function (e, t) {
            var r = {};
            for (var n in e) {
              if (t[n] && t[n].length % 2 === 0) {
                r[n] = t[n].concat(e[n]);
              } else {
                r[n] = e[n];
              }
            }
            return r;
          },
          has: function (e, t) {
            return typeof e === s ? t.toLowerCase().indexOf(e.toLowerCase()) !== -1 : false;
          },
          lowerize: function (e) {
            return e.toLowerCase();
          },
          major: function (e) {
            return typeof e === s ? e.replace(/[^\d\.]/g, '').split('.')[0] : d;
          },
          trim: function (e, t) {
            e = e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');
            return typeof t === r ? e : e.substring(0, _);
          },
        };
        var S = {
          rgx: function (e, t) {
            var r = 0,
              n,
              i,
              o,
              a,
              s,
              u;
            while (r < t.length && !s) {
              var c = t[r],
                l = t[r + 1];
              n = i = 0;
              while (n < c.length && !s) {
                s = c[n++].exec(e);
                if (!!s) {
                  for (o = 0; o < l.length; o++) {
                    u = s[++i];
                    a = l[o];
                    if (typeof a === h && a.length > 0) {
                      if (a.length == 2) {
                        if (typeof a[1] == f) {
                          this[a[0]] = a[1].call(this, u);
                        } else {
                          this[a[0]] = a[1];
                        }
                      } else if (a.length == 3) {
                        if (typeof a[1] === f && !(a[1].exec && a[1].test)) {
                          this[a[0]] = u ? a[1].call(this, u, a[2]) : d;
                        } else {
                          this[a[0]] = u ? u.replace(a[1], a[2]) : d;
                        }
                      } else if (a.length == 4) {
                        this[a[0]] = u ? a[3].call(this, u.replace(a[1], a[2])) : d;
                      }
                    } else {
                      this[a] = u ? u : d;
                    }
                  }
                }
              }
              r += 2;
            }
          },
          str: function (e, t) {
            for (var r in t) {
              if (typeof t[r] === h && t[r].length > 0) {
                for (var n = 0; n < t[r].length; n++) {
                  if (x.has(t[r][n], e)) {
                    return r === a ? d : r;
                  }
                }
              } else if (x.has(t[r], e)) {
                return r === a ? d : r;
              }
            }
            return e;
          },
        };
        var C = {
          browser: {
            oldSafari: { version: { '1.0': '/8', 1.2: '/1', 1.3: '/3', '2.0': '/412', '2.0.2': '/416', '2.0.3': '/417', '2.0.4': '/419', '?': '/' } },
            oldEdge: { version: { 0.1: '12.', 21: '13.', 31: '14.', 39: '15.', 41: '16.', 42: '17.', 44: '18.' } },
          },
          os: {
            windows: {
              version: {
                ME: '4.90',
                'NT 3.11': 'NT3.51',
                'NT 4.0': 'NT4.0',
                2e3: 'NT 5.0',
                XP: ['NT 5.1', 'NT 5.2'],
                Vista: 'NT 6.0',
                7: 'NT 6.1',
                8: 'NT 6.2',
                8.1: 'NT 6.3',
                10: ['NT 6.4', 'NT 10.0'],
                RT: 'ARM',
              },
            },
          },
        };
        var A = {
          browser: [
            [/\b(?:crmo|crios)\/([\w\.]+)/i],
            [v, [u, 'Chrome']],
            [/edg(?:e|ios|a)?\/([\w\.]+)/i],
            [v, [u, 'Edge']],
            [/(opera\smini)\/([\w\.-]+)/i, /(opera\s[mobiletab]{3,6})\b.+version\/([\w\.-]+)/i, /(opera)(?:.+version\/|[\/\s]+)([\w\.]+)/i],
            [u, v],
            [/opios[\/\s]+([\w\.]+)/i],
            [v, [u, 'Opera Mini']],
            [/\sopr\/([\w\.]+)/i],
            [v, [u, 'Opera']],
            [
              /(kindle)\/([\w\.]+)/i,
              /(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,
              /(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,
              /(ba?idubrowser)[\/\s]?([\w\.]+)/i,
              /(?:ms|\()(ie)\s([\w\.]+)/i,
              /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,
              /(rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i,
              /(weibo)__([\d\.]+)/i,
            ],
            [u, v],
            [/(?:[\s\/]uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],
            [v, [u, 'UCBrowser']],
            [/(?:windowswechat)?\sqbcore\/([\w\.]+)\b.*(?:windowswechat)?/i],
            [v, [u, 'WeChat(Win) Desktop']],
            [/micromessenger\/([\w\.]+)/i],
            [v, [u, 'WeChat']],
            [/konqueror\/([\w\.]+)/i],
            [v, [u, 'Konqueror']],
            [/trident.+rv[:\s]([\w\.]{1,9})\b.+like\sgecko/i],
            [v, [u, 'IE']],
            [/yabrowser\/([\w\.]+)/i],
            [v, [u, 'Yandex']],
            [/(avast|avg)\/([\w\.]+)/i],
            [[u, /(.+)/, '$1 Secure Browser'], v],
            [/focus\/([\w\.]+)/i],
            [v, [u, 'Firefox Focus']],
            [/opt\/([\w\.]+)/i],
            [v, [u, 'Opera Touch']],
            [/coc_coc_browser\/([\w\.]+)/i],
            [v, [u, 'Coc Coc']],
            [/dolfin\/([\w\.]+)/i],
            [v, [u, 'Dolphin']],
            [/coast\/([\w\.]+)/i],
            [v, [u, 'Opera Coast']],
            [/xiaomi\/miuibrowser\/([\w\.]+)/i],
            [v, [u, 'MIUI Browser']],
            [/fxios\/([\w\.-]+)/i],
            [v, [u, 'Firefox']],
            [/(qihu|qhbrowser|qihoobrowser|360browser)/i],
            [[u, '360 Browser']],
            [/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],
            [[u, /(.+)/, '$1 Browser'], v],
            [/(comodo_dragon)\/([\w\.]+)/i],
            [[u, /_/g, ' '], v],
            [
              /\s(electron)\/([\w\.]+)\ssafari/i,
              /(tesla)(?:\sqtcarbrowser|\/(20[12]\d\.[\w\.-]+))/i,
              /m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i,
            ],
            [u, v],
            [/(MetaSr)[\/\s]?([\w\.]+)/i, /(LBBROWSER)/i],
            [u],
            [/;fbav\/([\w\.]+);/i],
            [v, [u, 'Facebook']],
            [/FBAN\/FBIOS|FB_IAB\/FB4A/i],
            [[u, 'Facebook']],
            [/safari\s(line)\/([\w\.]+)/i, /\b(line)\/([\w\.]+)\/iab/i, /(chromium|instagram)[\/\s]([\w\.-]+)/i],
            [u, v],
            [/\bgsa\/([\w\.]+)\s.*safari\//i],
            [v, [u, 'GSA']],
            [/headlesschrome(?:\/([\w\.]+)|\s)/i],
            [v, [u, 'Chrome Headless']],
            [/\swv\).+(chrome)\/([\w\.]+)/i],
            [[u, 'Chrome WebView'], v],
            [/droid.+\sversion\/([\w\.]+)\b.+(?:mobile\ssafari|safari)/i],
            [v, [u, 'Android Browser']],
            [/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],
            [u, v],
            [/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],
            [v, [u, 'Mobile Safari']],
            [/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],
            [v, u],
            [/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],
            [u, [v, S.str, C.browser.oldSafari.version]],
            [/(webkit|khtml)\/([\w\.]+)/i],
            [u, v],
            [/(navigator|netscape)\/([\w\.-]+)/i],
            [[u, 'Netscape'], v],
            [/ile\svr;\srv:([\w\.]+)\).+firefox/i],
            [v, [u, 'Firefox Reality']],
            [
              /ekiohf.+(flow)\/([\w\.]+)/i,
              /(swiftfox)/i,
              /(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,
              /(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,
              /(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,
              /(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,
              /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,
              /(links)\s\(([\w\.]+)/i,
              /(gobrowser)\/?([\w\.]*)/i,
              /(ice\s?browser)\/v?([\w\._]+)/i,
              /(mosaic)[\/\s]([\w\.]+)/i,
            ],
            [u, v],
          ],
          cpu: [
            [/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],
            [[p, 'amd64']],
            [/(ia32(?=;))/i],
            [[p, x.lowerize]],
            [/((?:i[346]|x)86)[;\)]/i],
            [[p, 'ia32']],
            [/\b(aarch64|armv?8e?l?)\b/i],
            [[p, 'arm64']],
            [/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],
            [[p, 'armhf']],
            [/windows\s(ce|mobile);\sppc;/i],
            [[p, 'arm']],
            [/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],
            [[p, /ower/, '', x.lowerize]],
            [/(sun4\w)[;\)]/i],
            [[p, 'sparc']],
            [/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?:64|(?=v(?:[1-7]|[5-7]1)l?|;|eabi))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],
            [[p, x.lowerize]],
          ],
          device: [
            [/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus\s10)/i],
            [n, [l, 'Samsung'], [c, b]],
            [/\b((?:s[cgp]h|gt|sm)-\w+|galaxy\snexus)/i, /\ssamsung[\s-]([\w-]+)/i, /sec-(sgh\w+)/i],
            [n, [l, 'Samsung'], [c, w]],
            [/\((ip(?:hone|od)[\s\w]*);/i],
            [n, [l, 'Apple'], [c, w]],
            [/\((ipad);[\w\s\),;-]+apple/i, /applecoremedia\/[\w\.]+\s\((ipad)/i, /\b(ipad)\d\d?,\d\d?[;\]].+ios/i],
            [n, [l, 'Apple'], [c, b]],
            [/\b((?:agr|ags[23]|bah2?|sht?)-a?[lw]\d{2})/i],
            [n, [l, 'Huawei'], [c, b]],
            [
              /d\/huawei([\w\s-]+)[;\)]/i,
              /\b(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?|ele-l\d\d)/i,
              /\b(\w{2,4}-[atu][ln][01259][019])[;\)\s]/i,
            ],
            [n, [l, 'Huawei'], [c, w]],
            [
              /\b(poco[\s\w]+)(?:\sbuild|\))/i,
              /\b;\s(\w+)\sbuild\/hm\1/i,
              /\b(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,
              /\b(redmi[\s\-_]?(?:note|k)?[\w\s_]+)(?:\sbuild|\))/i,
              /\b(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i,
            ],
            [
              [n, /_/g, ' '],
              [l, 'Xiaomi'],
              [c, w],
            ],
            [/\b(mi[\s\-_]?(?:pad)(?:[\w\s_]+))(?:\sbuild|\))/i],
            [
              [n, /_/g, ' '],
              [l, 'Xiaomi'],
              [c, b],
            ],
            [/;\s(\w+)\sbuild.+\soppo/i, /\s(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],
            [n, [l, 'OPPO'], [c, w]],
            [/\svivo\s(\w+)(?:\sbuild|\))/i, /\s(v[12]\d{3}\w?[at])(?:\sbuild|;)/i],
            [n, [l, 'Vivo'], [c, w]],
            [/\s(rmx[12]\d{3})(?:\sbuild|;)/i],
            [n, [l, 'Realme'], [c, w]],
            [
              /\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)\b[\w\s]+build\//i,
              /\smot(?:orola)?[\s-](\w*)/i,
              /((?:moto[\s\w\(\)]+|xt\d{3,4}|nexus\s6)(?=\sbuild|\)))/i,
            ],
            [n, [l, 'Motorola'], [c, w]],
            [/\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],
            [n, [l, 'Motorola'], [c, b]],
            [/((?=lg)?[vl]k\-?\d{3})\sbuild|\s3\.[\s\w;-]{10}lg?-([06cv9]{3,4})/i],
            [n, [l, 'LG'], [c, b]],
            [/(lm-?f100[nv]?|nexus\s[45])/i, /lg[e;\s\/-]+((?!browser|netcast)\w+)/i, /\blg(\-?[\d\w]+)\sbuild/i],
            [n, [l, 'LG'], [c, w]],
            [/(ideatab[\w\-\s]+)/i, /lenovo\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|yt[\d\w-]{6}|tb[\d\w-]{6})/i],
            [n, [l, 'Lenovo'], [c, b]],
            [/(?:maemo|nokia).*(n900|lumia\s\d+)/i, /nokia[\s_-]?([\w\.-]*)/i],
            [
              [n, /_/g, ' '],
              [l, 'Nokia'],
              [c, w],
            ],
            [/droid.+;\s(pixel\sc)[\s)]/i],
            [n, [l, 'Google'], [c, b]],
            [/droid.+;\s(pixel[\s\daxl]{0,6})(?:\sbuild|\))/i],
            [n, [l, 'Google'], [c, w]],
            [/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],
            [n, [l, 'Sony'], [c, w]],
            [/sony\stablet\s[ps]\sbuild\//i, /(?:sony)?sgp\w+(?:\sbuild\/|\))/i],
            [
              [n, 'Xperia Tablet'],
              [l, 'Sony'],
              [c, b],
            ],
            [/\s(kb2005|in20[12]5|be20[12][59])\b/i, /\ba000(1)\sbuild/i, /\boneplus\s(a\d{4})[\s)]/i],
            [n, [l, 'OnePlus'], [c, w]],
            [/(alexa)webm/i, /(kf[a-z]{2}wi)(\sbuild\/|\))/i, /(kf[a-z]+)(\sbuild\/|\)).+silk\//i],
            [n, [l, 'Amazon'], [c, b]],
            [/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],
            [
              [n, 'Fire Phone'],
              [l, 'Amazon'],
              [c, w],
            ],
            [/\((playbook);[\w\s\),;-]+(rim)/i],
            [n, l, [c, b]],
            [/((?:bb[a-f]|st[hv])100-\d)/i, /\(bb10;\s(\w+)/i],
            [n, [l, 'BlackBerry'], [c, w]],
            [/(?:\b|asus_)(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus\s7|padfone|p00[cj])/i],
            [n, [l, 'ASUS'], [c, b]],
            [/\s(z[es]6[027][01][km][ls]|zenfone\s\d\w?)\b/i],
            [n, [l, 'ASUS'], [c, w]],
            [/(nexus\s9)/i],
            [n, [l, 'HTC'], [c, b]],
            [/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i, /(zte)-(\w*)/i, /(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],
            [l, [n, /_/g, ' '], [c, w]],
            [/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],
            [n, [l, 'Acer'], [c, b]],
            [/droid.+;\s(m[1-5]\snote)\sbuild/i, /\bmz-([\w-]{2,})/i],
            [n, [l, 'Meizu'], [c, w]],
            [
              /(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,
              /(hp)\s([\w\s]+\w)/i,
              /(asus)-?(\w+)/i,
              /(microsoft);\s(lumia[\s\w]+)/i,
              /(lenovo)[_\s-]?([\w-]+)/i,
              /linux;.+(jolla);/i,
              /droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i,
            ],
            [l, n, [c, w]],
            [
              /(archos)\s(gamepad2?)/i,
              /(hp).+(touchpad(?!.+tablet)|tablet)/i,
              /(kindle)\/([\w\.]+)/i,
              /\s(nook)[\w\s]+build\/(\w+)/i,
              /(dell)\s(strea[kpr\s\d]*[\dko])/i,
              /[;\/]\s?(le[\s\-]+pan)[\s\-]+(\w{1,9})\sbuild/i,
              /[;\/]\s?(trinity)[\-\s]*(t\d{3})\sbuild/i,
              /\b(gigaset)[\s\-]+(q\w{1,9})\sbuild/i,
              /\b(vodafone)\s([\w\s]+)(?:\)|\sbuild)/i,
            ],
            [l, n, [c, b]],
            [/\s(surface\sduo)\s/i],
            [n, [l, 'Microsoft'], [c, b]],
            [/droid\s[\d\.]+;\s(fp\du?)\sbuild/i],
            [n, [l, 'Fairphone'], [c, w]],
            [/\s(u304aa)\sbuild/i],
            [n, [l, 'AT&T'], [c, w]],
            [/sie-(\w*)/i],
            [n, [l, 'Siemens'], [c, w]],
            [/[;\/]\s?(rct\w+)\sbuild/i],
            [n, [l, 'RCA'], [c, b]],
            [/[;\/\s](venue[\d\s]{2,7})\sbuild/i],
            [n, [l, 'Dell'], [c, b]],
            [/[;\/]\s?(q(?:mv|ta)\w+)\sbuild/i],
            [n, [l, 'Verizon'], [c, b]],
            [/[;\/]\s(?:barnes[&\s]+noble\s|bn[rt])([\w\s\+]*)\sbuild/i],
            [n, [l, 'Barnes & Noble'], [c, b]],
            [/[;\/]\s(tm\d{3}\w+)\sbuild/i],
            [n, [l, 'NuVision'], [c, b]],
            [/;\s(k88)\sbuild/i],
            [n, [l, 'ZTE'], [c, b]],
            [/;\s(nx\d{3}j)\sbuild/i],
            [n, [l, 'ZTE'], [c, w]],
            [/[;\/]\s?(gen\d{3})\sbuild.*49h/i],
            [n, [l, 'Swiss'], [c, w]],
            [/[;\/]\s?(zur\d{3})\sbuild/i],
            [n, [l, 'Swiss'], [c, b]],
            [/[;\/]\s?((zeki)?tb.*\b)\sbuild/i],
            [n, [l, 'Zeki'], [c, b]],
            [/[;\/]\s([yr]\d{2})\sbuild/i, /[;\/]\s(dragon[\-\s]+touch\s|dt)(\w{5})\sbuild/i],
            [[l, 'Dragon Touch'], n, [c, b]],
            [/[;\/]\s?(ns-?\w{0,9})\sbuild/i],
            [n, [l, 'Insignia'], [c, b]],
            [/[;\/]\s?((nxa|Next)-?\w{0,9})\sbuild/i],
            [n, [l, 'NextBook'], [c, b]],
            [/[;\/]\s?(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05]))\sbuild/i],
            [[l, 'Voice'], n, [c, w]],
            [/[;\/]\s?(lvtel\-)?(v1[12])\sbuild/i],
            [[l, 'LvTel'], n, [c, w]],
            [/;\s(ph-1)\s/i],
            [n, [l, 'Essential'], [c, w]],
            [/[;\/]\s?(v(100md|700na|7011|917g).*\b)\sbuild/i],
            [n, [l, 'Envizen'], [c, b]],
            [/[;\/]\s?(trio[\s\w\-\.]+)\sbuild/i],
            [n, [l, 'MachSpeed'], [c, b]],
            [/[;\/]\s?tu_(1491)\sbuild/i],
            [n, [l, 'Rotor'], [c, b]],
            [/(shield[\w\s]+)\sbuild/i],
            [n, [l, 'Nvidia'], [c, b]],
            [/(sprint)\s(\w+)/i],
            [l, n, [c, w]],
            [/(kin\.[onetw]{3})/i],
            [
              [n, /\./g, ' '],
              [l, 'Microsoft'],
              [c, w],
            ],
            [/droid\s[\d\.]+;\s(cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],
            [n, [l, 'Zebra'], [c, b]],
            [/droid\s[\d\.]+;\s(ec30|ps20|tc[2-8]\d[kx])\)/i],
            [n, [l, 'Zebra'], [c, w]],
            [/\s(ouya)\s/i, /(nintendo)\s([wids3utch]+)/i],
            [l, n, [c, m]],
            [/droid.+;\s(shield)\sbuild/i],
            [n, [l, 'Nvidia'], [c, m]],
            [/(playstation\s[345portablevi]+)/i],
            [n, [l, 'Sony'], [c, m]],
            [/[\s\(;](xbox(?:\sone)?(?!;\sxbox))[\s\);]/i],
            [n, [l, 'Microsoft'], [c, m]],
            [/smart-tv.+(samsung)/i],
            [l, [c, g]],
            [/hbbtv.+maple;(\d+)/i],
            [
              [n, /^/, 'SmartTV'],
              [l, 'Samsung'],
              [c, g],
            ],
            [/(?:linux;\snetcast.+smarttv|lg\snetcast\.tv-201\d)/i],
            [
              [l, 'LG'],
              [c, g],
            ],
            [/(apple)\s?tv/i],
            [l, [n, 'Apple TV'], [c, g]],
            [/crkey/i],
            [
              [n, 'Chromecast'],
              [l, 'Google'],
              [c, g],
            ],
            [/droid.+aft([\w])(\sbuild\/|\))/i],
            [n, [l, 'Amazon'], [c, g]],
            [/\(dtv[\);].+(aquos)/i],
            [n, [l, 'Sharp'], [c, g]],
            [/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],
            [
              [l, x.trim],
              [n, x.trim],
              [c, g],
            ],
            [/[\s\/\(](android\s|smart[-\s]?|opera\s)tv[;\)\s]/i],
            [[c, g]],
            [/((pebble))app\/[\d\.]+\s/i],
            [l, n, [c, y]],
            [/droid.+;\s(glass)\s\d/i],
            [n, [l, 'Google'], [c, y]],
            [/droid\s[\d\.]+;\s(wt63?0{2,3})\)/i],
            [n, [l, 'Zebra'], [c, y]],
            [/(tesla)(?:\sqtcarbrowser|\/20[12]\d\.[\w\.-]+)/i],
            [l, [c, k]],
            [/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],
            [n, [c, w]],
            [/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],
            [n, [c, b]],
            [/\s(tablet|tab)[;\/]/i, /\s(mobile)(?:[;\/]|\ssafari)/i],
            [[c, x.lowerize]],
            [/(android[\w\.\s\-]{0,9});.+build/i],
            [n, [l, 'Generic']],
            [/(phone)/i],
            [[c, w]],
          ],
          engine: [
            [/windows.+\sedge\/([\w\.]+)/i],
            [v, [u, 'EdgeHTML']],
            [/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],
            [v, [u, 'Blink']],
            [
              /(presto)\/([\w\.]+)/i,
              /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,
              /ekioh(flow)\/([\w\.]+)/i,
              /(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,
              /(icab)[\/\s]([23]\.[\d\.]+)/i,
            ],
            [u, v],
            [/rv\:([\w\.]{1,9})\b.+(gecko)/i],
            [v, u],
          ],
          os: [
            [/microsoft\s(windows)\s(vista|xp)/i],
            [u, v],
            [/(windows)\snt\s6\.2;\s(arm)/i, /(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i, /(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)(?!.+xbox)/i],
            [u, [v, S.str, C.os.windows.version]],
            [/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],
            [
              [u, 'Windows'],
              [v, S.str, C.os.windows.version],
            ],
            [/ip[honead]{2,4}\b(?:.*os\s([\w]+)\slike\smac|;\sopera)/i, /cfnetwork\/.+darwin/i],
            [
              [v, /_/g, '.'],
              [u, 'iOS'],
            ],
            [/(mac\sos\sx)\s?([\w\s\.]*)/i, /(macintosh|mac(?=_powerpc)\s)(?!.+haiku)/i],
            [
              [u, 'Mac OS'],
              [v, /_/g, '.'],
            ],
            [
              /(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i,
              /(blackberry)\w*\/([\w\.]*)/i,
              /(tizen|kaios)[\/\s]([\w\.]+)/i,
              /\((series40);/i,
            ],
            [u, v],
            [/\(bb(10);/i],
            [v, [u, 'BlackBerry']],
            [/(?:symbian\s?os|symbos|s60(?=;)|series60)[\/\s-]?([\w\.]*)/i],
            [v, [u, 'Symbian']],
            [/mozilla.+\(mobile;.+gecko.+firefox/i],
            [[u, 'Firefox OS']],
            [/web0s;.+rt(tv)/i, /\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],
            [v, [u, 'webOS']],
            [/crkey\/([\d\.]+)/i],
            [v, [u, 'Chromecast']],
            [/(cros)\s[\w]+\s([\w\.]+\w)/i],
            [[u, 'Chromium OS'], v],
            [
              /(nintendo|playstation)\s([wids345portablevuch]+)/i,
              /(xbox);\s+xbox\s([^\);]+)/i,
              /(mint)[\/\s\(\)]?(\w*)/i,
              /(mageia|vectorlinux)[;\s]/i,
              /(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?=\slinux)|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus|raspbian)(?:\sgnu\/linux)?(?:\slinux)?[\/\s-]?(?!chrom|package)([\w\.-]*)/i,
              /(hurd|linux)\s?([\w\.]*)/i,
              /(gnu)\s?([\w\.]*)/i,
              /\s([frentopc-]{0,4}bsd|dragonfly)\s?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,
              /(haiku)\s(\w+)/i,
            ],
            [u, v],
            [/(sunos)\s?([\w\.\d]*)/i],
            [[u, 'Solaris'], v],
            [
              /((?:open)?solaris)[\/\s-]?([\w\.]*)/i,
              /(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,
              /(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,
              /(unix)\s?([\w\.]*)/i,
            ],
            [u, v],
          ],
        };
        var T = function (e, t) {
          if (typeof e === 'object') {
            t = e;
            e = d;
          }
          if (!(this instanceof T)) {
            return new T(e, t).getResult();
          }
          var r = e || (typeof i !== 'undefined' && i.navigator && i.navigator.userAgent ? i.navigator.userAgent : o);
          var n = t ? x.extend(A, t) : A;
          this.getBrowser = function () {
            var e = { name: d, version: d };
            S.rgx.call(e, r, n.browser);
            e.major = x.major(e.version);
            return e;
          };
          this.getCPU = function () {
            var e = { architecture: d };
            S.rgx.call(e, r, n.cpu);
            return e;
          };
          this.getDevice = function () {
            var e = { vendor: d, model: d, type: d };
            S.rgx.call(e, r, n.device);
            return e;
          };
          this.getEngine = function () {
            var e = { name: d, version: d };
            S.rgx.call(e, r, n.engine);
            return e;
          };
          this.getOS = function () {
            var e = { name: d, version: d };
            S.rgx.call(e, r, n.os);
            return e;
          };
          this.getResult = function () {
            return { ua: this.getUA(), browser: this.getBrowser(), engine: this.getEngine(), os: this.getOS(), device: this.getDevice(), cpu: this.getCPU() };
          };
          this.getUA = function () {
            return r;
          };
          this.setUA = function (e) {
            r = typeof e === s && e.length > _ ? x.trim(e, _) : e;
            return this;
          };
          this.setUA(r);
          return this;
        };
        T.VERSION = e;
        T.BROWSER = { NAME: u, MAJOR: t, VERSION: v };
        T.CPU = { ARCHITECTURE: p };
        T.DEVICE = { MODEL: n, VENDOR: l, TYPE: c, CONSOLE: m, MOBILE: w, SMARTTV: g, TABLET: b, WEARABLE: y, EMBEDDED: k };
        T.ENGINE = { NAME: u, VERSION: v };
        T.OS = { NAME: u, VERSION: v };
        if (typeof P !== r) {
          if ('object' !== r && M.exports) {
            P = M.exports = T;
          }
          P.UAParser = T;
        } else {
          if (true) {
            !((I = function () {
              return T;
            }.call(P, j, P, M)),
            I !== d && (M.exports = I));
          } else {
          }
        }
        var E = typeof i !== 'undefined' && (i.jQuery || i.Zepto);
        if (E && !E.ua) {
          var O = new T();
          E.ua = O.getResult();
          E.ua.get = function () {
            return O.getUA();
          };
          E.ua.set = function (e) {
            O.setUA(e);
            var t = O.getResult();
            for (var r in t) {
              E.ua[r] = t[r];
            }
          };
        }
      })(typeof window === 'object' ? window : this);
    },
  };
  var i = {};
  function f(e) {
    var t = i[e];
    if (t !== undefined) {
      return t.exports;
    }
    var r = (i[e] = { exports: {} });
    n[e].call(r.exports, r, r.exports, f);
    return r.exports;
  }
  (() => {
    f.d = (e, t) => {
      for (var r in t) {
        if (f.o(t, r) && !f.o(e, r)) {
          Object.defineProperty(e, r, { enumerable: true, get: t[r] });
        }
      }
    };
  })();
  (() => {
    f.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t);
  })();
  (() => {
    f.r = (e) => {
      if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
        Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' });
      }
      Object.defineProperty(e, '__esModule', { value: true });
    };
  })();
  var h = {};
  (() => {
    'use strict';
    var e = h;
    var t;
    t = { value: true };
    var r = f(238);
    var n = f(760);
    var i = f(696);
    var o = f(780);
    var a = n.getCurrentDate();
    var s = r();
    var u = function (e) {
      if (e && e.ok) {
        return e.json();
      }
    };
    var c = function (t) {
      return function (e) {
        console.warn('Location error ' + (t + 1) + ' (' + o.ipLocationServices[t] + ')', e);
      };
    };
    var l = {
      eventTime: a.Y + '-' + a.m + '-' + a.d + ' ' + a.H + ':' + a.i + ':' + a.s + '.' + a.ms,
      userId: null,
      loads: [{ activeTime: null, inactiveTime: null, pageTitle: document.title, requestUrl: location.href, referrerUrl: window.top.document.referrer }],
      userIsCookieEnabled: [navigator.cookieEnabled],
      userIsDoNotTrack: [n.isDoNotTrack()],
      googleCookiesGa: n.getCookies()._ga,
      locationCountry: null,
      locationCity: null,
      locationGeo: null,
      deviceUserAgent: s.ua,
      deviceType: s.device.type || 'desktop',
      deviceIsApp: false,
      deviceisAppOrRobot: n.isUserAgentBot(s.ua),
      deviceModelName: s.device.model,
      deviceOsName: s.os.name,
      deviceBrowserName: s.browser.name,
      userLanguage: navigator.language,
      userTimezoneOffset: new Date().getTimezoneOffset(),
    };
    n.getLocation()
      .then(function (e) {
        if (!e.error) {
          l.locationGeo = e.latitude + ', ' + e.longitude;
        }
      })
      .then(function () {
        return fetch(o.ipLocationServices[0]);
      })
      .then(u)
      .catch(c(0))
      .then(function (e) {
        if (e) {
          l.locationCountry = e.country;
          l.locationCity = e.city;
          if (!l.locationGeo) {
            l.locationGeo = e.latitude + ', ' + e.longitude;
          }
        } else {
          return fetch(o.ipLocationServices[1]);
        }
      })
      .then(u)
      .catch(c(1))
      .then(function (e) {
        if (e) {
          l.locationCountry = e.country;
          l.locationCity = e.city;
          if (!l.locationGeo) {
            l.locationGeo = e.loc;
          }
        } else {
          return fetch(o.ipLocationServices[2]);
        }
      })
      .then(u)
      .catch(c(2))
      .then(function (e) {
        if (e) {
          l.locationCountry = e.country_name;
          l.locationCity = e.city;
          if (!l.locationGeo) {
            l.locationGeo = e.latitude + ', ' + e.longitude;
          }
        }
      })
      .then(function () {
        return n.getFingerprint();
      })
      .then(function (e) {
        l.userId = e;
      })
      .catch(function (e) {
        console.warn('Fingerprint error', e);
      });
    var d = i.idleMeasure(o.idleTimeout);
    setInterval(function () {
      var e = d(),
        t = e.active,
        r = e.idle;
      d = i.idleMeasure(o.idleTimeout);
      n.putCurrentState(l, t, r);
      n.tryRequest({ times: 3, url: o.api.send, options: { method: 'POST', mode: 'cors', credentials: 'omit', body: JSON.stringify(l) } })
        .then(function (e) {
          l.loads = [];
          l.userIsCookieEnabled = [];
          l.userIsDoNotTrack = [];
        })
        .catch(function (e) {
          console.warn('Send error', e);
        });
    }, o.requestInterval);
    window.addEventListener('popstate', function () {
      var e = d(),
        t = e.active,
        r = e.idle;
      d = i.idleMeasure(o.idleTimeout);
      n.getFingerprint().then(function (e) {
        l.userId = e;
        n.putCurrentState(l, t, r);
      });
    });
    window.addEventListener(
      'unload',
      function () {
        var e = d(),
          t = e.active,
          r = e.idle;
        n.putCurrentState(l, t, r);
        navigator.sendBeacon(o.api.send, JSON.stringify(l));
      },
      { capture: true }
    );
  })();
})();
//# sourceMappingURL=index.min.js.map
