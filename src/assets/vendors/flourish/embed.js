/*!
Copyright 2016-2021 Kiln Enterprises Ltd

This file may be freely used, and distributed without modification.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND <PERSON>ITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
OF THE POSSIBILITY OF SUCH DAMAGE.
*/
(window.FlourishConfig = { app_url: 'https://app.flourish.studio', public_url: 'https://public.flourish.studio/', embeds_url: 'https://flo.uri.sh/' }),
  (function () {
    'use strict';
    var t,
      i,
      e = !1;
    function r(t) {
      if (e && window.top !== window.self) {
        var i = window;
        'srcdoc' === i.location.pathname && (i = i.parent);
        var r,
          o =
            ((r = {}),
            window._Flourish_template_id && (r.template_id = window._Flourish_template_id),
            window.Flourish && window.Flourish.app && window.Flourish.app.loaded_template_id && (r.template_id = window.Flourish.app.loaded_template_id),
            window._Flourish_visualisation_id && (r.visualisation_id = window._Flourish_visualisation_id),
            window.Flourish &&
              window.Flourish.app &&
              window.Flourish.app.loaded_visualisation &&
              (r.visualisation_id = window.Flourish.app.loaded_visualisation.id),
            window.Flourish &&
              window.Flourish.app &&
              window.Flourish.app.story &&
              ((r.story_id = window.Flourish.app.story.id), (r.slide_count = window.Flourish.app.story.slides.length)),
            window.Flourish && window.Flourish.app && window.Flourish.app.current_slide && (r.slide_index = window.Flourish.app.current_slide.index + 1),
            r),
          a = { sender: 'Flourish', method: 'customerAnalytics' };
        for (var s in o) o.hasOwnProperty(s) && (a[s] = o[s]);
        for (var s in t) t.hasOwnProperty(s) && (a[s] = t[s]);
        i.parent.postMessage(JSON.stringify(a), '*');
      }
    }
    function o(t) {
      if ('function' != typeof t) throw new Error('Analytics callback is not a function');
      window.Flourish._analytics_listeners.push(t);
    }
    function a(t) {
      if ('function' != typeof t) throw new Error('Analytics callback is not a function');
      window.Flourish._analytics_listeners = window.Flourish._analytics_listeners.filter(function (i) {
        return t !== i;
      });
    }
    function s() {
      e = !0;
      [
        { event_name: 'click', action_name: 'click', use_capture: !0 },
        { event_name: 'keydown', action_name: 'key_down', use_capture: !0 },
        { event_name: 'mouseenter', action_name: 'mouse_enter', use_capture: !1 },
        { event_name: 'mouseleave', action_name: 'mouse_leave', use_capture: !1 },
      ].forEach(function (t) {
        document.body.addEventListener(
          t.event_name,
          function () {
            r({ action: t.action_name });
          },
          t.use_capture
        );
      });
    }
    function n() {
      if (null == t) {
        var i = (function () {
          var t = window.location;
          'about:srcdoc' == t.href && (t = window.parent.location);
          var i = {};
          return (
            (function (t, e, r) {
              for (; (r = e.exec(t)); ) i[decodeURIComponent(r[1])] = decodeURIComponent(r[2]);
            })(t.search.substring(1).replace(/\+/g, '%20'), /([^&=]+)=?([^&]*)/g),
            i
          );
        })();
        t = 'referrer' in i ? /^https:\/\/medium.com\//.test(i.referrer) : !('auto' in i);
      }
      return t;
    }
    function l(t) {
      var i = t || window.innerWidth;
      return i > 999 ? 650 : i > 599 ? 575 : 400;
    }
    function u(t, e) {
      if (window.top !== window.self) {
        var r = window;
        if (('srcdoc' == r.location.pathname && (r = r.parent), i))
          return (t = parseInt(t, 10)), void r.parent.postMessage({ sentinel: 'amp', type: 'embed-size', height: t }, '*');
        var o = { sender: 'Flourish', context: 'iframe.resize', method: 'resize', height: t, src: r.location.toString() };
        if (e) for (var a in e) o[a] = e[a];
        r.parent.postMessage(JSON.stringify(o), '*');
      }
    }
    function d() {
      return (-1 !== navigator.userAgent.indexOf('Safari') || -1 !== navigator.userAgent.indexOf('iPhone')) && -1 == navigator.userAgent.indexOf('Chrome');
    }
    function h(t) {
      window.addEventListener('message', function (i) {
        if (
          null != i.source &&
          (i.origin === document.location.origin ||
            i.origin.match(/\/\/localhost:\d+$|\/\/flourish-api\.com$|\.flourish\.(?:local(:\d+)?|net|rocks|studio)$|\.uri\.sh$/))
        ) {
          var e;
          try {
            e = JSON.parse(i.data);
          } catch (t) {
            return void console.warn('Unexpected non-JSON message: ' + JSON.stringify(i.data));
          }
          if ('Flourish' === e.sender) {
            for (var r = document.querySelectorAll('iframe'), o = 0; o < r.length; o++)
              if (r[o].contentWindow == i.source || r[o].contentWindow == i.source.parent) return void t(e, r[o]);
            console.warn('could not find frame', e);
          }
        }
      }),
        d() && (window.addEventListener('resize', c), c());
    }
    function c() {
      for (var t = document.querySelectorAll('.flourish-embed'), i = 0; i < t.length; i++) {
        var e = t[i];
        if (!e.getAttribute('data-width')) {
          var r = e.querySelector('iframe');
          if (r) {
            var o = window.getComputedStyle(e),
              a = e.offsetWidth - parseFloat(o.paddingLeft) - parseFloat(o.paddingRight);
            r.style.width = a + 'px';
          }
        }
      }
    }
    function p(t, i, e, r, o) {
      var a = document.createElement('iframe');
      if (
        (a.setAttribute('scrolling', 'no'),
        a.setAttribute('frameborder', '0'),
        a.setAttribute('title', 'Interactive or visual content'),
        a.setAttribute(
          'sandbox',
          'allow-same-origin allow-forms allow-scripts allow-downloads allow-popups allow-popups-to-escape-sandbox allow-top-navigation-by-user-activation'
        ),
        i.appendChild(a),
        a.offsetParent || 'fixed' === getComputedStyle(a).position)
      )
        f(t, i, a, e, r, o);
      else {
        var s = { embed_url: t, container: i, iframe: a, width: e, height: r, play_on_load: o };
        if ((window._flourish_poll_items ? window._flourish_poll_items.push(s) : (window._flourish_poll_items = [s]), window._flourish_poll_items.length > 1))
          return;
        var n = setInterval(function () {
          (window._flourish_poll_items = window._flourish_poll_items.filter(function (t) {
            return !t.iframe.offsetParent || (f(t.embed_url, t.container, t.iframe, t.width, t.height, t.play_on_load), !1);
          })),
            window._flourish_poll_items.length || clearInterval(n);
        }, 500);
      }
    }
    function f(t, i, e, r, o, a) {
      var s;
      return (
        r && 'number' == typeof r
          ? ((s = r), (r += 'px'))
          : r &&
            r.match(
              /^[ \t\r\n\f]*([+-]?\d+|\d*\.\d+(?:[eE][+-]?\d+)?)(?:\\?[Pp]|\\0{0,4}[57]0(?:\r\n|[ \t\r\n\f])?)(?:\\?[Xx]|\\0{0,4}[57]8(?:\r\n|[ \t\r\n\f])?)[ \t\r\n\f]*$/
            ) &&
            (s = parseFloat(r)),
        o && 'number' == typeof o && (o += 'px'),
        r ? (e.style.width = r) : d() ? (e.style.width = i.offsetWidth + 'px') : (e.style.width = '100%'),
        !!o || (t.match(/\?/) ? (t += '&auto=1') : (t += '?auto=1'), (o = l(s || e.offsetWidth) + 'px')),
        o && ('%' === o.charAt(o.length - 1) && (o = (parseFloat(o) / 100) * i.parentNode.offsetHeight + 'px'), (e.style.height = o)),
        e.setAttribute('src', t + (a ? '#play-on-load' : '')),
        e
      );
    }
    var m = {
      de: { credits: { default: 'Erstellt mit Flourish' } },
      en: {
        credits: {
          default: 'A Flourish data visualization',
          chart: 'A Flourish chart',
          map: { text: 'A Flourish map', url: 'https://flourish.studio/visualisations/maps/' },
          survey: { text: 'A Flourish survey visualization', url: 'https://flourish.studio/visualisations/survey-data/' },
          network: { text: 'A Flourish network chart', url: 'https://flourish.studio/visualisations/network-charts/' },
          scatter: { text: 'A Flourish scatter chart', url: 'https://flourish.studio/visualisations/scatter-charts/' },
          sankey: { text: 'A Flourish sankey chart', url: 'https://flourish.studio/visualisations/sankey-charts/' },
          quiz: 'A Flourish quiz',
          bar_race: { text: 'A Flourish bar chart race', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
          'bar-chart-race': { text: 'A Flourish bar chart race', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
          cards: 'Interactive content by Flourish',
          chord: 'A Flourish chord diagram',
          election: 'A Flourish election chart',
          globe: { text: 'A Flourish connections globe', url: 'https://flourish.studio/visualisations/maps/' },
          hierarchy: { text: 'A Flourish hierarchy chart', url: 'https://flourish.studio/visualisations/treemaps/' },
          'line-chart-race': 'A Flourish line chart race',
          parliament: 'A Flourish election chart',
          'photo-slider': 'Interactive content by Flourish',
          slope: { text: 'A Flourish slope chart', url: 'https://flourish.studio/visualisations/slope-charts/' },
          sports: 'A Flourish sports visualization',
          explore: 'A Flourish data visualization',
          'word-cloud': 'A Flourish data visualization',
        },
      },
      es: {
        credits: {
          default: 'Creado con Flourish',
          bar_race: { text: 'Créé avec Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
          'bar-chart-race': { text: 'Créé avec Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
        },
      },
      fr: {
        credits: {
          default: 'Créé avec Flourish',
          bar_race: { text: 'Créé avec Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
          'bar-chart-race': { text: 'Créé avec Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
        },
      },
      it: {
        credits: {
          default: 'Creato con Flourish',
          bar_race: { text: 'Creato con Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
          'bar-chart-race': { text: 'Creato con Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
        },
      },
      mi: {
        credits: {
          default: 'Hangaia ki te Flourish',
          bar_race: { text: 'Hangaia ki te Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
          'bar-chart-race': { text: 'Hangaia ki te Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
        },
      },
      nl: {
        credits: {
          default: 'Gemaakt met Flourish',
          bar_race: { text: 'Gemaakt met Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
          'bar-chart-race': { text: 'Gemaakt met Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
        },
      },
      pt: {
        default: 'Feito com Flourish',
        bar_race: { text: 'Feito com Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
        'bar-chart-race': { text: 'Feito com Flourish', url: 'https://flourish.studio/visualisations/bar-chart-race/' },
      },
    };
    !(function () {
      var t = [];
      function e() {
        if (!window.FlourishLoaded) {
          window.FlourishLoaded = !0;
          var e = window.Flourish && window.Flourish.external,
            a = 'en',
            c = document.querySelector("meta[name='language'],meta[name='LANGUAGE']");
          c && c.hasAttribute('content') && ((a = c.getAttribute('content').substr(0, 2)), m[a] || (a = 'en'));
          var f,
            w = Array.prototype.slice.call(document.querySelectorAll('.flourish-embed'));
          if (!e)
            for (var v = document.querySelectorAll('script'), b = 0; b < v.length; b++) {
              var _ = v[b];
              if (_.src && _.src.match(/(?:\.flourish\.(?:net|rocks|studio)|(?:localhost|flourish\.local)(?::\d+)?)\//g) && _.src.match(/\/embed\.js$/)) {
                if (!f) {
                  var g = _.getAttribute('src');
                  f = g.substr(0, g.lastIndexOf('/resources')) + '/';
                }
                if (_.getAttribute('data-src')) {
                  var F = document.createElement('div');
                  F.setAttribute('class', 'flourish-embed'),
                    F.setAttribute('data-src', _.getAttribute('data-src')),
                    _.getAttribute('data-height') && F.setAttribute('data-height', _.getAttribute('data-height')),
                    _.getAttribute('data-width') && F.setAttribute('data-width', _.getAttribute('data-width')),
                    _.parentNode.insertBefore(F, _),
                    w.push(F);
                }
              }
            }
          var y =
              ((i = '#amp=1' == window.location.hash),
              {
                createEmbedIframe: p,
                isFixedHeight: n,
                getHeightForBreakpoint: l,
                startEventListeners: h,
                notifyParentWindow: u,
                isSafari: d,
                initCustomerAnalytics: s,
                addAnalyticsListener: o,
                sendCustomerAnalyticsMessage: r,
              }),
            A = function (t) {
              var i,
                r,
                o,
                s = t.getAttribute('data-src');
              if (s) {
                var n,
                  l = s.split('?'),
                  u = l.length > 1 || (window.Flourish && window.Flourish.hide);
                if (
                  ((s = l[0]),
                  e
                    ? ((n = s), (i = window.FlourishConfig.public_url), (r = '?utm_source=embed&utm_campaign=' + s))
                    : ((n = (o = 0 === s.indexOf('template/'))
                        ? window.FlourishConfig.app_url + '/' + s + '/preview'
                        : window.FlourishConfig.embeds_url + s + '/embed'),
                      (i = f + s + '/'),
                      (r = '?utm_source=showcase&utm_campaign=' + s)),
                  y.createEmbedIframe(n, t, t.getAttribute('data-width'), t.getAttribute('data-height'), t.hasAttribute('data-play-on-load')),
                  !o && !u)
                ) {
                  for (var d = '', h = t.className.split(' '), c = 0; c < h.length; c++)
                    if ('flourish-embed' != h[c] && 0 == h[c].indexOf('flourish-')) {
                      d = h[c].substr(9);
                      break;
                    }
                  d || (d = 0 == s.indexOf('story') ? 'story' : 'default');
                  var p = (function (t, i) {
                      var e, r;
                      return (
                        (i = i || ''),
                        'object' == typeof (e = m[(t = t || 'en')].credits[i] || m.en.credits[i] || m.en.credits.default) &&
                          (e.url && (r = e.url), (e = e.text)),
                        { credit_text: e, credit_url: r }
                      );
                    })(a, d),
                    w = (function (t, i, e, r) {
                      (t = t || 'https://flourish.studio'),
                        (i = i || '?utm_source=api&utm_campaign=' + window.location.href),
                        (e = e || 'https://public.flourish.studio/'),
                        (r = r || 'A Flourish data visualisation');
                      var o = document.createElement('div');
                      o.setAttribute('class', 'flourish-credit'),
                        o.setAttribute(
                          'style',
                          'width:100%!important;margin:0 0 4px!important;text-align:right!important;font-family:Helvetica,sans-serif!important;color:#888!important;font-size:11px!important;font-weight:bold!important;font-style:normal!important;-webkit-font-smoothing:antialiased!important;box-shadow:none!important;'
                        );
                      var a = document.createElement('a');
                      a.setAttribute('href', t + i),
                        a.setAttribute('target', '_top'),
                        a.setAttribute(
                          'style',
                          'display:inline-block!important;text-decoration:none!important;font:inherit!important;color:inherit!important;border:none!important;margin:0 5px!important;box-shadow:none!important;'
                        ),
                        o.appendChild(a);
                      var s = document.createElement('img');
                      s.setAttribute('alt', 'Flourish logo'),
                        s.setAttribute('src', e + 'resources/bosh.svg'),
                        s.setAttribute(
                          'style',
                          'font:inherit!important;width:auto!important;height:12px!important;border:none!important;margin:0 2px 0!important;vertical-align:middle!important;display:inline-block!important;box-shadow:none!important;'
                        ),
                        a.appendChild(s);
                      var n = document.createElement('span');
                      return (
                        n.setAttribute(
                          'style',
                          'font:inherit!important;color:#888!important;vertical-align:middle!important;display:inline-block!important;box-shadow:none!important;'
                        ),
                        n.appendChild(document.createTextNode(r)),
                        a.appendChild(n),
                        o
                      );
                    })(p.credit_url || i, r, window.FlourishConfig.public_url, p.credit_text);
                  t.appendChild(w);
                }
              }
            };
          if (!window.Flourish || !window.Flourish.disable_autoload) for (b = 0; b < w.length; b++) A(w[b]);
          y.startEventListeners(function (t, i) {
            'resize' == t.method
              ? ('number' == typeof t.height && (t.height += 'px'), t.height && (i.style.height = t.height))
              : 'customerAnalytics' === t.method &&
                (delete t.method,
                delete t.sender,
                (function (t) {
                  window.Flourish &&
                    window.Flourish._analytics_listeners.forEach(function (i) {
                      i(t);
                    });
                })(t));
          });
          for (b = 0; b < t.length; b++) A(t[b]);
          window.Flourish && (window.Flourish.loadEmbed = A);
        }
      }
      window.Flourish &&
        ((window.Flourish.loadEmbed = function (i) {
          t.push(i);
        }),
        (window.Flourish.addAnalyticsListener = o),
        (window.Flourish.removeAnalyticsListener = a),
        window.Flourish._analytics_listeners || (window.Flourish._analytics_listeners = [])),
        'loading' === document.readyState ? document.addEventListener('DOMContentLoaded', e) : e();
    })();
  })();
//# sourceMappingURL=embed.js.map
