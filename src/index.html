<!doctype html>
<html lang="hu" data-sec="#ffcc00" data-prim="#cc00ff">
  <head>
    <base href="/" />
    <title><PERSON><PERSON><PERSON></title>
    <meta charset="utf-8" />
    <meta name="robots" content="index, follow" />
    <meta name="robots" content="max-image-preview:large" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <script class="structured-data" type="application/ld+json"></script>
    <link rel="icon" type="image/x-icon" href="favicon.ico?everything=42" />
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500&display=swap" rel="stylesheet" />

    <script>
      window.adocf = {
        useDOMContentLoaded: true,
      };

      window.PRE_adocean_queue = [];
      window.adOceanInited = false;

      window.adoQueueFn = (fn, args) => {
        console.log(' <> queuing ado call: %s', fn, args);
        window.PRE_adocean_queue.push({ fn, args });
      };

      window.googletag = window.googletag || { cmd: [] };
    </script>

    <script src="/assets/scripts/inmobi.js"></script>
    <!-- InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->
    <script type="text/javascript" async>
      // Make sure we call gemius init only after InMobi is loaded and inited
      console.log(' >> initializing InMobi ready callback for AdOcean');
      const inMobiReadyCallback = () => {
        console.log(' >> InMobi ready');

        if (!initAdOcean) {
          console.warn(' >> << no adocean init found');
        }
        !adOceanInited && initAdOcean && initAdOcean();
      };

      const detectDomain = () => {
        const domainParts = document.location.hostname.split('.');
        let site = domainParts.slice(-2).join('.');
        if (domainParts.includes('localhost') || domainParts.includes('local')) {
          site = localStorage.getItem('quantcast.megyei.domain') || 'feol.hu';
        } else if (domainParts.includes('dev')) {
          site = `${domainParts[0]}.hu`;
        } else if (domainParts.includes('apptest')) {
          site = domainParts[0].substring(0, domainParts[0].length - 2) + '.hu';
        }
        return 'www.' + site;
      };

      InMobiHandler.init('gq2uc_c-uMyQL', detectDomain(), inMobiReadyCallback);
    </script>
    <!-- End Quantcast Choice. Consent Manager Tag v2.0 (for TCF 2.0) -->

    <!-- AD OCEAN GEMIUS -->
    <script type="text/javascript" src="https://hu.adocean.pl/files/js/ado.js"></script>
    <script type="text/javascript">
      /* (c)AdOcean 2003-2020 */

      window.initAdOcean = () => {
        console.log(' >> init AdOcean');
        if (typeof ado !== 'object') {
          window.ado = {};
          window.ado.config = window.ado.preview = window.ado.placement = window.ado.master = window.ado.slave = function () {};
        }

        window.ado.config({
          mode: 'new',
          xml: false,
          consent: true,
          characterEncoding: true,
          attachReferrer: true,
          fpc: 'auto',
          defaultServer: 'hu.adocean.pl',
          cookieDomain: 'SLD',
        });

        window.ado.preview({ enabled: true });

        window.adOceanInited = true;
        window.PRE_adocean_queue.forEach(({ fn, args }) => {
          console.log(' >>> replaying adOcean queue: %s:', fn, args);
          window.ado[fn](...args);
        });
      };

      const HALF_MINUTE = 30000;

      window.setTimeout(() => {
        if (!window.adOceanInited && window.Ado) {
          console.warn(' >> GFC initialization timeout: activating AdOcean');
          window.initAdOcean();
        } else if (!window.Ado) {
          console.error(' <!> GFC initialization timeout: AdOcean is not loaded! Aborting');
        }
      }, HALF_MINUTE);
    </script>
    <script type="text/javascript" src="/assets/vendors/google/googlefc.js"></script>

    <script async crossorigin="anonymous" src="/assets/vendors/facebook/facebook.js" nonce="utPHfmeF"></script>
  </head>

  <body>
    <div id="init-loader" class="fullscreen-loader"></div>
    <app-root></app-root>
    <script src="/assets/scripts/init-loader.js"></script>
    <script defer src="/assets/scripts/version.js"></script>
  </body>
</html>
