@use 'shared' as *;
@use 'swiper/scss' as *;

body {
  background: $base-background-color;
}

a {
  &,
  &:visited {
    color: $base-text-color;
  }

  &:hover,
  html &:active {
    color: $blue;
  }
}

@include media-breakpoint-up(md) {
  .desktop-hidden {
    display: none !important;
  }
}

@include media-breakpoint-down(sm) {
  .mobile-hidden {
    display: none !important;
  }
}

@include media-breakpoint-up(md) {
  .tablet-hidden {
    display: none !important;
  }
}
