@use 'shared' as *;

section {
  .wrapper.with-aside {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 40px;

    > .left-column {
      display: block;
      width: calc(100% - #{$aside-width} - 40px);
      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    > aside {
      display: block;
      width: $aside-width;
      @include media-breakpoint-down(md) {
        width: 100%;
      }

      > * {
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }
      }
    }
  }
}
