@use 'variables' as *;

@mixin center-vertically {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Display block icon
@mixin icon($name) {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-image: url($images-path + $name);
}

// Background image
@mixin img($file) {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url($images-path + $file);
}

// IE10 IE11 only
@mixin ieonly() {
  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    @content;
  }
}

// Firefox only
@mixin ffonly() {
  @-moz-document url-prefix() {
    @content;
  }
}

// DOM element with fixed ratio - 100% width
@mixin imgRatio($x, $y) {
  display: block;
  width: 100%;
  padding-top: calc($y / $x) * 100%;
  background-position: center;
  background-size: cover;
  background-color: rgba(0, 0, 0, 0.1);
  filter: blur(0); // Prevents pixelation
  -ms-interpolation-mode: bicubic;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

@mixin imgZoom() {
  // HOVER
  background-position: center top;
  background-repeat: no-repeat;
  transition: all 0.15s ease-in-out;
  background-size: 104%;

  &:hover {
    background-size: 120%;
  }
  // HOVER
}

@mixin article-before-line {
  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 5px;
    height: 100%;
    background-color: $primary-color;
  }
}

@mixin article-before-title-style {
  min-height: 60px;
  padding-left: 25px;
  display: flex;
  align-items: center;
  position: relative;
  font-weight: 500;
  font-size: 20px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@mixin read-more-blue-arrow {
  font-weight: 500;
  font-size: 16px;
  color: $link-color;
  position: relative;
  padding-right: 30px;
  svg {
    width: 15px;
    height: 15px;

    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);

    path {
      stroke: #{$link-color};
    }
  }
}

@mixin clearfix {
  &:after {
    clear: both;
    content: '.';
    display: block;
    font-size: 0;
    height: 0;
    visibility: hidden;
  }
}
