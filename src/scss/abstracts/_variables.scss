// Images path
$images-path: '/assets/images/';

// Wrapper width
$global-wrapper-width: 1400px;

// Breakpoints
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
);

$grid-columns: 12;
$grid-gutter-width: 30px;

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
);

// Layout extension - wrapper with aside
$aside-width: 440px;
$block-bottom-margin: 50px;
$block-bottom-margin-mobile: 30px;
$mobile-old: 360px;

// Font Family
$font-family: 'Prompt', sans-serif;
$font-secondary: 'Montserrat', sans-serif;
$font-inter: 'Inter', sans-serif;
$font-zilla: 'Zilla Slab', serif;

// Colors
$black: #000000;
$grey-3: #3e3e3e;
$black-2: #5a5a5a;
$grey-5: #515151;
$grey-7: #5a5a5a;
$grey-11: #7f7f7f;
$grey-12: #818181;
$grey-13: #808080;
$grey-16: #c4c4c4;
$grey-17: #cfcfcf;
$grey-18: #b3b3b3;
$grey-19: #e5e5e5;
$grey-22: #eeeeee;
$grey-25: #f5f5f5;
$white: #ffffff;
$white-6: #e6e6e6;
$white-4: #f5f5f5;

// Brand colors (from \environments\portal-configs\) */
$primary-color: var(--primary-color);
$secondary-color: var(--secondary-color);
$link-color: var(--link-color);
$active-color: var(--active-color);
$logo-width: var(--logo-width);
$tag-color: var(--tag-color);
$tag-text-color: var(--tag-text-color);

$blue: #005ca2;
$green: #006d34;
$red: #a7111b;
$red-2: #da202d;

$base-text-color: $black;
$base-background-color: $white;

$socialcolor-twitter: #1eabf1;
$socialcolor-facebook: #1877f2;
$socialcolor-instagram: #ed4956;

$error-color: #ed4956;
$error-inputbg: #ffe7e7;

$brand-elections-blue-950: #1f284c;
$brand-elections-blue-900: #2d3f7b;
$brand-elections-blue-800: #32479b;
$brand-elections-blue-700: #3a5bc7;
$brand-elections-blue-600: #4069d0;
$brand-elections-blue-400: #0088ff;
$brand-elections-blue-50: #f1f6fd;
$brand-elections-bg-radial: radial-gradient(50% 50% at 50% 50%, #{$brand-elections-blue-800} 0%, #{$brand-elections-blue-950} 100%);
$brand-elections-bg-linear: linear-gradient(90deg, #{$brand-elections-blue-700}, #{$brand-elections-blue-400});
