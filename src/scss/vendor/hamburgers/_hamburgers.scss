/*!
 * Hamburgers
 * @description Tasty CSS-animated hamburgers
 * <AUTHOR> @jonsuh
 * @site https://jonsuh.com/hamburgers
 * @link https://github.com/jonsuh/hamburgers
 */

// Base Hamburger (We need this)
// ==================================================
@forward 'base';

// Hamburger types
// ==================================================
@forward 'types/3dx';
@forward 'types/3dx-r';
@forward 'types/3dy';
@forward 'types/3dy-r';
@forward 'types/3dxy';
@forward 'types/3dxy-r';
@forward 'types/arrow';
@forward 'types/arrow-r';
@forward 'types/arrowalt';
@forward 'types/arrowalt-r';
@forward 'types/arrowturn';
@forward 'types/arrowturn-r';
@forward 'types/boring';
@forward 'types/collapse';
@forward 'types/collapse-r';
@forward 'types/elastic';
@forward 'types/elastic-r';
@forward 'types/emphatic';
@forward 'types/emphatic-r';
@forward 'types/minus';
@forward 'types/slider';
@forward 'types/slider-r';
@forward 'types/spin';
@forward 'types/spin-r';
@forward 'types/spring';
@forward 'types/spring-r';
@forward 'types/stand';
@forward 'types/stand-r';
@forward 'types/squeeze';
@forward 'types/vortex';
@forward 'types/vortex-r';
