@use 'shared' as *;

.modal {
  --kui-modal-padding: 10px;

  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  z-index: 11;
  padding: var(--kui-modal-padding) 0;

  &-backdrop {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 10;
    background-color: rgba(0, 0, 0, 50%);
  }

  &-wrapper {
    color: $black;
    background: $white;
    border: 1px solid $grey-19;
    box-shadow: 0px 20px 50px rgba(0, 0, 0, 0.25);
    border-radius: 6px;
    max-width: 680px;
    max-height: calc(100vh - 2 * var(--kui-modal-padding));
    z-index: 12;
    position: relative;
  }

  &-body {
    margin: auto;
    padding: 0 20px;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 2 * var(--kui-modal-padding));
  }
}

html.modal-open,
body.modal-open {
  overflow: hidden;
}
