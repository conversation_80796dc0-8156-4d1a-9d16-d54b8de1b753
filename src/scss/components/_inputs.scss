@use 'shared' as *;

.custom-input {
  input[type='checkbox'],
  input[type='radio'] {
    position: absolute;
    opacity: 0;
    z-index: -1;
  }

  label {
    position: relative;
    display: inline-block;
    padding: 0 0 0 50px;
    cursor: pointer;
    color: $black;
    font-weight: 500;
    font-size: 12px;
    line-height: 18px;
  }

  label::before,
  label::after {
    position: absolute;
    top: calc(50% - 15px);
    left: 0;
    display: block;
    width: 30px;
    height: 30px;
    content: ' ';
  }

  label::before {
    content: ' ';
    background-color: $white;
    border: 1px solid $grey-16;
    border-radius: 6px;
  }

  /* Checkbox */
  input[type='checkbox'] + label::after {
    @include icon('icons/check-green.svg');
    background-size: 50%;
    content: ' ';
    border: 0;
    background-color: transparent;
    transform-origin: 10% center;
  }

  /* Radio */
  input[type='radio'] + label::before {
    border-radius: 50%;
  }

  input[type='radio'] + label::after {
    content: ' ';
    top: calc(50% - 9px);
    left: 0;
    width: 18px;
    height: 18px;
    background: $white;
    border: 1px solid $white;
    border-radius: 50%;
  }

  /* :checked */
  input[type='checkbox']:checked + label::before,
  input[type='radio']:checked + label::before {
    border-color: $grey-16;
  }

  input[type='checkbox'] + label::after,
  input[type='radio'] + label::after {
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
  }

  input[type='checkbox']:checked + label::after,
  input[type='radio']:checked + label::after {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }

  /* Transition */
  label::before,
  label::after {
    -webkit-transition: 0.25s all ease;
    -o-transition: 0.25s all ease;
    transition: 0.25s all ease;
  }
}

select {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0;
  border: 0;
  background: 0;
  font-size: 14px;
  font-style: normal;
  color: $grey-7;
  -webkit-appearance: none;
  appearance: none;

  &::-ms-expand {
    display: none;
  }
}

.custom-select-box {
  border: 1px solid $grey-16;
  border-radius: 6px;
  padding: 14px;
  margin-left: 15px;
  display: flex;

  .custom-select-label {
    font-size: 14px;
    line-height: 21px;
    color: $grey-7;
    margin-right: 10px;
    display: block;
  }

  .custom-select {
    font-weight: 600;
    padding: 0 17px 0 0;
    @include icon('/icons/menu-arrow.svg');
    background-size: 12px 12px;
    background-position: 100% 50%;

    option {
      padding: 0 5px;
    }
  }
}

.custom-select-box-flyer {
  display: flex;
  position: relative;
  @include media-breakpoint-down(sm) {
    margin: 0;
    width: 100%;
  }

  .custom-select-label-flyer {
    font-size: 14px;
    line-height: 21px;
    color: $grey-7;
    margin-right: 10px;
    display: block;
    position: absolute;
    pointer-events: none;
    margin-top: 15px;
    margin-left: 25px;
  }

  .custom-select-flyer {
    border: 1px solid $grey-16;
    padding: 15px;
    font-weight: 600;
    margin-left: 15px;
    background-size: 12px 12px;
    background-position: 100% 50%;
    padding-left: 90px;
    border-radius: 6px;
    padding-right: 25px;

    @include media-breakpoint-down(sm) {
      margin-left: unset;
      padding-left: 175px;
    }

    option {
      padding: 0 5px;
    }
  }

  .flyer-icon {
    @include icon('/icons/menu-arrow.svg');
    width: 10px;
    height: 10px;
    display: inline-block;
    position: absolute;
    right: 10px;
    margin-top: 20px;
    @include media-breakpoint-down(sm) {
      right: 0px;
      margin-right: 19px;
    }
  }
}

.search-input {
  border: 1px solid $grey-16;
  border-radius: 6px;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  color: $grey-7;

  .search-button {
    width: 24px;
    height: 24px;
    @include icon('icons/search.svg');
    margin-right: 10px;
    margin-left: -5px;
  }

  input[type='text'] {
    width: calc(100% - 43px);
  }

  .clear-button {
    width: 12px;
    height: 12px;
    @include icon('icons/x.svg');
  }
}

.form-block {
  .input-block-title {
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    color: $black;
    margin-bottom: 30px;
  }

  .input-wrapper {
    margin-bottom: 15px;

    .input-header {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 9px;

      label {
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        padding: 0 20px;
      }

      .error {
        padding: 0 20px;
        color: $error-color;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        display: none;
        transition: 0.3s ease all;
      }

      .info {
        padding: 0 20px;
        color: $grey-7;
        font-weight: 500;
        font-size: 12px;
        line-height: 18px;
      }
    }

    .error-mobile {
      display: none;
      width: 100%;
      padding: 10px 20px;
      color: $error-color;
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;
    }

    input[type='text'],
    input[type='password'],
    input[type='number'],
    input[type='email'],
    textarea {
      border: 1px solid $grey-16;
      border-radius: 6px;
      width: 100%;
      padding: 13px 20px;
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;
    }

    textarea {
      height: 200px;
    }

    &.error {
      input[type='text'],
      input[type='password'],
      input[type='number'],
      input[type='email'],
      textarea {
        background: $error-inputbg;
      }

      .input-header {
        .error {
          display: block;
        }
      }

      .error-mobile {
        @include media-breakpoint-down(sm) {
          display: block;
        }
      }
    }

    .file-drop {
      .ngx-file-drop__drop-zone {
        background: $grey-25;
        border: 1px dashed $grey-16;
        border-radius: 6px;
        padding: 0 10px;

        .ngx-file-drop__content {
          font-style: normal;
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
          color: $grey-7;

          .desc {
            @include media-breakpoint-down(sm) {
              display: none;
            }
          }

          .button {
            margin-left: 15px;
          }
        }
      }

      .upload-list {
        .upload-list-elem {
          border: 1px solid $grey-16;
          border-radius: 6px;
          font-weight: 500;
          font-size: 14px;
          padding: 15px 43px;
          margin-top: 15px;
          position: relative;
          display: flex;
          justify-content: space-between;
          @include media-breakpoint-down(sm) {
            flex-wrap: wrap;
            padding-top: 5px;
            padding-bottom: 5px;
          }

          &:before {
            position: absolute;
            left: 12px;
            top: 14px;
            width: 18px;
            height: 18px;
            content: ' ';
            display: block;
            @include icon('icons/check-green.svg');
          }

          .file-name {
            word-break: break-word;
            @include media-breakpoint-down(sm) {
              padding-top: 10px;
              padding-bottom: 10px;
            }
          }

          .file-size {
            white-space: nowrap;
            @include media-breakpoint-down(sm) {
              padding-top: 10px;
              padding-bottom: 10px;
            }
          }

          .file-format-wrong,
          .file-size-wrong {
            display: none;
            color: $error-color;
            text-align: right;
            @include media-breakpoint-down(sm) {
              padding-top: 10px;
              padding-bottom: 10px;
              text-align: left;
              width: 100%;
            }
          }

          .remove-file {
            position: absolute;
            right: 12px;
            top: 14px;
            width: 18px;
            height: 18px;
            content: ' ';
            display: block;
            @include icon('icons/x-lite.svg');
          }

          &.wrong-type {
            background-color: $error-inputbg;

            &:before {
              @include icon('icons/error-icon.png');
            }

            .file-size {
              display: none;
            }

            .file-format-wrong {
              display: block;
            }

            .file-size-wrong {
              display: none;
            }
          }

          &.wrong-size {
            background-color: $error-inputbg;

            &:before {
              @include icon('icons/error-icon.png');
            }

            .file-size {
              display: none;
            }

            .file-format-wrong {
              display: none;
            }

            .file-size-wrong {
              display: block;
            }
          }
        }
      }
    }
  }
}

.button {
  background: $grey-22;
  border: 1px solid $grey-16;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  min-width: 180px;
  text-align: center;
  padding: 13px;
  border-radius: 6px;
  transition: 0.2s ease-out all;
  box-shadow:
    0px 0px transparent,
    0px 0px transparent,
    0px 0px transparent,
    0px 0px transparent;

  &:hover {
    box-shadow:
      1px 1px $grey-16,
      -1px 1px $grey-16,
      1px -1px $grey-16,
      -1px -1px $grey-16;
  }
}
