@use 'shared' as *;

.mat-mdc-dialog-container[class] {
  padding: 0;
}

.auth-popup {
  position: relative;
  padding: 30px 40px;
  width: 360px;
  max-width: 100%;
  max-height: calc(100vh - 60px);
  overflow: auto;
  @include media-breakpoint-down(sm) {
    padding: 30px 15px;
  }

  .close-popup-button {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 16px;
    height: 16px;
    @include icon('icons/x-small.svg');
  }

  .popup-title {
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
    margin-bottom: 10px;
  }

  .popup-lead {
    font-weight: 300;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    margin-bottom: 16px;
  }

  .form-block {
    form {
      .input-header .error {
        width: 100%;
      }

      .button {
        width: 100%;
        margin-top: 26px;
      }

      .text-wrapper {
        width: 100%;
        text-align: center;
        font-weight: 500;
        font-size: 12px;
        line-height: 21px;
        padding: 10px 0;

        .link {
          color: $blue;
        }

        &.text-wrapper-b {
          font-size: 14px;
          line-height: 21px;
          padding-bottom: 0;
        }
      }

      .divider {
        height: 1px;
        background: $grey-16;
        margin: 10px -40px;
        width: calc(100% + 80px);
        @include media-breakpoint-down(sm) {
          width: calc(100% + 30px);
          margin: 10px -15px;
        }
      }
    }
  }
}
