import { ExtendedWindow } from './app/shared';

declare const window: ExtendedWindow;
window.__zone_symbol__BLACK_LISTED_EVENTS = ['mousemove', 'pointermove', 'scroll', 'unload'];
window.__Zone_disable_requestAnimationFrame = true;
window.__Zone_disable_canvas = true;
window.__Zone_disable_geolocation = true;
window.__Zone_disable_XHR = true;
//window.__Zone_disable_timers = true; interferes with router navigation
window.__Zone_disable_on_property = true;
window.__Zone_disable_ZoneAwarePromise = false;
window.__Zone_disable_EventTarget = false;
