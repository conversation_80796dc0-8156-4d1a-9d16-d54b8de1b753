import { APP_BASE_HREF } from '@angular/common';
import { CommonEngine } from '@angular/ssr/node';
import express, { NextFunction, Request, RequestHandler, Response } from 'express';
import { fileURLToPath } from 'node:url';
import path, { dirname, join, resolve } from 'node:path';
import bootstrap from './main.server';
import { REQUEST, RESPONSE, SSRProxyConfig } from '@trendency/kesma-core';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { environment } from './environments/environment';
import { PORTAL_CONFIG } from './app/shared';
import { readFileSync } from 'fs';
import { PortalConfig } from './environments/environment.definitions';
import defaultPortalConfig from './environments/portal-config.json';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const portalConfigJson =
  environment.type !== 'local' ? readFileSync(path.join(__dirname, 'environments', 'portal-config.json')).toString() : JSON.stringify(defaultPortalConfig);
const portalConfig: PortalConfig = JSON.parse(portalConfigJson);

export function app(): express.Express {
  const server = express();
  server.disable('x-powered-by');

  const serverDistFolder = dirname(fileURLToPath(import.meta.url));
  const browserDistFolder = resolve(serverDistFolder, '../browser');
  const indexHtml = join(serverDistFolder, 'index.server.html');

  // Disables console output during server-side rendering to keep server logs clean.
  console.log = (): void => {};
  console.info = (): void => {};

  const commonEngine = new CommonEngine();

  server.set('view engine', 'html');
  server.set('views', browserDistFolder);

  server.use(handleInvalidUri);

  // Serve static files from /browser
  server.get(
    '*.*',
    express.static(browserDistFolder, {
      maxAge: '10m',
    })
  );

  server.get('/cimke/:slug/', handleRedirectOldTags);

  // External SSR proxy config
  if (environment.ssrProxyConfig) {
    environment.ssrProxyConfig.forEach((ssrProxy: SSRProxyConfig) => {
      if (!process.env['HTTPS_PROXY']) {
        console.warn(`WARNING: HTTPS_PROXY environment variable is not defined, skipping proxy path: ${ssrProxy.path}`);
      } else {
        server.use(
          ssrProxy.path,
          createProxyMiddleware({
            target: ssrProxy.target,
            agent: new HttpsProxyAgent(process.env['HTTPS_PROXY']),
            pathRewrite: { [`^${ssrProxy.path}`]: '' },
            changeOrigin: true,
            xfwd: true,
          }) as RequestHandler
        );
      }
    });
  }

  // Catch files that not exists.
  // These should not be forwarded to the SSR as they will trigger API calls that will also return 404.
  server.get(/[^/]+\.[^/]+$/, (_, res: Response) => {
    res.sendStatus(404);
  });

  // All regular routes use the Angular engine
  server.get('*', (req, res, next) => {
    const { protocol, originalUrl, baseUrl, headers } = req;
    commonEngine
      .render({
        bootstrap,
        documentFilePath: indexHtml,
        url: `${protocol}://${headers.host}${originalUrl}`,
        publicPath: browserDistFolder,
        providers: [
          {
            provide: APP_BASE_HREF,
            useValue: baseUrl,
          },
          { provide: REQUEST, useValue: req },
          { provide: RESPONSE, useValue: res },
          { provide: PORTAL_CONFIG, useValue: portalConfig },
        ],
        inlineCriticalCss: false,
      })
      .then((html) => res.send(html))
      .catch((err) => next(err));
  });
  return server;
}

function run(): void {
  const port = process.env['PORT'] || 4000;
  // Start up the Node server
  const server = app();
  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

function handleInvalidUri(req: Request, res: Response, next: NextFunction): void {
  let error = null;
  try {
    decodeURIComponent(req.path);
  } catch (e) {
    error = e;
  }
  if (error) {
    console.log('Invalid URI error: ', error);
    return res.redirect('/404');
  } else {
    next();
  }
}

function handleRedirectOldTags(req: Request, res: Response, next: NextFunction): void {
  const lastChar = req.url.slice(-1);
  if (lastChar === '/') {
    const newUrl = req.url.slice(0, -1);
    console.log('REDIRECTING OLD CONTENT: ', req.url, ' -> ', newUrl);
    res.redirect(301, newUrl);
    res.end();
  } else {
    next();
  }
}

run();
