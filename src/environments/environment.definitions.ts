import { Environment, EnvironmentApiUrl } from '@trendency/kesma-core/lib/definitions/environment.definitions';

export interface MegyeiEnvironment extends Environment {
  googleMapsKey: string;
}

export type PortalConfig = Readonly<{
  apiUrl: string | EnvironmentApiUrl;
  personalizedRecommendationApiUrl: string | EnvironmentApiUrl;
  siteUrl: string;
  portalName: string;
  portalSubtitle: string;
  primaryColor: string;
  secondaryColor: string;
  linkColor: string;
  activeColor: string;
  tagColor: string;
  tagTextColor: string;
  logoWidth: string;
  city: string;
  county: string;
  facebookAppId: string;
  googleAnalyticsId: string;
  gemiusId: string;
  twitterSiteName?: string;
  googleAnalyticsRegions?: { id: string; region: string };
  gemiusScript?: string;
  gemiusIframe?: { id: string; content: string };
  googleTagManager: string;
  googleSiteKey: string;
}>;
