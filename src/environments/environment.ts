import { MegyeiEnvironment } from './environment.definitions';

// <PERSON><PERSON><PERSON> fejlesztői környezet
export const environment: MegyeiEnvironment = {
  production: false,
  type: 'local',
  apiUrl: 'https://kozponti-api.dev.trendency.hu/publicapi/hu', // for prod proxy: '/publicapi/hu' then: npm start start-with-proxy
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  // apiUrl: 'http://api.kesma.local/publicapi/hu',
  // apiUrl: 'http://bamafe.apptest.content.private/publicapi/hu',
  siteUrl: 'http://localhost:4200',
  googleMapsKey: 'AIzaSyCOGk2WcrdQiBUeRfiqYRy70XR10EtBVts',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  httpReqTimeout: 30, // second
};
