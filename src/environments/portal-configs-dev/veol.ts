import { PortalConfig } from '../environment.definitions';

export const portalConfig: PortalConfig = {
  apiUrl: 'https://kozponti-varnish-publicapi.dev.trendency.hu/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://veol.dev.trendency.hu',
  portalName: 'VEOL',
  portalSubtitle: 'Veszprém vármegyei hírportál',
  primaryColor: 'rgba(0, 92, 162, 1)',
  secondaryColor: 'rgba(218, 37, 28, 1)',
  linkColor: 'rgba(0, 92, 162, 1)',
  activeColor: 'rgba(0, 92, 162, 1)',
  tagColor: 'rgba(218, 37, 28, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Veszprém',
  county: 'Veszprém',
  facebookAppId: 'NotSet',
  googleAnalyticsId: '***********-1',
  googleTagManager: 'GTM-NGTJQGW',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  gemiusId: 'bPCV9MsJ5cJrgo0jAAwLNfV4.KmdlIxhmscCYEi_v0H.87',
};
