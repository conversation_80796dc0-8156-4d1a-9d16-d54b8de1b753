import { PortalConfig } from '../environment.definitions';

export const portalConfig: PortalConfig = {
  apiUrl: 'https://kozponti-varnish-publicapi.dev.trendency.hu/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://beol.dev.trendency.hu',
  portalName: 'ERDON',
  portalSubtitle: '<PERSON><PERSON><PERSON><PERSON><PERSON> hírportál',
  primaryColor: 'rgba(27, 75, 139, 1)',
  secondaryColor: 'rgba(218, 37, 29, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(27, 75, 139, 1)',
  tagColor: 'rgba(6, 115, 175, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Nagyvárad',
  county: 'Bihar',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'UA-25566293-1',
  googleTagManager: 'GTM-N8424S7',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  gemiusId: 'pz.qlfcG92IRAdJxtqyZhJaGLbqFMZrCAqXNjq2mtKv.27',
  googleAnalyticsRegions: { id: 'UA-5849601-1', region: 'eszakkeletDevMegyeiTracker' },
};
