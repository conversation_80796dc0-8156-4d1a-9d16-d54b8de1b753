import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.duol.hu/publicapi/hu',
    serverApiUrl: 'http://duolfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.duol.hu',
  portalName: 'DUOL',
  portalSubtitle: 'dunaújvárosi hírportál',
  primaryColor: 'rgba(108, 167, 212, 1)',
  secondaryColor: 'rgba(225, 61, 45, 1)',
  linkColor: 'rgba(84, 122, 152, 1)',
  activeColor: 'rgba(225, 61, 45, 1)',
  tagColor: 'rgba(225, 61, 45, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Székesfehérvár',
  county: 'Dunaújváros',
  facebookAppId: '435400756553980',
  googleAnalyticsId: 'UA-36393106-4',
  googleTagManager: 'GTM-TQRXQ24',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'd1A7Mks_sFHMZRXbBhzhYqeirmIgAU_3f3JcXMTgMmr.r7',
  googleAnalyticsRegions: { id: 'UA-*********-7', region: 'nyugatiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638511945464_69216925',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["3u1lc1ocjO4DOo9HrNSM10jj7dW3lfTF9vdkvVtJuaT.u7",*********],["6..A1tKUdoLYJnKj._aOWJfRDOof_x59923h.zIQJdD.y7",731456894],["uS9qldTGsixOqaM4DvTDhRrs.5mKVizO3OjAgBpSu3..m7",72851784],["E0UVGh3v3GnigrV2qeqMTFoTPSEilBVmjzCHhiPiUbf.K7",4025608296],["41rLlp3J96VHN536kPJiB3gEjaxPaDWEV99dZ_JZ9df.R7",578949735],["Puobj0nep3HTmv9FOOxfPgtrvsmKDUll_dS9msH8ibr.r7",1568166868],["6aVq80WUsnWUSnmO26l_5QtajbGKGzTCDFo04u6ENXr.T7",3224054286],["6Oa7XdIHB94Ci6jNnhFo0kogrwIitA0bb.p9X9jjroL.K7",3468030957],["GHC7L1K9B9VSeA_AkjMnPlpDHfMipAmmL2NG_qiBDn3.j7",2910305570],["Q7iwBo3qhfsXqLIEAefm95gTDOofsh599yi0Ut3cLdf.M7",1476837534],["GLm7xFJBB_cdaak936_kWkq..3Qi8i1Q_S.9TDWBxq7.H7",350381835],["uNMao2J7AilN.X3Im46oyUogXHUitD5rL7Ce8ewXy.j.z7",2384484191],["uNC642KqYgtI0qm951cOlAtar5iKGw0PEmEwZ6zplwj.T7",3761641355],["SZVgtjT31uU_yjH0S7giV5hDPdwfkhTYhxLtOSZQEWv.z7",4210363324],["3j.LzVoO9_VuFQlBfZtkOxsun0qKDW0Pr.MaZ9ZJSlr.M7",2050676473],["PtoQ9kquJlk4s1JfvOviYymk.57nDS1SJ15n_7cyeqb.q7",4005113978],["Puplo0nejC6eRt7APRjEUHgG.75Pzy1MdNbQswptgyz.K7",3534528102],["GYJlOMVajEBz7vUW8gPcZIf2HshPZgjU5KhzpoqBwsb.g7",1039015051],["s0W1My5cPERXFsqn9QEKZTl_zhG3v_kQ5LTmRVszYs7.i7",3840383447],["SOO7XcK4B93dSFPlN4Xysil2DE7nxB4boZ97a48_xIn.f7",2835700269]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "GUi6FMU9YqaKi_p9PhUtdM.CxUUGiPhSysyidRO7M1L.n7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
