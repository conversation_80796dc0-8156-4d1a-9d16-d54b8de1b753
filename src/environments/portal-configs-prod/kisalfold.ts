import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.kisalfold.hu/publicapi/hu',
    serverApiUrl: 'http://kisalfoldfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.kisalfold.hu',
  portalName: 'KISALFOLD',
  portalSubtitle: 'Győr-Moson-Sopron vármegyei hírportál',
  primaryColor: 'rgba(225, 61, 45, 1)',
  secondaryColor: 'rgba(6, 115, 175, 1)',
  linkColor: 'rgba(5, 92, 140, 1)',
  activeColor: 'rgba(6, 115, 175, 1)',
  tagColor: 'rgba(225, 61, 45, 1)',
  tagTextColor: 'white',
  logoWidth: '280px',
  city: 'Győr',
  county: 'Győr-Moson-Sopron',
  facebookAppId: '377437792934842',
  googleAnalyticsId: 'UA-979753-4',
  googleTagManager: 'GTM-MJ76JQH',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: '.RpAUuLRc1A5quhx7SKhF6R.zXA6ARtAS2wd6Ecor8f.p7',
  googleAnalyticsRegions: { id: 'UA-*********-7', region: 'nyugatiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638516031796_45020669',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["YOrRD0EO84azdFWm9u3s3jSi0RJC_avBHJweAd0B6tn.47",1791758043],["xnUnUNh7br_JFglswHwwiuYMMcFFVGv95OdrNg9mmrf._7",625072560],["O9YsTcvIJMgJ6QEtLiZl_UdP0ZdQUKy8B0iYtESN.ub.W7",1384626954],["mxOBRqx_s11jrFSOKVyf1yeDMWKAcmxmzX2oonsTGqn.x7",2523720773],["YNrXqUDevm.jbSk.xrIgc_asQ4RFZIeC5KSo_GOrg8L.N7",2699510691],["wCh8bSDz1NS0zWHlG85_Z.ZXk6oVN6eFnZ9_ytKLb3j.B7",1792773850],["awCBRDwRsyXjemRC.Ur._TbnIcmAZMyEWooVgVNR0gz.p7",761242541],["YNrWj0DeGQ3p6olbL5PS_BX1oI.lzJgvZz.8dwu8RQz.b7",2003897842],["YUeH4LRZfjeqCK4Oo3FC3Rd6oIuwwZikVBmCn_.pyiX.k7",3473148304],["YVp8.bRa1JRvnGb7tfdkK1Wr0e2tRKwT3AwOaoY1CmD.Z7",2526219733],["kAEsQrE8JFpUhybKYzuYDzRx0QdCZKwcfMTVpwFfKs7.67",3473471987],["YT2H4rPMfm5EPG7zWsVnxnWmQB19gNiogq2v.G11.sL.l7",2883116725],["ZtUs1.hWJFDumiejZIxUmOZ_splF64M7l7Ux4a92m5L.87",2400295077],["m2Nx36y_Uyk4EQS9xegztkVwUkrdHcO.rMaOxvZ_rgX.q7",826749717],["NqWG_Ihq2VsEU.6B_Cbk8haEMxDluubn9JvLfHbHG5f.q7",3555611976],["ZnsnKviJbu0e2C9U.MBvkkbwQ2pQRYd5Zydr0lPW2wX.R7",1929897125],["xsKGyNf62RtEojO.VKDfMhPSIeNy6syNb4cIgYho7kL.67",4091553529],["kJ4nK7D7bu0pRlj_m6V8RCckQGKAZNhmzZk17ObQEqH.D7",3896232744],["O92BRsyEs12oaW81fdvc61WoU.Wt_HgR3MCs4rKnBV..97",625544201],["y5YmPxyqyaO5MglTvrGaQ0R7YgMS92OLHJTRqllrO5j.57",2157807856]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "vhrRbktE8ypNeEYhD7zbE62MG.02WL4RjfGdHuaydzT.37/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
