import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.bama.hu/publicapi/hu',
    serverApiUrl: 'http://bamafeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.bama.hu',
  portalName: 'BAMA',
  portalSubtitle: 'Baranya vármegyei hírportál',
  primaryColor: 'rgba(0, 92, 162, 1)',
  secondaryColor: 'rgba(0, 109, 52, 1)',
  linkColor: 'rgba(0, 92, 162, 1)',
  activeColor: 'rgba(0, 109, 52, 1)',
  tagColor: 'rgba(0, 92, 162, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Pécs',
  county: 'Baranya',
  facebookAppId: '342721689187243',
  twitterSiteName: 'bamaszerk',
  googleAnalyticsId: 'UA-53604765-1',
  googleTagManager: 'GTM-THMHC3',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: '0suVf_cD5aWSi0fykU5MLMTYHWv1cgbfgxER5.vZ.CH.J7',
  googleAnalyticsRegions: { id: 'UA-*********-6', region: 'deliMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638510876442_19777414',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["2konlYtrNzByPvuubjmI5EapMTcd.JNtvjckXZ6YOdz.D7",2468796070],["pD.cwGQoPeWI9tmWts0kI7Wl4wpV_O3Ke1LkT2mT2mj.H7",1037873794],["BPKMeccY.WpTtM_pvcd3RXd04xtYRe6TM1Q9qrStofr.x7",1519026193],["1SJ2a0eBQhn5P9sLZhYm9lYiUjnt2hpdq277ipa9fT3.K7",191150234],[".0VxghAQHNv4grIeQEXm05UIoNCFRb9Y1etYRVZiPA..W7",1190412485],["z3Um5JB4ko0DWEBkw.m0sVZR0aHtONMKvssxdFoo5Qv.i7",2061565109],["BeV8nse5nRAuGAPXWjjKcJTYIQ6FZPLp1QJhH0KoydD.57",525395373],["nwWGoiBtog6zxZrxSHKKlDa0IcQdhPMdHhVhvJ5aSbH.w7",3597711576],["n7uMni.P.TxTHL73zKJXQ1Yi0ezt2tLsjpcBR4XpcVz.P7",3405550567],["qhncwBtwPeWDPeRS54CW1lPSoLZ6xL9PLTqx8cWbzS3.m7",1702073215],["qs0nXhsBN2BdiasT.0NUHLWjstNVkdn9dWCRWm3.5hz.47",3440622258],["zyuGDZ.KofwDn.ZxgKVsqXQt0Q1KwNMsMw1KAvQv8T7.Y7",2687826558],["1FYnjtSFNzB3n.rHoZ0EMbUD4wpVge3KSv96MFm1Ynv.U7",2163130018],["pT7XddcEh1pTGDFzqdnE.GRNgQxKObOkkJM3AQP90cv.V7",3940432315],["1Kl2ndSnQiGi9jCtGPmQl2ewIelYkvNrYv7n.Lh6IYv.H7",1147020702],["BNXRoFPdvEHI.zG6Gl3xwYSno.sag77dbez_Byk4_HT.E7",2492533563],["BeHXdcboh1oopdFsPaVs9jaHUgAdvhoU6EYkrQum3lT.w7",861351335],["bzuH97BkRw9jK_sWeLMUP0Z3UEYdhZ8zpSsoBF_7OOD.I7",1372230640],["1RWMeUci.WnOlbPzyltKkFQx8CF6z98wus.4GcDCvIv.97",1269671505],["z35xuZ.sHBw_YqDBJcId5ja0o.Adhb9oBYH_aXMX_Iv._7",3114131404]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "d8gn.1KINzrMibhTbzw_iG0uSVjOJvkz2kvPNFpPI1n.F7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
