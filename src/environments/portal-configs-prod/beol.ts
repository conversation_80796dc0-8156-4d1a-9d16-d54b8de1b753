import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.beol.hu/publicapi/hu',
    serverApiUrl: 'http://beolfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.beol.hu',
  portalName: 'BEOL',
  portalSubtitle: 'Békés vármegyei hírportál',
  primaryColor: 'rgba(217, 26, 36, 1)',
  secondaryColor: 'rgba(38, 82, 157, 1)',
  linkColor: 'rgba(38, 82, 157, 1)',
  activeColor: 'rgba(217, 26, 36, 1)',
  tagColor: 'rgba(217, 26, 36, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Békéscsaba',
  county: 'Békés',
  facebookAppId: '561090840666908',
  googleAnalyticsId: '***********-1',
  googleTagManager: 'GTM-5LSTBH',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'cjU1vw7S1aVlTKiddl0mHLb.fZUYG8e3KEBBcmOCAiX.P7',
  googleAnalyticsRegions: { id: 'UA-*********-5', region: 'keletiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638513040591_65648306',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["Sxh7eNRzW4lRcO0dQ0lnyULtc9BMW.tqExOT7TPd8Q3.k7",56481850],["u6fV1XPloKd25Rkb80__5NCX8mvhZMbI_Mxq0DlriEz.O7",2847453836],["Fh2L8yA9u5kyxr2u0pvDUOCf0xmxj7u6mMLwqPl4.eD.O7",373559105],["5lNwlaBH2sscI6sAKsNvnTOLonJMzaeNQ9430Bv76ID.b7",2513713279],["QFZ6LulutfYl9YIe7GrUWvBjYaKxjxCTmNCrFTVxY5..17",579368186],["Sx6Lg9QVu1jszA0Xp.T3CiKboryxz6eJs2xnH0OjINj.b7",1541822868],["QciFfFyMYL6M0jldce4arhHEQf3hOec9oNiEJ5dgsLv.e7",1321840425],["EUV6N_yPtizWAMxDkNfUV9A3caThpLCA5Q7Bal1RzFz.p7",1103975118],["S85wZdPV2or7kFAsAv8F5.AF06mxNrtaYmR6nvnc1Jr.27",4224622689],["6y4l7uPCUH1WvZPxIvFAFVOXkq4cKwbayqCHJYWZuL7.e7",2676298163],["Qb96Z1wZtmznQYJenZlQP8AFcP.h_bBiwhWxAi3HwC..S7",3562737674],["u051PnQXAJOmd9___2XZd0MfcRBMz7CirSSI4GE9n0b.g7",816003647],["sOkgaomxKoh1wQqnc.diQgKOgx3h7Zs5DQH63dy9wKD.K7",599087976],["EZTQhevPemAHoNnn0TeeLiJtomyxVqc5aoBX6PC4rHT.m7",3237912867],["u0TQJXSkeuAMH.Am6.4V6EOPg45M.5rJ46xjsPQISXP.E7",931537586],["SxgqSdRzBs8cLOGpAK5bkzKPgDuxZOyksxM_6Xs0Qjn.27",133563093],["ts17ejB6W8.cluzrxgqvXP.VQpOxVudL_L_hoYF.BE7.07",613266310],["QXWAZVx7Ou4c4cC.bSBJnCKdcWCxNr.y45yoZfsRh0r.J7",2026002448],["QRHaIFw6VsImOCcWoEKmesBjc_nhgfsm_Mn9b2jP8Kb.f7",2912328565],["QXHbo1yq_8smGzJ06BV_0N.GETHhkfB3_Cz__Az1Oxj.j7",2277447311]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "PqLbCwM__y_vat6mb7.GsFqDWGAtRdKeyYRsnSTzwJ..v7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
