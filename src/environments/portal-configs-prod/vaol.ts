import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.vaol.hu/publicapi/hu',
    serverApiUrl: 'http://vaolfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.vaol.hu',
  portalName: 'VAOL',
  portalSubtitle: 'Vas vármegyei hírportál',
  primaryColor: 'rgba(0, 125, 70, 1)',
  secondaryColor: 'rgba(6, 115, 175, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(6, 115, 175, 1)',
  tagColor: 'rgba(0, 125, 70, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Szombathely',
  county: 'Vas',
  facebookAppId: '358907434221446',
  googleAnalyticsId: 'UA-36393106-5',
  googleTagManager: 'GTM-WZVQSG7',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bVA1NEsZnTw7I5pTPpjSrdWY.BNNxAxXBKmIEfV5V5v.k7',
  googleAnalyticsRegions: { id: 'UA-*********-7', region: 'nyugatiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638518299299_74715776',
    content: ` #document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["6HG1W.nBC05lFCs.cPQF5CFtX4hZoIm_cmmdbU_1laD.s7",*********],["s8Nq6kX5Edy6sqlZKw09tEGZX1cpcYloBUxgYVnzdhL.R7",3373410111],["PrHAf2HqRXIwSNL4CbqJ0NI7.TPBU2ETesiIzDRUBGP.j7",2074104386],["DuTAdfJARTuwJHhUcQ.n6vI4b2WRQykYA51tO.M_rR7.U7",2067514882],["sw27.0aXZmmFGq937vS_8CN3TQn8CIDm8Al_M7QBuFD.r7",646728357],["GOFqxXnYEQpVmz6hYXw.8cHYTenBQ4DW4M8YGx8RyD..57",3643549078],["uE6w34okVSq671zo.d4lfoBMb9XBsSm_CFKNOpVOxWr.C7",4293935710],["GIi1M3olC3yFylVARcgWy8HXLtrBkXVAGhgk2jvMAVf.T7",750647834],["GJEV6HqYqytPwZWnnJ1KvwL3X4gs3Im_Sncn1H0pSW3.P7",614270894],["GEG6Q3pYwV7.UHLeLIY_T.LUbD4s7Kqref5ctxGM59L.k7",2745375675],["PlgVUWIMq_Iw7LBUpdF.b.N2v2Ysk0jtve86mRUQpUz.g7",921767132],["ucAbHP1CBtAQlQ4kP.INpwL3HK4s3Iq.k7RFOowV6HX.G7",1949325083],["uOu6PInjwV8kPC5.ZDibo4BMnc.BsaDWeGK1tkUEwFH.Z7",2169002307],["40YazLYFYTzwQKfxo59o5uJEX82RY4lM6pZHviRJYbn.Y7",2163387220],["rugV8wJ_q5kQOhXF2T0P87CsHGiRY4nbn_gcCH6683P.s7",3154505786],["GJGw1nqYVPL6Hl0BLbshUuHUrV6RM0EmMvtIB5rEWJf.37",1287135646],["rkIV8QJcq2Hgm2atCZ57WZCy.XjB2mFxBWMencWPuNj.07",2985369425],["SRdqul0tEZv7CklkjZbt7iDMTb9Z3oEEofzrAdsMbEj.j7",1658357857],["GO5q6no3EdxPL4fY8O7MRCLWXzz8RomsA1bqZWSlYe3.07",1247355372],["Q_NrP6ZHttvaspjlo7M._aCKnTCRJaE2SKV4VH0xbHj._7",1080514499]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "Rki1Wv9EC_Akoryd4tIk0Snvhe3dUEPzGQFZgtV1sqf.m7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
