import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.sonline.hu/publicapi/hu',
    serverApiUrl: 'http://sonlinefeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.sonline.hu',
  portalName: 'SONLINE',
  portalSubtitle: 'Somogy vármegyei hírportál',
  primaryColor: 'rgba(223, 223, 222, 1)',
  secondaryColor: 'rgba(6, 114, 177, 1)',
  linkColor: 'rgba(6, 114, 177, 1)',
  activeColor: 'rgba(6, 114, 177, 1)',
  tagColor: 'rgba(6, 114, 177, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Kaposvár',
  county: 'Somogy',
  facebookAppId: '247645648776273',
  googleAnalyticsId: 'UA-53607845-1',
  googleTagManager: 'GTM-T3ZPQ6',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bDs7W9BLsDYnKXFttOOCAbcffQuYWccbPqTr3t4V7R7.j7',
  twitterSiteName: 'sonlinehu',
  googleAnalyticsRegions: { id: 'UA-*********-6', region: 'deliMegyeiTracker' },
  //  gemiusIframe: {}
};
