import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.veol.hu/publicapi/hu',
    serverApiUrl: 'http://veolfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.veol.hu',
  portalName: 'VEOL',
  portalSubtitle: 'Veszprém vármegyei hírportál',
  primaryColor: 'rgba(0, 92, 162, 1)',
  secondaryColor: 'rgba(218, 37, 28, 1)',
  linkColor: 'rgba(0, 92, 162, 1)',
  activeColor: 'rgba(0, 92, 162, 1)',
  tagColor: 'rgba(218, 37, 28, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Veszprém',
  county: 'Veszprém',
  googleAnalyticsId: '***********-1',
  googleTagManager: 'GTM-NGTJQGW',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  googleAnalyticsRegions: { id: 'UA-*********-7', region: 'nyugatiMegyeiTracker' },
  gemiusId: 'bPCV9MsJ5cJrgo0jAAwLNfV4.KmdlIxhmscCYEi_v0H.87',
  facebookAppId: '149303868575509',
};
