import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.baon.hu/publicapi/hu',
    serverApiUrl: 'http://baonfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.baon.hu',
  portalName: 'BAON',
  portalSubtitle: 'Bács-Kiskun vármegyei hírportál',
  primaryColor: 'rgba(232, 115, 14, 1)',
  secondaryColor: 'rgba(0, 92, 163, 1)',
  linkColor: 'rgba(0, 92, 163, 1)',
  activeColor: 'rgba(232, 115, 14, 1)',
  tagColor: 'rgba(0, 92, 162, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Kecskemét',
  county: 'Bács-Kiskun',
  facebookAppId: '1496780083871001',
  googleAnalyticsId: 'UA-1697516-3',
  googleTagManager: 'GTM-NJT4JL',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'cjWbuw9KiKE.iq3FTd6MJ8Wm3_L1k_8O42azIyKVWPH.k7',
  googleAnalyticsRegions: { id: 'UA-*********-5', region: 'keletiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638516736866_64579131',
    content: ` #document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["0pbKjISCm4.t6PYhEwN4hKgTIdVX7GPJuzHgYwGUbYH.27",1681368933],["zVywSECXz2gSN0PNfHMKSRtt45GSYS876x2MRO1ShVP.k7",3206876583],[".GbKvF2_m8.WXlYEBT4IlPsA8FfCv1BuXuPRxZ0BZOz.E7",146621654],["nBdl2mzrRSELGOhZdSbMcQsbAqGSQzsBIQSQP5hpubr.P7",3071472227],[".SLL3cCMQBNTE.tAHZjMWEqNoKJazjCuPY9hC3j9_BL.c7",438400222],["bWvFamAx5aVzhNJC2Zu1xRtrAnuS_js9mOQGyOJfGXj.L7",3722918407],["cfS106SL9V0dJRiYa1hoNikm8HHv_lAqZn2U5nYfZI7.K7",3768360807],["bWvFQ2Ax5ZxCaZf.MAvM0QtJM5iSvE9_iyivSyVO4gv.H7",3445744534],["cuQa3KRbS5Yd8ir9T0R21RjBUqbvg1sUZgo57xKBDUv.R7",53314710],["0je7n4QSUNuC.yskTuGkKqfmAhFXJTth_DSQb54wZVf.G7",477230051],["17jFocfK5XWHdjh1cJ88_KhWIdVXVWPJi6iXRx0v_K7.97",3999369979],["cpcbxKPs8HDnSSu9shmc_2prMSgqgwQH0MTdxtfqD.v.M7",3773547938],["BykQNkfDb1Y3168V4JRvhftw8HbC71BoUdEeYcPlTw7.Z7",3820619184],["0pFg9oPgHxwdsN_B.6sPmKflAuxXczrjVcy5kM5mES3.E7",276177469],["0ubL3YPSQBOMXrDYrnsAvXouoMIqgzC_1nth.Qrs_Fb.d7",3592508269],["nM1qzG1N_ysxQVZ75ZDYyIioUuyHsFrS8jLDaOeNFZ7.s7",2108755638],["zEcaOt0VS27G7tZ1xQ2Y2hudMb6ScQP7602H7pkU1Fn.q7",2916972924],["zERqNd1E__HLDK.5OeLYHDmfsp7.CBs.cH7DmusNea..A7",3655922999],[".SjK6sAum8md6fAL8Vpw0klkkwX.bw9Ns30cOj7q7Vj.L7",768889088],[".TjLDcBeQFL4B6ZXxU1S_3hUUIKH_xCvwuXxTps8zNf.x7",2332241148]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "crvKjaRim9spgfdHam9DIzOya1F5dhKM0R2KPBaAn5v.P7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
