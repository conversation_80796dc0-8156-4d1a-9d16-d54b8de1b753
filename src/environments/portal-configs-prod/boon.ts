import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.boon.hu/publicapi/hu',
    serverApiUrl: 'http://boonfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.boon.hu',
  portalName: 'BOON',
  portalSubtitle: 'Borsod-Abaúj-Zemplén vármegyei hírportál',
  primaryColor: 'rgba(0, 92, 162, 1)',
  secondaryColor: 'rgba(218, 37, 28, 1)',
  linkColor: 'rgba(0, 92, 162, 1)',
  activeColor: 'rgba(218, 37, 28, 1)',
  tagColor: 'rgba(218, 37, 28, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Miskolc',
  county: 'Borsod-Abaúj-Zemplén',
  facebookAppId: '343947129586593',
  googleAnalyticsId: 'UA-25566293-1',
  googleTagManager: 'GTM-TTQLKP8',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'pz.qlfcG92IRAdJxtqyZhJaGLbqFMZrCAqXNjq2mtKv.27',
  googleAnalyticsRegions: { id: 'UA-*********-10', region: 'eszakkeletMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638512781685_24240096',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["Bt_1nrIf4T39wjO62MHJLQONrxMi4P9t8HLBmYLXYTD.27",2087772437],["oETFpxuC0c0BIK4SxqvGVSMq7eTylBeh00Z4ni7VlKz.17",902366344],["cTDLsR6TL.Pc5FvuhjTI4LBDjbe3l1cl9IB1nqWiUHD.B7",841735965],["e_Gw9ZXqK9t8.OZRlQVyktKRjXDntVeB4uxINILEBBz.B7",919820282],["cNK1bqt24P5LftNUoq3xFhOVzrDyDQuNoz0OsEcZdCz.E7",1316537674],["cSrKKR3x94unro2RAkSKfEG9PeEf3zciP_n478Kk6D..L7",2878491784],["ADxgBQuSe0Wr3BXPbrQ_VVDgbhgfz0r.D7f7y6Z8PCf.K7",1544082761],["29e1.HYC4RdMq_3UWXX2leKaDKq3lUAJUoafvgbSjNj.I7",3938562080],["oa6wI44sK_P3ZDsdkuyUofL6D.Ai8U.dAxSvgAtHQBf.J7",2570239309],["oI7Kxxsg90Rh1WxAyrPVlwO9ziEi8QrtnTP_3BPtbIj.z7",1795771042],["pj_7E8IMPOMourbiYCb2fhNlvO3yLS.YAv5.BG9HtO3.97",1907409817],["1gmwgzHmKvQIBIC6kA7mikGN3Scf.3dBRQBfLtWRUE3.G7",4290344592],["cTPFpx5k0c18iK3iQrNCjTGzzmZPTAur6N6Is9TFDBL.37",3076397128],["2yGwenagK713EIt9YVNc5iFQbq1PPErNBcBxB7zIJbr.37",2060295504],["BsYaGbJ8p5GdM1197NaCvEGM7bofShbPFQrFRK1mYLf.57",1725094423],["e_e1.paM4U3Sbql1naygCqA9Twm3.j8pZzv3M2mFJa7.57",2475530983],["1lkasDImp9qivPJzh5odEvNYvqkiSmthM2vRvZu3GZf.a7",4236798181],["qwQbYwa23FCSMSEtPbV.dOI5bj638EuDuNM4fNY2fH7.Y7",740414851],["BiZl5bHsMXmSX2jEdve0jSFPnwpPh1774ojEmC5h_Pn.o7",1282741915],["AQEQtX3AyvfHPmsglsTk9xOXv.PypCBco0.J_h8xxzL.Q7",1819764917]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "CUwasR.ypzUmf647CNVFzKsKxZub8xoy7_YTvI.9JjD.q7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
