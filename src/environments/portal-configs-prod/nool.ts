import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.nool.hu/publicapi/hu',
    serverApiUrl: 'http://noolfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.nool.hu',
  portalName: 'NOOL',
  portalSubtitle: 'Nógrád vármegyei hírportál',
  primaryColor: 'rgba(248, 194, 42, 1)',
  secondaryColor: 'rgba(6, 115, 175, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(6, 115, 175, 1)',
  tagColor: 'rgba(6, 115, 175, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Salgótarján',
  county: 'Nógrád',
  facebookAppId: '790661371136666',
  googleAnalyticsId: 'UA-61834979-1',
  googleTagManager: 'GTM-TT2GHQJ',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'AfKaz6dHW7Pq1UUAEr0t9qeiTBMU7TgrENfBuuYMIDH.G7',
  googleAnalyticsRegions: { id: 'UA-*********-4', region: 'eszakiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638516211561_57292449',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["O7Ic4iO1qlJIuFUxmHvjY0klQ9r9ElgaXLG9DQqPToD.N7",4245039929],["kUy2touRr5CuIoFH0pjWfHiIQ2uVyli6SXhDO32JQ4..o7",2680093288],["O2vM8iMC_rxIbhTUWMu.xEoyMdNof20HvqyERQQcBnn.j7",4160384324],["loRhds_qSaej0.knBve1aWpek0Y4IHf7R5kty0OnBmL.27",2024286079],["lpDMzc8J_upIXfQRv5eKT4iq40GV2Jh51hdAPOVzR7X.P7",2275759936],["loccQs98qtKuVYP5C.N2QQuuIRTQm82nZH.hHxhs3sv.07",3254333010],["xl3H2T7jRKk_J1e..A17oClZIUAtMc15v.cezSIRKej.y7",2988900547],["k.Qc4hgxqlISHiRV.ciW20ohM9JoXbioXkomYlvzxwX.z7",3786517289],["kEQX_xiB9LVIgIvhtoqBuZhVoExlXWmY6b1qbSV8RSv.a7",1927451679],["YeNslBtSWq7Dc788RV5XGijoUpctT8Si4x20IBbMvlj.h7",1994878906],["wMbGWIisn0aTrDsL9goWOCj5UpctccSivGdXBg6..iz.c7",3454551292],["y4IXaXL19C4DLtEBf.5Yk3foMz2Virf6ZhCQQHk6E2L.O7",726535352],["lpPH287aROBNg7Hjh3Rd66fXgThlW41kGd0x08FmIjH.o7",2113644206],["MbZnEqr4pFPZvsHlKq_J6OtussUAFIRRjivk3DkgwiH.F7",1271619131],["YMe3MqgWVCqYuJw8efI14imIgPMtX4zWqb9uhGJn8UT.T7",600751566],["ZgcWf19RTxyTpty7q39X8ym4ApstP6QLc219.T7Dvj3.X7",13798057],["yyy8JHMSCjWehVVzHV5.1Ih98JKVEYj440LKA1InYcL.R7",1271277329],["a9HHopOfRNkzgvDvFHx4xFpVUHJoP0lt15La1jrnjP..F7",4100341482],["ZsDMK1_O_jM4fLoN.xKR6_uAoLcAGGm6lDR6.6.szTH.F7",3304379942],["lo6xBM84_PTtWRirrz1MfHh3MRiVqG2Vlof_OAkR5Sr.H7",679849384]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "kBIWdxfaT0BPi8K_VS_pFJGfeC2EXE8XU.9YbV32Xxv.27/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
