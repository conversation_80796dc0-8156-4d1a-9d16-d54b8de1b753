import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.haon.hu/publicapi/hu',
    serverApiUrl: 'http://haonfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.haon.hu',
  portalName: 'HAON',
  portalSubtitle: 'Hajdú-Bihar vármegyei hírportál',
  primaryColor: 'rgba(6, 115, 175, 1)',
  secondaryColor: 'rgba(245, 194, 42, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(6, 115, 175, 1)',
  tagColor: 'rgba(245, 194, 42, 1)',
  tagTextColor: 'black',
  logoWidth: '217px',
  city: 'Debrecen',
  county: 'Hajdú-Bihar',
  facebookAppId: '320652735502104',
  googleAnalyticsId: 'UA-25566699-1',
  googleTagManager: 'GTM-TR9V3TD',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'B9.glG_HY87YwxeYiB22SKRtLbp60YLaYrvhK6QSyV3.37',
  twitterSiteName: 'hajduonline',
  googleAnalyticsRegions: { id: 'UA-*********-10', region: 'eszakkeletMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638515566734_42821100',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["VerRMfHCn5.kB6ri291F0PdzsjoNqkYIfsq0e5B7ecX.D7",**********],["hJ_MCP7v4EjYZC6VhHY__dPSoLw.EFrhtn5vxjw7.H..X7",**********],["72qG9FrHhST4SfqXdpkxEqYLgbjS408jy2IywbOxPM3.q7",624490905],["hWEsSWJv0Mglr3zb6CjhbhUgAqFq1GZ780yeBn51CAj.f7",273340762],["hRPW5GHwxY5azzp3SA3ZA.cWMW0N0y88qGKFiWVVGOP.57",255030365],["VMx3DI_iWrHNpiSqeJEhStPSk5Y.EGo0EDD1dGDy_Sb.07",215988646],["hb3X.mIO_n1aBdPdQC7ho8ZIQC6i1ZsCy9xc9RMqv5n.d7",**********],["Ve52WvKTJXq0lqW4k9GrtSVqUII6tztWIMnv825mUKn.S7",281534257],["KkwscMZ00NDJkn17tbyhTdSRAhE.PmXbIxietxDfzCr.l7",**********],["9UrXnAKv_sYPPyRTB_ed5tSRsp8.PkamHSdr7aXtGAb.K7",**********],["VY3X1vIj_nR.zCSYxweRmLXNYjPSLiXFAX7OJRHqGFT.D7",874320910],["T1PcsEt4IHvpS.gqK2i7LPekkysNPGoSfio_c5AK0UP.G7",**********],["io7WhKX7xQ6Z698Jebf3VfdEUMcNTDrkDgyJfURqQx7.27",787712891],["H4eBcNueXzA4mSFAaun5TdY8shWiZ0aK0YbxOeKh0XD.17",**********],["hAKB4f9AX6ethmrl6Dv1aPQvoJzP8VrnIHvJUJWSmzT.M7",**********],["WsyGwjXfha5ZWVUm1boTG9bXYs89XiYnOBOBMOTlRUf.I7",**********],["iu_Ga6ZrhWT0AfoXCZ6xqSWc4yc6.oqpIFC7kO5xjU3.a7",779061554],["9Pt89wJvgCd1P2NbfCsBzAPw8OHPWns0Y0RydqT5pFz.r7",113771764],["Jbt8YIKngGck8X4xXyn2oSU80Wk6Pm9i838ojTo6QHj.37",**********],["7wSBQlulXybuSGr6L8k5HdY6Usei0IXLa_CRr9LvPYT.I7",927849944]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "fA8nVGKEqieRs8ZDLPyhnEyZG3sbxEzQXDaWo_8ai2r.s7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
