import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.szon.hu/publicapi/hu',
    serverApiUrl: 'http://szonfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.szon.hu',
  portalName: 'SZON',
  portalSubtitle: 'Szabolcs-Szatmár-Bereg vármegyei hírportál',
  primaryColor: 'rgba(223, 64, 58, 1)',
  secondaryColor: 'rgba(17, 116, 174, 1)',
  linkColor: 'rgba(17, 116, 174, 1)',
  activeColor: 'rgba(17, 116, 174, 1)',
  tagColor: 'rgba(223, 64, 58, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Nyíregyháza',
  county: 'Szabolcs-Szatmár-Bereg',
  facebookAppId: '576190226204259',
  googleAnalyticsId: 'UA-25600701-1',
  googleTagManager: 'GTM-MN5RNVH',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: '.R.q_G969_FW.NJfuV3TmpaGLbqFsQNcLDrBYVnHg2r.U7',
  googleAnalyticsRegions: { id: 'UA-*********-10', region: 'eszakkeletMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638516964657_27012196',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["UPRnDD5w9TnyX1I4ykVCyJMb8tu3yBjeVTq7hLgWkov.O7",1983750502],["9svM7oKaS9n3_j80ByXojqGkERYUgUIqapfvuB4OJnL.w7",2825757701],["JpXGIgKQ8OwdQnFtOIh7uGJO8iZ8FRjACjhu.zM6SnH.m7",308767972],["IXFs.s5jq0Tnvu_Z2fdAWLGvwank9iJJp_afFF9JtrT.C7",3482077749],["gcEXla5JRYxNsbvh0yYqVXIp4.5MTS6dTPbsGmsrNX7.y7",1779337749],["UYu2hD59AKT8w6rAxmjH0NF5wcS0PyIFTQq.7eXMyuT.57",3391411088],["IW3GUs6S8CxHPiFq4pgDxFJcEQV8ukIxp6c4Nvf5jnj.I7",2827597528],["VhZnDnHL9XFSM_bdh4XAiWKQ09J8zD1sfZU1dt9oP5r.t7",38984091],["i8FnDiXh9XGydSFhNV7xOrGtkGHkXw6zF4h84g70.V7.37",444773535],["UT3BAj4OyuVdhQh642Fanh.RcaR82gIGHz_lQ8VdyrX.p7",2102471370],["UZFntz4f9ZgnpaJ3vjh23XO.gvbn_Bz7KBZ4hjgxq63.k7",675408652],["IR4We85SoDlNP5rstyTTyD.N8rtMyBjOAlj4BtWL6oz.27",299203353],["JkXB0gJQyqWtz60ztCK0NDCf8gZMTxmwMl27tc1w1j..R7",2203717853],["IRuxkM6B2f9CCtH.vhRzOXHKYRFMP2KoR7nl0IgFzgv.i7",2048291779],["VhlhcHKcKin3ZQ1pt6WQG3NPMDjn6E51VWY8UpiCJYL.77",2692930951],["UDVntcrT9WEGkEb8ukeQy9Grgve0thz7QEc79WGyp9..u7",1849828740],["8ITB0NuQym3BZ4dtTurFeNFJwX_0XyJH5xdyad9o0oH._7",1855574052],["UCURUsujeothQzf5XubmZsES06zkBjzuEJxSn8Uvf3j.J7",3512571530],["_vSxYsaE2mYnDXIqMTLr6oOHgyK3Ixz9whXFCFGKXzr.i7",1750861851],["8QDM_U48S0iC10ne_m3eNhCgkNJ8RQ4TT6KZKP0r6Wv.Z7",1904343649]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "iVu3nUBBpYtLKy40RFSi0Gg8uJLVrxPECTmdTN30o0L.s7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
