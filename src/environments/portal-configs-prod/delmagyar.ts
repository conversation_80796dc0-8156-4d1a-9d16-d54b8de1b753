import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.delmagyar.hu/publicapi/hu',
    serverApiUrl: 'http://delmagyarfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.delmagyar.hu',
  portalName: 'DELMAGYAR',
  portalSubtitle: 'Csongrád-Csanád vármegyei hírportál',
  primaryColor: 'rgba(245, 185, 0, 1)',
  secondaryColor: 'rgba(224, 37, 30, 1)',
  linkColor: 'rgba(224, 37, 30, 1)',
  activeColor: 'rgba(245, 185, 0, 1)',
  tagColor: 'rgba(245, 185, 0, 1)',
  tagTextColor: 'white',
  logoWidth: '303px',
  city: 'Szeged',
  county: 'Csongrád-Csanád',
  facebookAppId: '379646062958838',
  googleAnalyticsId: 'UA-979753-3',
  googleTagManager: 'GTM-5HT3PC7',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'zaCbImdwAAaqxSGeMS4AgqRurmb6vcNm8gBokTSgIp3.t7',
  twitterSiteName: 'delmagyar',
  googleAnalyticsRegions: { id: 'UA-*********-5', region: 'keletiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638512532660_45653567',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["n693EuOIpXLsAcGGnT6ds9VWfCDfzJIb4BY9t895Wab.J7",2017660589],["n6KH6_MpBc2GguwIlMjOxJZwzJJHYLJQmd69lHcZvaX.s7",805285863],["1.1xf5bQ2pmrUze7ZlQt1WXifrF3NA1DsIV5DEQ62yL.L7",1093075624],["BBp3EBbXpTv2hsDIwkEdSsP3fLd0cJIPfrE9cgFisZn.K7",3091403617],["pPN8a5qdW46BVySAKQI3wbcIfkaCrA1Ra2SmaoFG_wX.F7",3541620181],["pVOHgpodBRa8ZewdcqdOBJSXbIikvPIKCM1dbw4t2eL.17",618914173],["n7jcVOMV_yImgM7cNAQkW6PLb9akn6Dw_yCvMoGpWLT.H7",2684539529],["BISMMhbku3jxeeR_Fq97rMVjb9Xf7KDwI09yAWdIMG..w7",52641537],["z4kmplORUNRh1Pvrd.TU76cRnb_ycukiywpp_1k9DfT.z7",2220667541],["zyyMZFLiuv.3HFU6hQNcl6ehjtmyQq2udJd2LxSC15b.x7",697101050],["z3_MklMEuvlnYlWReeocx8Vh.Q7fhajHI8yZxKd7Vqb.t7",2668995310],["n7mBT_MVOrxREpeAPAbFfJRlzGekdrJObu9wb0GCkrL.L7",3877340662],["dYN8nSqiWwWhwiPFOX.zMpayHGhHF9I9k9CwcPfSfmH.N7",1816268445],["1SZ3SwpqpepnUIds5nxsKpbeHBiyQtHtAU.9ZVf3vPn.l7",1191742538],["ekcsq24hqzhxk1_5H2KY6pcNbIiyo.IKy71tMjFkOYn.17",614230323],["nwIse_OZqvhcBw_GpXGk8ZbcbGmy2vIQYX0ga.wJDfb.L7",3841072721],["n6KGmOMpYAOxxIX.HEAlHfTEjrqvnq205fYZQxII7_n.F7",1524805016],[".0mMBNL4u3CMQFTRCTxc.vTDzKyvubIMULJgW3OUCkr.v7",3925952540],["dAeMBLb2u3.gXgTA6dEsHnaifLF3G5KLw_JNyOy9dQj.e7",1136158492],["n7JxdONZ2irnvYblBRXde6Qob2CkdqEMm0avZd0fMAX.r7",4001530612]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "HcmB5X1ROimq5q5E2LDbKhbzBruiB.BgMUiXXsm.0Wn.h7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
