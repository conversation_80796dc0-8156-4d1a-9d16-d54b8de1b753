import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: {
    clientApiUrl: 'https://api.szoljon.hu/publicapi/hu',
    serverApiUrl: 'http://szoljonfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'https://www.szoljon.hu',
  portalName: 'SZOLJON',
  portalSubtitle: 'Jász-Nagykun-Szolnok vármegyei hírportál',
  primaryColor: 'rgba(225, 61, 45, 1)',
  secondaryColor: 'rgba(247, 194, 42, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(6, 115, 175, 1)',
  tagColor: 'rgba(225, 61, 45, 1)',
  tagTextColor: 'white',
  logoWidth: '260px',
  city: 'Szolnok',
  county: 'Jász-Nagykun-Szolnok',
  facebookAppId: '715163528544412',
  googleAnalyticsId: 'UA-53608835-1',
  googleTagManager: 'GTM-5NH26F',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bJs7_d.LeGnteKhdhbT5u_V1fSrdTUdCUWu7QMC0d3j.X7',
  googleAnalyticsRegions: { id: 'UA-*********-5', region: 'keletiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638517689200_49214955',
    content: ` #document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["HnWHUA1yhJsbIzvHUDzNzC4u3hoOhZJU978kxRwqM87.O7",3998718389],["KN52CIT8f012SFsbX82OVy5LzIIO3KdxlyM1ZQvb7sr.E7",*********],["KHgsi4TqKiVAprq.ZrQJ2T5mHAzekcdMLdIflOe_Clz.A7",*********],["_.h3WwTCJBcLZeH.DvwVa_8GrZ52F33MhA_YSwW0etP.Y7",1661173744],["U3AhzbG7GZ0XxejquhdxfV5i.S_uJJ5YtvpRcj.D51L.57",2594477574],["_YAnAHixdJMnjgJy7v25ES68ftEOvtHWxxBaIWBTk3T.V7",2967850776],["_.5xFgVkyUI7TWjYX7xJlFxzfGc7robjyTYFZvnfGmb.o7",2916253326],["7rgna40KdIDGuFbsVIij9V4FzMyufac9HRY1uMrXLi..27",*********],["I7B3UEFwJKmhGDKHxaiVlv2QftDT0NHWfACnpjNXC7b.K7",1882695192],["U3CGBrG733hcAdaFXjnwBV5jrVOuBn3qjcWLZVcqrhX.C7",1159084262],["Hth2DwzDf00bK1rydOkOFF4FbDmufebzjVhSnm8TvjL.V7",2371976920],["fnsnku25dBp285xyEHvxoU37.QPeGJ5Wupn4B4tz2rv.Z7",2596460385],["KUCMTPfqOhQH7DSdg_D_yHy.3mQL3JHwgjSKH.mrC8D.d7",77477460],["KH6GEYWM3_Z7DtDey3D_oE4rv1re_IXeWjU92qu2m3f.G7",1292385778],["gvAhnSEmGV12QBh4WU2MtD5nnZTeRt6Kt.foQmvUdpv.17",*********],["UyZ3WbEdJOC3LjJuNt4ZxF3VbO6unecR56BSz2hiEjr.d7",2188363864],["WWnRpGhDaZBRgb6AWiIrjh0vnbqj6d4ID1CrIap9dhj.X7",3979363738],["Tkfcwn2_epiMW39.0V7LIYy6TbPb173FPGixzuoKE3z.Z7",3673522601],["7luG341r3287D8uXqFPC1U5Yv3rev4Xuircg3j7rtGD..7",2692395995],["HnWH8A1yhItGrpvXEFAl62x7nW8L2t32CaVuvpqpnmf.A7",2279514364]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "WZHR1Wh5abtT2PPueiyhJAYTtoqSQpThnr6vUsQ6FUb..7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
