import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://zaolfe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://zaolfe.apptest.content.private',
  portalName: 'ZAOL',
  portalSubtitle: 'ZALA vármegyei hírportál',
  primaryColor: 'rgba(0, 126, 187, 1)',
  secondaryColor: 'rgba(186, 152, 89, 1)',
  linkColor: 'rgba(0, 126, 187, 1)',
  activeColor: 'rgba(0, 126, 187, 1)',
  tagColor: 'rgba(186, 152, 89, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Zalaegerszeg',
  county: 'Zala',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'UA-36393106-2',
  googleTagManager: 'GTM-WCQC2Z4',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bVo1lEOYHdm4Ouose8faZNTa.BM1u4vtZusCUyJXvwP.A7',
  googleAnalyticsRegions: { id: 'UA-*********-7', region: 'nyugatiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638518449020_66024893',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["llIX7Ebt3PrxMBM9qRM0dnzJzlvOxptSdvY7J.oaLFn..7",2843375902],["kFrM8JBG46dRpWxebABpZr3lDOU5_NBu2seWG.5dI7j.q7",3316276168],["MBDB0rAD0p9bPftqOJG1adx7jddmXOeI.LbSECDquGH.p7",383415721],["m8zGVIroi.nAyOiemlz4pl8GvFOhmr.5VcfpwZO339..x7",2355156748],["YZzBC5Np0hZ8RPtl5.C16L63XHs5fv.y2tQfVD3g2Nr.67",2772735943],["NlVmZGco6GUmJu0MtY6ONF_T3WKhUQen9WNvqBdH8Bf.A7",2759322200],["Zshm8tck6H5LM74zsbCPLM4fn48Jm_8sMb.Ek8O7uSj.S7",2295225970],["wHrBogAx0l_whpBai17zDfy3.1Q2uK7Pj4iKHOBqrZX._7",1111436665],["YVpnDpOHjQhxULmgT_zjRm79r1Nx1I7gjxFntHQhXcb.h7",1241833414],["m3YX7ors3TH2P3L60_dYat5Ur5gJMo_epMyU1KLL6RL.w7",3837392038],["ZnrGu9e1iAqsn0OV6jEa2nz4jPvOJOcG1IBYfSo2f.X.27",566066691],["NptmlGbb6KVRouglIXOMpK4ijSI5_OdqafZYSnacpJr.Y7",4114462921],["xrthSbdrMosc8ToEKha8BQwJrAY21hAQnDSPzlzRGMf.u7",567570926],["y_O26vtWmLuwYe40CmEXd4zrPdXOBMezd2JFvYY.WJr.l7",1318073651],["YDi2FCCqmItxKI632Dg3Zn22buzObdtwF0_h7CapLSz.Q7",3896549483],["a0MX9Rsw3TKAsORMXmGxTX_jjRxxTecWnMFYb1z9fOn.m7",1511887378],["YDgXxSCq3WHrEzlVbBxv7r62XDA5mfAQCmO5kKJqYyv.S7",3124750527],["MLFh6bCTMgsmWongkK8ssX1WvgfOfPs.OjrxkyDKLSj.Z7",1928342612],["MA6xabAy4mAmyGq8vBJIr415.73Oba7ban8E8i0_7bj.07",3931556851],["wNERWQAUgoVGT9ByQLurzqz5vBSemL..Yd58rUXP59L.a7",1105017679]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "Yd.Mv5Mq4vru4GNirJpcLPUX9oEler68iJwZmKdA_p7.l7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
