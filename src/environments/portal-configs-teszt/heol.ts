import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://heolfe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://heolfe.apptest.content.private',
  portalName: 'HEOL',
  portalSubtitle: 'Heves vármegyei hírportál',
  primaryColor: 'rgba(6, 115, 175, 1)',
  secondaryColor: 'rgba(0, 125, 70, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(6, 115, 175, 1)',
  tagColor: 'rgba(0, 125, 70, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Eger',
  county: 'Heves',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'UA-53610610-1',
  googleTagManager: 'GTM-N9KSJL',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'cihA3Y7K66ufdUV2cwcSHMV3nFjN3jArKqPL4Mu7ShD.77',
  googleAnalyticsRegions: { id: 'UA-*********-4', region: 'eszakiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638515723502_93357244',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["bxi2eyNKO_kRmH0VzOi2eRxkTd6DCD8C0NgmwKGhSrP.r7",*********],["dDlsJ2d45gB2FQBA6mrufb1CffTrJ1OM_IBZ_ZcDLhf.87",404883999],["peTMfko9hp5shvCxutNu7b96fjqOB1NKGbL8MpCSsfr.p7",612856997],["n46xUJMXhdbM4.QgUZ.xpc11HGHrV0iL_LNXslN_gen.R7",399070032],["qsAcMI64Nms7yNXlm70GGR4_nP4mYF7_u1HD_N74OuL.p7",3122206603],["pX9nd0orMJ0B9G6S.ay1CL9MrZGOjf_pSX5WHHi.Qov.q7",96706461],["dQgWO9o02z0R4ihnsatMQ0vDzDVTCSgBo.9U_tDsgd7.c7",544498918],["pS8c.knbNfSSKTFlho.k2rzjbCvr6WfQ6w16PaZJ3Q..W7",1288321539],["qrq39Y4W4LtmxtLO95FEI.4Ajm5W6vOO.u1WO.slbqf.v7",1375826336],["byURgiOpJaLmy6_nBLkCqY_6HMG_ZEfyo1Rn6kDINUn..7",817500909],["ekCx8B5OhVe72cUoQvA6RA4d3o4mBxNdLqMPUoufq1n.77",3581229314],["1JNn10bbMB1BPW4Sss_1GK76X0uOQHexSSKGuA.wo5f.T7",2287243115],["BdKxUDn.hddsH28PPiYKhd2trXe7Yf_g6xn9zF5LWYP.v7",376201199],["pXhmgkpvixycWjeagoSvAb0QX0rrsHexLlZ2ZGpYM2D.A7",2754215523],["1WIRGbqnJWNMAxWjhkASe68pHMyOnkiXLE1Xp0PVaWb.67",3506500852],["bytnniNLMKasmALTbdxAjM0Djlzrk.OVG2HsjoZkppL.J7",2022924411],["qlpmu46Wi5M7nffRI0WAbJ9PLkW_6zNQQ9Gc5QD6vsT.C7",2896264976],["n_VnzpMjMOYB4Q4dmOap5r7sXyuOnneh1j1J_UxJ96r.07",3206304988],["1ONhokcr1XfhXY9j2ldqco8tnW6_rV9evJF2FD.Byur.G7",3935182605],["qqMWDY4q2zSRNGIYY_Fx2Y8tTUi_rT9bo_QmAoDX8p3.c7",1471315806]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "1GjBmEbudaEn5DauuWoNBhTDl39yGB0QZYI_ko4KQBv.Q7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
