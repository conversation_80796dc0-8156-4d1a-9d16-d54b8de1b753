import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://sonlinefe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://sonlinefe.apptest.content.private',
  portalName: 'SONLINE',
  portalSubtitle: 'SOMOGY vármegyei hírportál',
  primaryColor: 'rgba(223, 223, 222, 1)',
  secondaryColor: 'rgba(6, 114, 177, 1)',
  linkColor: 'rgba(6, 114, 177, 1)',
  activeColor: 'rgba(6, 114, 177, 1)',
  tagColor: 'rgba(6, 114, 177, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Kaposvár',
  county: 'Somogy',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'NotSet',
  googleTagManager: 'GTM-T3ZPQ6',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bDs7W9BLsDYnKXFttOOCAbcffQuYWccbPqTr3t4V7R7',
};
