import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://feolfe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://feolfe.apptest.content.private',
  portalName: 'FEOL',
  portalSubtitle: 'fejér vármegyei hírportál',
  primaryColor: 'rgba(0, 125, 70, 1)',
  secondaryColor: 'rgba(253, 207, 50, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(0, 125, 70, 1)',
  tagColor: 'rgba(6, 115, 175, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Székesfehérvár',
  county: 'Fejér',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'UA-36393106-3',
  googleTagManager: 'GTM-KVQJWJD',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'zZA1lCNBHVlOkuo8hjNwytU6rsxNgGbDBMa1zbtWynH.v7',
  googleAnalyticsRegions: { id: 'UA-*********-7', region: 'nyugatiMegyeiTracker' },
};
