import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://teolfe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://teolfe.apptest.content.private',
  portalName: 'TEOL',
  portalSubtitle: 'TOLNA vármegyei hírportál',
  primaryColor: 'rgba(230, 58, 99, 1)',
  secondaryColor: 'rgba(6, 115, 175, 1)',
  linkColor: 'rgba(6, 115, 175, 1)',
  activeColor: 'rgba(6, 115, 175, 1)',
  tagColor: 'rgba(230, 58, 99, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Szekszárd',
  county: 'Tolna',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'UA-53600193-1',
  googleTagManager: 'GTM-KHKKQP',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bOfqH9B6N3G3V1KlvsHUcrd8rjvwEANw74NFJFiny2v.M7',
  twitterSiteName: 'teol_hu',
  googleAnalyticsRegions: { id: 'UA-*********-6', region: 'deliMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638517466936_28824633',
    content: ` #document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["ipNn4GLh7LHbJDNQ3dXfBjddbmJlYl._WiwAzC.lgcL..7",2326076022],["_n0cdALxYqKmKTd.ZbK51AXSX.4qNjUQdPvnguYRZ0..R7",1397491569],["HyfGhphCVxg7GkjWO39HyzRZrL9XRlV4N_BHF_UmP8..S7",3402669989],["hWgcQi8TYpsGA.JHXBpvtfagryYq6wQi1RMjCfnTTTj.e7",1343859242],["H9u8W5fDwj0KnvKXHma3wRPsT22Hc0RBhDyG.jlWPQj.Y7",1225445148],["KuzB34MYMYibDz.0Ve.eaibM.51l2SR4igBzui.s2V3._7",69147668],["WsO8jPM3wrRLyghYcQ6CTEQy7Wkn5_ve0WSJnSDekG..Q7",2083919009],["TwtsCwf.EihA1S2gQl7YMBdhjZCVcSvu8JqWcK9niBv.C7",3550293340],["hRUc5C7xYlIAbjeY86ZxrXTQrzFinASXbJLzSfu_mbj.T7",591711124],["_izBrQOxMRF2LmneHTYHoVTDn4iS5mPSrDT2a2e_3eL.o7",3011643659],["H9URxpgh4ealprtX4iiAwOYGvO1aCfVorO_NVsgnj0T.x7",828009872],["JEkWr9xwByWlaNROtJv1ogak.70qvSSIfKrMXPALuaT.D7",2265775161],["WsMWS.M3B6VxEs5rPVZFsBcDzqWVGB.7wMmNRMcd0Cj.07",1421740707],["KfzH24NIjETBI6jnuUwiFvaer8cqVASLggTW7tsVrPT.f7",3129917166],["f3u3MHfpnFgANyjwRp.K1SRnn9hX6GQiQUh8ikkIXTD.Q7",2044936762],["JAXGL9x_V8_qk290KKQiD0RiDOMn9xWCYVKHCa0Dd83.27",185143500],["WsO3ofM3nD8W4sORbVrDxBQbvF2HnfTIVFZ9gj1Xb6P.U7",3097567024],["VMjHe0ycjDWE2ajZk6FmURR6HoGH3DA86rz9kaBEXK3.i7",4158593571],["JXIRzU764eXVt.phe4ovbkVGbqbCC1C81ZoQNWG8NRL.x7",395422648],["9HmxPVvZQdHqTQTEbO7pKwY0rJoqjVT6n5mAXuLsrHT.O7",2591540135]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "7VIcejIJYsY6mk5VAiDHMv2fxW87Ke_PnmW_o7OyAaj.U7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
