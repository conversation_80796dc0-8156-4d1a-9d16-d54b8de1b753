import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://veolfe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://veolfe.apptest.content.private',
  portalName: 'VEOL',
  portalSubtitle: 'Veszprém vármegyei hírportál',
  primaryColor: 'rgba(0, 92, 162, 1)',
  secondaryColor: 'rgba(218, 37, 28, 1)',
  linkColor: 'rgba(0, 92, 162, 1)',
  activeColor: 'rgba(0, 92, 162, 1)',
  tagColor: 'rgba(218, 37, 28, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Veszprém',
  county: 'Veszprém',
  facebookAppId: 'NotSet',
  googleAnalyticsId: '***********-1',
  googleTagManager: 'GTM-NGTJQGW',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bPCV9MsJ5cJrgo0jAAwLNfV4.KmdlIxhmscCYEi_v0H.87',
  gemiusScript: '',
};
