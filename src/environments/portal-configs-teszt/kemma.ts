import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://kemmafe.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://kemmafe.apptest.content.private',
  portalName: 'KEMMA',
  portalSubtitle: 'komárom-esztergom vármegyei hírportál',
  primaryColor: 'rgba(247, 194, 42, 1)',
  secondaryColor: 'rgba(228, 56, 141, 1)',
  linkColor: 'rgba(0, 125, 70, 1)',
  activeColor: 'rgba(0, 125, 70, 1)',
  tagColor: 'rgba(0, 125, 70, 1)',
  tagTextColor: 'white',
  logoWidth: '217px',
  city: 'Tatabánya',
  county: 'Komárom-Esztergom',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'UA-53599099-1',
  googleTagManager: 'GTM-PP4D4R',
  googleSiteKey: '6LcoTuIqAAAAACNDhcViYrpOt7Rq9Qwxx3YtEcx0',
  gemiusId: 'bO5Kf1iTj7_kpzRzZhmdf7d8.Bjwk..IJbHbY3NDfEP.f7',
  twitterSiteName: 'kemmaponthu',
  googleAnalyticsRegions: { id: 'UA-*********-4', region: 'eszakiMegyeiTracker' },
  gemiusIframe: {
    id: 'gemius_hcconn_1638513300295_21956133',
    content: `#document
        <html><head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                <title>ls</title>
        </head>
        <body>
                <script type="text/javascript">
                        var ssl = (document.location && document.location.protocol && document.location.protocol=='https:')?1:0;
                        var lsdata = '-GREFRESH';
                        var lsvalid = null;
                        function convert(value) {
                                var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_.";
                                var eparams = [["n6dlOFCLQQWdo2j9NKYwja9Pvl4mHvQ2RwcysRzantb.M7",3444082898],["CgawQTr.O26Y84.orvci4v5p3Ue_hwBoBzu8yVRl4Vr.t7",1850463740],["CgYQaDr.28rD6SrQp6hkX5_2jQdWPuBYBvKJCy2aqUr.27",2184003691],["ChlqHDrAZ3oN3dd3T_kTox6nn1WOLOfzanEF5ZsZ6ij.P7",4180873592],["b9PKVeAPB0XTJNc1dIbHvTw7jWy7LOAmkujGYkG.qXH.u7",19068734],["1QtgH3dsi2FIplBLMdlaQzwLjQK7TOAaYfg89vGFoZ3.l7",1100276656],["z9nLxMBHPEbYkNdOJKKaLkvNDNC7Z5jjXBhyoG5KxZH.V7",2075846929],["BIIazISJt2Q4_pwDYrTdEBv_ztDrapSzeYNPzHaNGlj.K7",1669999660],["BHTLnYQqPD43sscGrYMiotzmrDVTPNj8XJwyhXdq9Xv.87",2479304359],["Cm9lczuiQbNtEL4FJ7CuJRxb3dzrEQBcwiXMLY2s1Ub.r7",2910394808],["b4e1uOCQ8VqoYJjTtB.EYO05.1lTDKfQTwNFJtYVtg7.K7",2420912949],["qsa79kscTL7zIWcPx4qG6CvurMTrAdmcvDi8.B3CSMD.77",3441993295],["BYtgtfe0i6qYo0UhwVE5OM1sDKWDeZjhGVf.hr7MyZT.b7",1875385506],["dYFqGpcYZ0MDuU1imdRu3Z9GT8RWDsiuyr91EoCwSrb.17",3898350117],["zynKhcCHB4Ydw3y4qFBhG0vNjZG7Z_B0f3S27Xz4iUD.d7",2281148222],["pUGwqAeTO3bOo_ZoZra_0b2r.xSDEKgSr8S7LLZn2mf.r7",1421680219],["BMxq9YQnZ3E4DZ1Z6rmuFtzVrKBTStke6XxfoOc1FT3.Q7",1522109308],["dKy79CSqTIhNmPvymsOAWB42jSeOGuBoajvzihsOyTL.R7",2384455058],["2uvF07t54YeoxlOkIzlh9v6ZXMu_Z7kxygKJaw7q7.H.i7",2829687418],["1J_1YQPx8RFyReLYCVfVWyv.r3zr04hfSXbFGE4BzsT.M7",4265288102]];
                                var eparam = eparams[Math.floor(Math.random()*eparams.length)];
                                var seed = eparam[1];
                                var result = eparam[0];
                                if (typeof value != "string" || value.length==0 || value.charAt(0)=='-') {
                                        return value;
                                } else {
                                        for (var i=0; i<value.length; ++i) {
                                                var ch = value.charAt(i);
                                                var pos = chars.indexOf(ch);
                                                if (ch=='|') {
                                                        return result + value.slice(i, value.length);
                                                } else if (pos != -1) {
                                                        result = result + chars[(pos + Math.floor(seed / 67108864)) % chars.length];
                                                } else {
                                                        result = result + ch;
                                                }
                                                seed = ((seed * 1664525) + 1013904223) % 4294967296;
                                        }
                                        return result;
                                }
                        }
                        function refresher() {
                                var n = (new Date()).getTime();
                                if (lsvalid==null || lsvalid>n) {
                                        try {
                                                localStorage.gstorage = lsdata;
                                        } catch (e) {}
                                }
                        }
                        function msgreceiver(e) {
                                if (typeof e.data=="string" && e.data.substr(0,23)=="_xx_gemius_set_add_xx_/") {
                                        try {
                                                localStorage.gaddstorage = e.data.substr(23);
                                        } catch (e) {}
                                }
                                if (typeof e.data=="string" && e.data=="_xx_gemius_get_add_xx_" && typeof window.postMessage!='undefined') {
                                        try {
                                                if (!localStorage.gaddstorage) {
                                                        parent.postMessage("_xx_gemius_add_xx_/","*");
                                                } else {
                                                        parent.postMessage("_xx_gemius_add_xx_/"+localStorage.gaddstorage,"*");
                                                }
                                        } catch (e) {
                                                parent.postMessage("_xx_gemius_add_xx_/-GETERR","*");
                                        }
                                }
                                if (e.origin=="https://ls.hit.gemius.pl" && typeof e.data=="string" && e.data.substr(0,24)=="_xx_gemius_internal_xx_/" && ssl==0) {
                                        var data = e.data.substr(24);
                                        var lsvalidts = (new Date()).getTime()+(1000*86400*14);
                                        if (data!='' && data.charAt(0)!='-') {
                                                localStorage.gstorage = data+"|"+lsvalidts;
                                        }
                                        parent.postMessage("_xx_gemius_xx_/"+convert(localStorage.gstorage),"*");
                                }
                        }
                        if (typeof window.postMessage != 'undefined') {
                                try {
                                        lsdata = localStorage.gstorage;
                                        if (lsdata && lsdata.length>46 && lsdata.charAt(46)!='/') {
                                                lsdata = lsdata.slice(0,46)+lsdata.slice(lsdata.lastIndexOf('/'),lsdata.length);
                                        }
                                        if (lsdata && lsdata.charAt(0)!='-') {
                                                var m = lsdata.match(/\|(\d+)$/);
                                                if (m) {
                                                        lsvalid = m[1];
                                                }
                                                setInterval("refresher()",1000);
                                        }
                                        if (ssl) {
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-') {
                                                        localStorage.gstorage = "qpe1wEtW8SUJYuotMjuHRgb79rGt0rdfeLFK5y4tS9D.M7/"+new String((new Date()).getTime())+"/";
                                                }
                                                lsdata = localStorage.gstorage;
                                                parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                try {
                                                        if (window.location.search.indexOf("mode=new") != -1) {
                                                                parent.postMessage("_xx_gemius_internal_xx_/"+lsdata,"http://ls.hit.gemius.pl");
                                                        }
                                                } catch (e) {
                                                }
                                        } else {
                                                var m;
                                                var n;
                                                if (lsdata) {
                                                        m = lsdata.match(/^([A-Z0-9a-z\.\_\/]*).*\|([0-9]+)$/);
                                                        n = (new Date()).getTime() + 60000;
                                                }
                                                if (!lsdata || lsdata=='' || lsdata.charAt(0)=='-' || !m || m[2]<n) {
                                                        var f = document.createElement('iframe');
                                                        f.setAttribute('width',0);
                                                        f.setAttribute('height',0);
                                                        f.setAttribute('scrolling','no');
                                                        f.style.display="none";
                                                        f.style.visibility="hidden";
                                                        document.body.appendChild(f);
                                                        f.setAttribute('src','https://ls.hit.gemius.pl/lsget.html?mode=new');
                                                } else {
                                                        parent.postMessage("_xx_gemius_xx_/"+convert(lsdata),"*");
                                                }
                                        }
                                } catch (e) {
                                        parent.postMessage("_xx_gemius_xx_/-GETERR","*");
                                }
                                if (window.addEventListener) {
                                        window.addEventListener('message', msgreceiver, false);
                                } else if (window.attachEvent) {
                                        window.attachEvent('onmessage', msgreceiver);
                                }
                        }
                </script>


</body></html>`,
  },
};
