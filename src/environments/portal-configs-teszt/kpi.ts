import { PortalConfig } from '../environment.definitions';

/* eslint-disable */
export const portalConfig: PortalConfig = {
  apiUrl: 'http://kpife.apptest.content.private/publicapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  siteUrl: 'http://kpife.apptest.content.private',
  portalName: 'KPI',
  portalSubtitle: '',
  primaryColor: 'rgba(225, 225, 225, 1)',
  secondaryColor: 'rgba(225, 225, 225, 1)',
  linkColor: 'rgba(225, 225, 225, 1)',
  activeColor: 'rgba(225, 225, 225, 1)',
  tagColor: 'rgba(225, 225, 225, 1)',
  tagTextColor: 'white',
  logoWidth: '280px',
  city: '',
  county: '',
  facebookAppId: 'NotSet',
  googleAnalyticsId: 'NotSet',
  googleTagManager: 'NotSet',
  googleSiteKey: 'NotSet',
  gemiusId: 'NotSet',
};
