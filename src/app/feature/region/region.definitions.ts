import { Params } from '@angular/router';
import { ApiResponseMetaList, ApiResult, ArticleCard, LimitableQuery } from '@trendency/kesma-ui';

export type RegionResolverData = Readonly<{
  articles: ApiResult<ArticleCard[], ApiResponseMetaList>;
  region: Region;
}>;

export type RegionQuery = LimitableQuery &
  Readonly<{
    'imageFieldConfig[thumbnail][width]'?: number;
    'imageFieldConfig[thumbnail][height]'?: number;
  }>;

export type BackendRegion = Readonly<{
  id: string;
  title: string;
  slug: string;
  latitude?: string;
  longitude?: string;
  description: string;
  parentId?: string;
  parentTitle?: string;
  parentDescription?: string;
  thumbnailUrlCreatedAt?: string;
  thumbnailUrl?: string;
  fullSizeUrlCreatedAt?: string;
  fullSizeUrl?: string;
  blazonThumbnailUrlCreatedAt: string;
  blazonThumbnailUrl: string;
  blazonFullSizeUrlCreatedAt: string;
  blazonFullSizeUrl: string;
  long_description: string;
}>;

export type Region = Readonly<{
  id: string;
  title: string;
  slug: string;
  latitude: number;
  longitude: number;
  description: string;
  parent?: {
    id: string;
    title: string;
    description?: string;
  };
  thumbnail?: {
    createdAt: Date;
    url: string;
  };
  fullSize?: {
    createdAt: Date;
    url: string;
  };
  blazon?: {
    thumbnail: {
      createdAt: Date;
      url: string;
    };
    fullSize: {
      createdAt: Date;
      url: string;
    };
  };
  longDescription: string;
}>;

export type CombineRegionRequests = [ApiRequest, Params];

export type ApiRequest = Readonly<{ data: RegionResolverData }>;
