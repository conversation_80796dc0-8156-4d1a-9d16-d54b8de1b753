import { BackendRegion, Region } from './region.definitions';
import { backendDateToDate } from '@trendency/kesma-core';

export const backendRegionToRegion = ({
  id,
  title,
  slug,
  latitude,
  longitude,
  description,
  parentId,
  parentTitle,
  parentDescription,
  thumbnailUrlCreatedAt,
  thumbnailUrl,
  fullSizeUrlCreatedAt,
  fullSizeUrl,
  blazonThumbnailUrlCreatedAt,
  blazonThumbnailUrl,
  blazonFullSizeUrlCreatedAt,
  blazonFullSizeUrl,
  long_description,
}: BackendRegion): Region => ({
  id,
  title,
  slug,
  latitude: parseFloat(latitude ?? ''),
  longitude: parseFloat(longitude ?? ''),
  description,
  parent: parentId
    ? {
        id: parentId ?? '',
        title: parentTitle ?? '',
        description: parentDescription,
      }
    : undefined,
  thumbnail: thumbnailUrl
    ? {
        createdAt: backendDateToDate(thumbnailUrlCreatedAt ?? '') ?? new Date(),
        url: thumbnailUrl,
      }
    : undefined,
  fullSize: fullSizeUrl
    ? {
        createdAt: backendDateToDate(fullSizeUrlCreatedAt ?? '') ?? new Date(),
        url: fullSizeUrl,
      }
    : undefined,
  blazon: blazonFullSizeUrl
    ? {
        fullSize: {
          createdAt: backendDateToDate(blazonFullSizeUrlCreatedAt) ?? new Date(),
          url: blazonFullSizeUrl,
        },
        thumbnail: {
          createdAt: backendDateToDate(blazonThumbnailUrlCreatedAt) ?? new Date(),
          url: blazonThumbnailUrl,
        },
      }
    : undefined,
  longDescription: long_description,
});
