@use 'shared' as *;

section.region {
  .wrapper {
    > .left-column {
      .top-section {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .short-lead {
          font-size: 20px;
          line-height: 21px;
          position: relative;
        }

        .time {
          color: $grey-7;
          font-size: 14px;
          line-height: 21px;
        }
      }

      h1 {
        font-weight: 500;
        font-size: 60px;
        line-height: 70px;
        margin-bottom: 30px;
        @include media-breakpoint-down(md) {
          font-size: 30px;
          line-height: 40px;
        }
      }

      .region-header {
        font-size: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 15px 0 15px 20px;
        margin-bottom: 50px;
        margin-top: -5px;
        text-transform: uppercase;
        font-weight: 500;
        @include article-before-line();

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }
      }

      .lead {
        font-weight: normal;
        font-size: 30px;
        line-height: 50px;
        margin-bottom: $block-bottom-margin;
        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
          font-size: 20px;
          line-height: 30px;
        }
      }

      app-social-row {
        margin-bottom: calc(#{$block-bottom-margin} - 15px);

        @include media-breakpoint-down(md) {
          margin-bottom: calc(#{$block-bottom-margin-mobile} - 15px);
        }
      }

      .block-recommendation {
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }

        @include media-breakpoint-down(sm) {
          width: calc(100% + 30px);
          margin-left: -15px;
          margin-right: -15px;

          app-article-card .article-block[class] {
            border-radius: 0;
            border-left-width: 0;
            border-right-width: 0;
            padding-left: 0;
            padding-right: 15px;
          }
        }
      }

      app-section-header {
        .section-header {
          margin-bottom: 0;
        }
      }

      .block-show-comments {
        display: block;
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }

        .show-comments-btn {
          display: block;
          width: 100%;
          height: 50px;
        }
      }

      .cards-container {
        padding-top: 30px;
        display: flex;
        margin-bottom: $block-bottom-margin;
        flex-wrap: wrap;
        width: calc(100% + 30px);
        margin-left: -15px;
        margin-right: -15px;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }
      }

      .block-comments {
        display: block;
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }
      }
    }

    > aside {
      .cards-container {
        display: block;
      }

      app-region-sidebar {
        display: inline-block;
        margin-bottom: 50px;
      }
    }
  }
}
