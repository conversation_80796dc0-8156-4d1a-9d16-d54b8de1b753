import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { ArticleService } from '../article/article.service';
import { RegionService } from './region.service';
import { RedirectService } from '@trendency/kesma-ui';

@Injectable()
export class RegionResolverService {
  constructor(
    private readonly articleService: ArticleService,
    private readonly regionService: RegionService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const { regionSlug } = route.params;
    const { page } = route.queryParams || {};

    const STATIC_ARTICLE_AMOUNT = 14;

    return forkJoin({
      regions: this.regionService.getRegionList(),
      articles: this.articleService.getRegionArticles(regionSlug, 0, STATIC_ARTICLE_AMOUNT),
    }).pipe(
      map(({ regions, articles }) => ({
        region: (regions?.data ?? []).find(({ slug }: { slug: string }) => regionSlug === slug),
        articles,
      })),
      tap(({ articles }) => {
        if (this.redirectService.shouldBeRedirect(page, articles?.data ?? []) || Number(page) > (articles?.meta?.limitable?.pageMax ?? 0)) {
          this.redirectService.redirectOldUrl(`regio/${regionSlug}`, false, 302);
        }
      }),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(() => error);
      })
    );
  }
}
