<section class="region">
  <app-region-header [region]="region"></app-region-header>

  <iframe
    *ngIf="isDesktopAd && portalName === 'ERDON'"
    [src]="thirdPartyAdUrls?.desktop?.frameUrl"
    allow="autoplay"
    id="aac22075"
    name="aac22075"
    scrolling="no"
    style="display: block; margin: 10px auto 40px; width: 100%; max-width: 970px; height: 250px; border: 0"
    ><a [href]="thirdPartyAdUrls?.desktop?.linkUrl" target="_blank"><img [src]="thirdPartyAdUrls?.desktop?.imgUrl" alt="" /></a
  ></iframe>

  <iframe
    *ngIf="isMobileAd && portalName === 'ERDON'"
    [src]="thirdPartyAdUrls?.mobile?.frameUrl"
    allow="autoplay"
    id="a4fd0e2b"
    name="a4fd0e2b"
    scrolling="no"
    style="display: block; margin: 10px auto 40px; width: 100%; max-width: 300px; height: 250px; border: 0"
    ><a [href]="thirdPartyAdUrls?.mobile?.linkUrl" target="_blank"><img [src]="thirdPartyAdUrls?.mobile?.imgUrl" alt="" /></a
  ></iframe>

  <div *ngIf="region" class="wrapper with-aside">
    <article class="left-column">
      <ng-container *ngFor="let blocks of articleListIteration; let i = index">
        <app-article-card *ngIf="mainArticle" [article]="blocks[0]" [isTagVisible]="true" [styleID]="articleCardType.STYLE1"></app-article-card>

        <ng-container *ngFor="let section of blocks[1]; let j = index">
          <div *ngIf="section?.length > 0" class="cards-container">
            <div *ngFor="let article of section" class="col-12 col-md-6">
              <app-article-card [article]="article" [isSubTitleVisible]="true" [isTagVisible]="true" [styleID]="articleCardType.STYLE5"></app-article-card>
            </div>
          </div>
          <ng-container *ngIf="i === 0 && j === 0">
            <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
            <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 1 && j === 1">
            <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
            <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
          </ng-container>
        </ng-container>
      </ng-container>

      <!-- cikk ajánló blokk -->
      <div #articleList>
        <app-section-header sectionTitle="További friss hírek"></app-section-header>
      </div>
      <div *ngIf="otherNews?.length" class="cards-container">
        <div *ngFor="let article of otherNews" class="col-12 col-md-12">
          <app-article-card [article]="article" [isSubTitleVisible]="true" [isTagVisible]="true" [styleID]="articleCardType.STYLE6"></app-article-card>
        </div>
      </div>

      <app-pager
        (pageChange)="onPageChange($event)"
        [allowAutoScrollToTop]="false"
        [currentPage]="currentPage"
        [rowAllCount]="maxCount"
        [rowOnPageCount]="maxPerPage"
      ></app-pager>
    </article>

    <aside>
      <app-region-sidebar [region]="region"></app-region-sidebar>
      <app-sidebar [articleSlug]="mainArticle?.slug" [categorySlug]="mainArticle?.columnSlug"></app-sidebar>
    </aside>
  </div>
</section>
