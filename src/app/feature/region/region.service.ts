import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BackendRegion, Region } from './region.definitions';
import { backendRegionToRegion } from './region.utils';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';

const ALL_REGIONS = 99999999999;

@Injectable({
  providedIn: 'root',
})
export class RegionService {
  constructor(private readonly reqService: ReqService) {}

  getRegionList(): Observable<ApiResult<Region[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<BackendRegion[], ApiResponseMetaList>>(`/source/content-group/regions?rowCount_limit=${ALL_REGIONS}`).pipe(
      map(({ data, meta }) => ({
        data: data.map(backendRegionToRegion),
        meta,
      }))
    );
  }
}
