import { ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard } from '@trendency/kesma-ui';
import { combineLatest, Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { chunkArray, IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { NgFor, NgIf } from '@angular/common';
import {
  ArticleCardComponent,
  ArticleCardTypes,
  defaultMetaInfo,
  generateThirdPartyAdUrls,
  List,
  PagerComponent,
  PortalConfigService,
  RegionHeaderComponent,
  RegionSidebarComponent,
  SectionHeaderComponent,
  ThirdPartyAdUrls,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { Region } from './region.definitions';
import { ArticleService } from '../article/article.service';

const OTHER_RECENT_NEWS_PER_PAGE = 8;

@Component({
  selector: 'app-region',
  templateUrl: './region.component.html',
  styleUrls: ['./region.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    RegionHeaderComponent,
    NgIf,
    NgFor,
    ArticleCardComponent,
    AdvertisementAdoceanComponent,
    SectionHeaderComponent,
    PagerComponent,
    RegionSidebarComponent,
    SidebarComponent,
  ],
})
export class RegionComponent implements OnInit, OnDestroy {
  readonly articleCardType = ArticleCardTypes;

  @ViewChild('articleList') readonly articleList: ElementRef;

  region?: Region;
  articles: List<ArticleCard> = [];
  mainArticle: ArticleCard;
  secondLargeArticle: ArticleCard;
  firstArticleBlock: ArticleCard[][] = [];
  secondArticleBlock: ArticleCard[][] = [];
  otherNews: List<ArticleCard> = [];
  currentPage = 0;
  maxCount = 0;
  maxPerPage = OTHER_RECENT_NEWS_PER_PAGE;
  articleListIteration: any = [];
  adverts: AdvertisementsByMedium;

  thirdPartyAdUrls: ThirdPartyAdUrls = generateThirdPartyAdUrls(this.sanitizer);
  isDesktopAd: boolean = this.utilsService.isBrowser() && window?.innerWidth > 768;
  isMobileAd: boolean = this.utilsService.isBrowser() && window?.innerWidth <= 768;
  portalName: string;

  private readonly unsubscribe$ = new Subject<boolean>();

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly articleService: ArticleService,
    private readonly seo: SeoService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly sanitizer: DomSanitizer
  ) {
    this.portalName = portalConfig.portalName;
  }

  ngOnInit(): void {
    combineLatest([this.route.data, this.route.queryParams])
      .pipe(
        switchMap(
          ([
            {
              data: {
                articles: { data },
                region,
              },
            },
            queryParam,
          ]): Observable<Advertisement[]> => {
            if (data) {
              this.firstArticleBlock = chunkArray(data.slice(1, 9), 6, 2);
              this.secondArticleBlock = chunkArray(data.slice(10, 14), 4);

              this.articles = data;

              this.mainArticle = data[0];
              this.secondLargeArticle = data[9];

              this.articleListIteration = [
                [this.mainArticle, this.firstArticleBlock],
                [this.secondLargeArticle, this.secondArticleBlock],
              ];
            }
            this.region = region;

            this.setMetaData();

            this.fetchBottomArticleList(queryParam?.['page']);

            return this.adStoreAdo.advertisemenets$;
          }
        ),
        map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((ads: AdvertisementsByMedium): void => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  onPageChange(pageNumber: number): void {
    this.currentPage = pageNumber;
    // this.fetchRecentNews();

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
    });
  }

  private fetchBottomArticleList(page: number): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const mainNewsArticleExcludeId: string[] = this.articles
      .slice(0, 14)
      .map((a): string => a.id ?? '')
      .reduce((acc: string[], curr: string): string[] => {
        acc = [...acc, curr];
        return acc;
      }, []);

    if (page || page > 0) {
      this.currentPage = page - 1;
    }

    this.articleService
      .getRegionArticles(this.region?.slug ?? '', this.currentPage, 8, undefined, undefined, mainNewsArticleExcludeId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(({ data, meta }) => {
        this.otherNews = data ?? [];
        this.maxCount = meta.limitable.rowAllCount ?? 0;
        this.changeRef.detectChanges();

        if (this.articleList && this.utilsService.isBrowser() && page) {
          setTimeout(() => {
            this.scrollToFirstArticle();
          }, 50);
        }
      });
  }

  private setMetaData(): void {
    if (!this.region) {
      return;
    }

    let metaData: IMetaData = {
      ...defaultMetaInfo(this.portalConfig),
      title: `${this.portalConfig?.portalName} - ${this.region.title}`,
      description: this.region.description || this.portalConfig.portalSubtitle,
      ogTitle: this.region.title,
    };
    if (this.region.blazon) {
      metaData = {
        ...metaData,
        ogImage: this.region.blazon.thumbnail.url,
      };
    }

    this.seo.setMetaData(metaData);
  }

  private scrollToFirstArticle(): void {
    const offsetMargin = 50;
    window.scrollTo(0, (this.articleList.nativeElement as HTMLElement)?.offsetTop - offsetMargin);
  }
}
