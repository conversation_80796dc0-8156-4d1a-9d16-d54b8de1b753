import { Location, NgClass, NgFor, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, Renderer2, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { isEqual } from 'lodash-es';
import { combineLatest, Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, map, switchMap, takeUntil } from 'rxjs/operators';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, Column, createCanonicalUrlForPageablePage, RedirectService } from '@trendency/kesma-ui';
import {
  DateRangePickerComponent,
  defaultMetaInfo,
  List,
  ListResult,
  mapSearchResultToArticleCard,
  MAX_RESULTS_PER_PAGE,
  PortalConfigService,
  SearchPageBaseComponent,
} from '../../shared';
import { InputClearKey, InputFocusKey, ResolvedData, SearchResult } from './search-by-keyword-page.definitions';
import { ArticleService } from '../article/article.service';
import {
  mapQueryParamsToSearchParamsAndPage,
  mapSearchParamsAndPageToQueryParams,
  mapSearchParamsToArticleServiceSearchQuery,
} from './search-by-keyword-page.utils';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'app-search-by-keyword-page',
  templateUrl: './search-by-keyword-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./search-by-keyword-page.component.scss'],
  imports: [SearchPageBaseComponent, FormsModule, ReactiveFormsModule, NgClass, NgIf, NgFor, SidebarComponent, DateRangePickerComponent, DateFnsModule],
})
export class SearchByKeywordPageComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('categoryInput') categoryInput: ElementRef;
  @ViewChild('dateRangePicker') dateRangePicker?: DateRangePickerComponent;

  readonly searchForm = new UntypedFormGroup({
    text: new UntypedFormControl(null),
    author: new UntypedFormControl(null),
    category: new UntypedFormControl(null),
    fromDate: new UntypedFormControl(null),
    toDate: new UntypedFormControl(null),
  });

  readonly maxDate = new Date();

  title: string;
  page = 0;

  results: List<ArticleCard>;
  totalResultCount: number;
  isLoading = true;

  isTextInputSelected = false;
  isAuthorInputSelected = false;
  isCategoryListOpen = false;

  categories: List<Column>;
  articles$: Observable<ResolvedData> = this.route.data.pipe(map((res) => res['data']));

  private readonly searchRequest$ = new Subject<void>();
  private readonly searchResult$: Observable<SearchResult>;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly articleService: ArticleService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly location: Location,
    private readonly seo: SeoService,
    private readonly renderer2: Renderer2,
    private readonly changeRef: ChangeDetectorRef,
    private readonly redirectService: RedirectService
  ) {
    this.bindMethods();
    this.searchResult$ = this.searchRequest$.pipe(switchMap(() => this.fetchResults()));
  }

  ngOnInit(): void {
    this.articles$.pipe(takeUntil(this.destroy$)).subscribe((data: ResolvedData) => {
      this.results = data?.articles?.items;
      this.totalResultCount = data?.articles?.totalCount;
      this.isLoading = false;
      this.changeRef.markForCheck();
    });
    this.searchResult$.pipe(takeUntil(this.destroy$)).subscribe(({ items, totalCount }) => {
      this.results = items;
      this.totalResultCount = totalCount;
      this.changeRef.markForCheck();
    });

    this.initCategoryInputClickListener();

    this.populateDataFromResolver();
    this.populateSearchFormAndPageFromQueryParams();
    this.setSeoMetaData();

    this.resetPageOnSearchFormChanges();
    this.searchOnDateRangeChanges();
  }

  ngAfterViewInit(): void {
    const fromDate = this.searchForm.get('fromDate')?.value;
    const toDate = this.searchForm.get('toDate')?.value;
    if (fromDate && toDate) {
      this.dateRangePicker?.updateDateRange?.next([fromDate, toDate]);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  search(): void {
    this.title = this.searchForm.get('text')?.value;
    this.updateQueryParams();
    this.searchRequest$.next();
    this.changeRef.detectChanges();
  }

  setInputFocus(key: InputFocusKey, value: boolean): void {
    switch (key) {
      case 'text':
        this.isTextInputSelected = value;
        break;
      case 'author':
        this.isAuthorInputSelected = value;
        break;
    }
  }

  clearInput(key: InputClearKey): void {
    switch (key) {
      case 'text':
        this.searchForm.get('text')?.setValue(undefined);
        break;
      case 'author':
        this.searchForm.get('author')?.setValue(undefined);
        break;
      case 'category':
        this.searchForm.get('category')?.setValue(undefined);
        break;
    }

    this.search();
  }

  handleClearDateClick($event: Event): void {
    $event.stopImmediatePropagation();
  }

  toggleCategoryList(): void {
    this.isCategoryListOpen = !this.isCategoryListOpen;
  }

  onAllCategoriesButtonClick(): void {
    this.clearInput('category');
    this.isCategoryListOpen = false;
  }

  selectCategory(category: Column): void {
    this.searchForm.get('category')?.setValue(category);
    this.isCategoryListOpen = false;
    this.search();
  }

  onPageChange(page: number): void {
    this.page = page - 1;
    this.search();
  }

  handleDateChange(event: { start: Date | null; end: Date | null }): void {
    this.searchForm?.patchValue({
      fromDate: event.start,
      toDate: event.end,
    });
  }

  private populateDataFromResolver(): void {
    this.categories = this.route.snapshot.data['data'].categories;
  }

  private populateSearchFormAndPageFromQueryParams(): void {
    const { searchParams, page } = mapQueryParamsToSearchParamsAndPage(this.route.snapshot.queryParams, this.categories);
    this.searchForm.setValue(searchParams);
    this.page = page > 0 ? page - 1 : 0;
  }

  private resetPageOnSearchFormChanges(): void {
    this.searchForm.valueChanges.pipe(distinctUntilChanged(isEqual), takeUntil(this.destroy$)).subscribe(() => this.resetPage());
  }

  private searchOnDateRangeChanges(): void {
    const fromDateField = this.searchForm.get('fromDate');
    const toDateField = this.searchForm.get('toDate');
    if (fromDateField && toDateField) {
      combineLatest([fromDateField.valueChanges, toDateField.valueChanges])
        .pipe(debounceTime(500))
        .subscribe(() => this.search());
    }
  }

  private updateQueryParams(): void {
    const queryParams = mapSearchParamsAndPageToQueryParams({
      searchParams: this.searchForm.value,
      page: this.page + 1,
    });
    const urlTree = this.router.createUrlTree([], { relativeTo: this.route, queryParams });
    this.location.go(urlTree.toString());
  }

  private fetchResults(): Observable<ListResult<ArticleCard>> {
    const searchQuery = mapSearchParamsToArticleServiceSearchQuery(this.searchForm.value);

    this.isLoading = true;

    return this.articleService.searchArticleByKeyword(searchQuery, this.page, MAX_RESULTS_PER_PAGE).pipe(
      map(({ data, meta }) => {
        if (this.redirectService.shouldBeRedirect(this.page, data)) {
          this.redirectService.redirectOldUrl('kereses', false, 302);
        }
        return {
          items: data.map(mapSearchResultToArticleCard),
          totalCount: meta.limitable.rowAllCount ?? 0,
        };
      }),
      finalize(() => (this.isLoading = false)),
      takeUntil(this.destroy$)
    );
  }

  private resetPage(): void {
    this.page = 0;
  }

  private setSeoMetaData(): void {
    const seoMetaData: IMetaData & { index: string } = {
      ...defaultMetaInfo(this.portalConfig),
      ogTitle: `${this.portalConfig?.portalName} - Keresés`,
      index: 'noindex, nofollow',
    };

    this.seo.setMetaData(seoMetaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  private bindMethods(): void {
    this.fetchResults = this.fetchResults.bind(this);
    this.resetPage = this.resetPage.bind(this);
    this.search = this.search.bind(this);
  }

  private initCategoryInputClickListener(): void {
    this.renderer2.listen('window', 'click', (event: MouseEvent) => {
      const path = (event as unknown as { path: EventTarget[] }).path || (event.composedPath && event.composedPath());

      if (this.isCategoryListOpen) {
        const isClickCategoryInput = path.find((element) => element === this.categoryInput.nativeElement);
        if (!isClickCategoryInput) {
          this.isCategoryListOpen = false;
        }
      }
    });
  }
}
