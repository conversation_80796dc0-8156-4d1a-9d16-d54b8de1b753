<app-search-page-base
  [title]="title"
  [results]="results"
  [isLoading]="isLoading"
  [totalResultCount]="totalResultCount"
  [page]="page"
  (pageChange)="onPageChange($event)"
>
  <span class="title" slot="title">{{ title }}</span>

  <form class="search-inputs" [formGroup]="searchForm" slot="search-inputs">
    <div class="text-and-author-inputs">
      <div class="text-input" [ngClass]="{ active: isTextInputSelected }">
        <i class="main-icon" (click)="search()"></i>

        <input
          type="text"
          class="input"
          enterkeyhint="go"
          placeholder="Keresés a cikkek között"
          formControlName="text"
          (focus)="setInputFocus('text', true)"
          (blur)="setInputFocus('text', false)"
        />

        <i *ngIf="searchForm.get('text')?.value && isTextInputSelected" class="clear-icon" (mousedown)="clearInput('text')"></i>
      </div>

      <div class="author-input" [ngClass]="{ active: isAuthorInputSelected }">
        <i class="main-icon" (click)="search()"></i>

        <input
          type="text"
          class="input"
          placeholder="Szerző neve"
          formControlName="author"
          (focus)="setInputFocus('author', true)"
          (blur)="setInputFocus('author', false)"
        />

        <i *ngIf="searchForm.get('author')?.value && isAuthorInputSelected" class="clear-icon" (mousedown)="clearInput('author')"></i>
      </div>
    </div>

    <div class="category-and-date-range-inputs">
      <div #categoryInput class="category-input">
        <div class="toggle-category-list-button" (click)="toggleCategoryList()" [ngClass]="{ open: isCategoryListOpen }">
          <p class="category-text">
            {{ searchForm.get('category')?.value?.['title'] || 'Rovat' }}
          </p>
          <i class="arrow-icon"></i>
        </div>

        <div class="category-list" [ngClass]="{ open: isCategoryListOpen }">
          <div class="category-list-label">Rovatok</div>

          <div class="category-list-items">
            <button class="all-categories-button" (click)="onAllCategoriesButtonClick()">Összes <i class="arrow-icon"></i></button>

            <button
              class="category-button"
              *ngFor="let category of categories"
              [ngClass]="{ 'is-selected': category.slug === searchForm.get('category')?.value?.['slug'] }"
              (click)="selectCategory(category)"
            >
              {{ category.title }}
            </button>
          </div>
        </div>
      </div>

      <app-date-range-picker
        (onDateChange)="handleDateChange($event)"
        [id]="'dateRangePicker'"
        (click)="isCategoryListOpen = false"
        [maxDate]="maxDate"
        #dateRangePicker
      ></app-date-range-picker>
    </div>
  </form>

  <ng-container slot="result-count">
    <span class="result-count-keyword">{{ title }}</span
    >{{ title ? ' kulcsszóra' : '' }} {{ totalResultCount }} db találat
  </ng-container>

  <ng-container slot="sidebar">
    <app-sidebar></app-sidebar>
  </ng-container>
</app-search-page-base>
