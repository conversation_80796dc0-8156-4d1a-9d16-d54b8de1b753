import { QueryParams, SearchParams, SearchParamsAndPage } from './search-by-keyword-page.definitions';
import { SearchQuery } from '../article/article.definitions';
import { isNil, pickBy } from 'lodash-es';
import { dateToUtcDateString, List, not, utcDateStringToDate } from '../../shared';
import { Column } from '@trendency/kesma-ui';

export const mapSearchParamsAndPageToQueryParams = ({ searchParams: { text, author, category, fromDate, toDate }, page }: SearchParamsAndPage): QueryParams =>
  pickBy(
    {
      text: text || null,
      author: author || null,
      category: category?.slug ?? null,
      fromDate: fromDate ? dateToUtcDateString(fromDate) : null,
      toDate: toDate ? dateToUtcDateString(toDate) : null,
      page: String(page),
    },
    not(isNil)
  );

export const mapQueryParamsToSearchParamsAndPage = (queryParams: QueryParams, categories: List<Column>): SearchParamsAndPage => ({
  searchParams: {
    text: queryParams.text || null,
    author: queryParams.author || null,
    category: categories.find((category) => category.slug === queryParams.category) ?? null,
    fromDate: queryParams.fromDate ? utcDateStringToDate(queryParams.fromDate) : null,
    toDate: queryParams.toDate ? utcDateStringToDate(queryParams.toDate) : null,
  },
  page: Number(queryParams.page) || 0,
});

export const mapSearchParamsToArticleServiceSearchQuery = ({ text, author, category, fromDate, toDate }: SearchParams): SearchQuery =>
  pickBy(
    {
      global_filter: text || null,
      author: author || null,
      column: category?.slug ?? null,
      from_date: fromDate ? dateToUtcDateString(fromDate) : null,
      to_date: toDate ? dateToUtcDateString(toDate) : null,
    },
    not(isNil)
  );
