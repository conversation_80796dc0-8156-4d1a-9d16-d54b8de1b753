@use 'shared' as *;
@use '../../shared/components/search-page-base/search-page-base' as *;

.title {
  @include title();
}

.search-inputs {
  width: 800px;
  max-width: 100%;
  flex: 1 0 auto;
  display: flex;
  flex-wrap: wrap;
  margin-top: 15px;

  > * {
    margin-right: 15px;
    margin-bottom: 15px;
  }

  .text-and-author-inputs {
    flex: 1 0 auto;
    display: flex;

    > *:not(:last-child) {
      margin-right: 15px;

      @include media-breakpoint-down(xs) {
        margin-right: 0;
        margin-bottom: 15px;
      }
    }

    @include media-breakpoint-down(xs) {
      flex-wrap: wrap;
      width: 100%;
    }
  }

  .category-and-date-range-inputs {
    flex: 1 0 auto;
    position: relative;
    display: flex;

    > *:not(:last-child) {
      margin-right: 15px;

      @include media-breakpoint-down(xs) {
        margin-bottom: 15px;
      }
    }

    @include media-breakpoint-down(xs) {
      flex-wrap: wrap;
      width: 100%;
    }
  }

  %free-text-input {
    flex: 1 1 auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    overflow: hidden;
    padding: 12px 0;
    border-radius: 6px;
    color: $black-2;
    box-sizing: border-box;
    transition: all ease 0.1s;
    border: 1px solid $grey-16;

    .input {
      flex: 1 1 auto;
      width: 100%;
      padding: 0;
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;

      &::placeholder {
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        color: $black-2;
      }
    }

    .main-icon {
      display: block;
      flex: 0 0 auto;
      margin: 0 10px;
      width: 24px;
      height: 24px;
      cursor: pointer;
    }

    .clear-icon {
      display: block;
      flex: 0 0 auto;
      @include icon('icons/x.svg');
      width: 12px;
      height: 12px;
      margin: 0 10px;
      cursor: pointer;
    }
  }

  .text-input {
    @extend %free-text-input;
    width: 250px;

    .main-icon {
      @include icon('icons/search.svg');
    }

    @include media-breakpoint-down(xs) {
      width: 100%;
    }
  }

  .author-input {
    @extend %free-text-input;
    width: 200px;

    .main-icon {
      @include icon('icons/author.svg');
    }

    @include media-breakpoint-down(xs) {
      width: 100%;
    }
  }

  .category-input {
    flex: 0 0 auto;

    @include media-breakpoint-down(xs) {
      width: 100%;
    }

    .toggle-category-list-button {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid $grey-16;
      border-radius: 6px;
      padding: 14px;
      color: $black-2;
      cursor: pointer;
      margin: auto;
      width: 120px;
      height: 100%;
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;

      @include media-breakpoint-down(md) {
        width: 100%;
      }

      .category-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .arrow-icon {
        display: inline-block;
        @include icon('icons/menu-arrow.svg');
        width: 12px;
        height: 8px;
        margin-left: 10px;
      }

      &.open {
        .arrow-icon {
          transform: rotate(180deg);
        }
      }
    }

    .category-list {
      position: absolute;
      width: 500px;
      height: 436px;
      padding: 40px 30px 15px;
      background: $white;
      border: 1px solid $grey-19;
      box-shadow: 0px 20px 50px rgba(0, 0, 0, 0.25);
      border-radius: 6px;
      z-index: 10;
      display: none;
      top: calc(100% + 10px);
      right: 0;

      @include media-breakpoint-down(md) {
        width: 100%;
      }

      &:before {
        content: '';
        position: absolute;
        top: -7px;
        height: 10px;
        width: 10px;
        transform: rotate(-45deg);
        background: $white;
        border-top: 1px solid $grey-19;
        border-right: 1px solid $grey-19;
        right: 245px;

        @include media-breakpoint-down(xs) {
          display: none;
        }
      }

      @include media-breakpoint-down(sm) {
        top: 61px;
      }

      &.open {
        display: block;
      }

      .category-list-label {
        font-size: 14px;
        line-height: 16px;
        color: $grey-19;
        margin-bottom: 8px;
      }

      .category-list-items {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        height: 360px;
        overflow-x: auto;
        font-size: 14px;
        line-height: 30px;

        @include media-breakpoint-down(xs) {
          grid-template-columns: 1fr;
        }

        &::-webkit-scrollbar {
          width: 10px;
        }

        /* Track */
        &::-webkit-scrollbar-track {
          background: $grey-22;
          border-radius: 5px;
        }

        /* Handle */
        &::-webkit-scrollbar-thumb {
          background: $grey-18;
          border-radius: 5px;
        }

        /* Handle on hover */
        &::-webkit-scrollbar-thumb:hover {
          background: $grey-16;
        }

        %category-button {
          text-align: left;
          font-size: 16px;
          line-height: 30px;
        }

        .all-categories-button {
          @extend %category-button;
          font-weight: 600;

          .arrow-icon {
            @include icon('icons/arrow-right.svg');
            margin-left: 10px;
            width: 14px;
            height: 14px;
            margin-bottom: -2px;
          }
        }

        .category-button {
          @extend %category-button;
          color: #5a5a5a;

          &.is-selected {
            font-weight: 600;
          }
        }
      }
    }
  }

  app-date-range-picker {
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }
}

.result-count-keyword {
  @include result-count-keyword();
}
