import { Routes } from '@angular/router';
import { SearchByKeywordPageComponent } from './search-by-keyword-page.component';
import { SearchByKeywordPageResolver } from './search-by-keyword-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const SEARCH_BY_KEYWORD_ROUTES: Routes = [
  {
    path: '',
    component: SearchByKeywordPageComponent,
    resolve: {
      data: SearchByKeywordPageResolver,
    },
    providers: [SearchByKeywordPageResolver],
    canActivate: [PageValidatorGuard],
  },
];
