import { inject, Injectable } from '@angular/core';
import { ArticleService } from '../article/article.service';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ResolvedData } from './search-by-keyword-page.definitions';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { RedirectService } from '@trendency/kesma-ui';
import { mapSearchResultToArticleCard } from 'src/app/shared';
import { mapSearchParamsToArticleServiceSearchQuery } from './search-by-keyword-page.utils';

@Injectable()
export class SearchByKeywordPageResolver {
  constructor(private readonly articleService: ArticleService) {}
  private readonly redirectService = inject(RedirectService);
  private readonly router = inject(Router);

  resolve(route: ActivatedRouteSnapshot): Observable<ResolvedData> {
    const MAX_RESULTS_PER_PAGE = 10;
    const { text = null, author = null, category = null, fromDate = null, toDate = null, page } = route.queryParams;

    const param = {
      text,
      author,
      category,
      fromDate,
      toDate,
    };

    const currentPage = page ? parseInt(page, 10) - 1 : 0;
    const searchQuery = mapSearchParamsToArticleServiceSearchQuery(param);
    const categories$ = this.articleService.getAllColumns().pipe(map(({ data }) => ({ categories: data })));
    const articles$ = this.articleService.searchArticleByKeyword(searchQuery, currentPage, MAX_RESULTS_PER_PAGE).pipe(
      map(({ data, meta }) => {
        if (this.redirectService.shouldBeRedirect(currentPage, data)) {
          this.redirectService.redirectOldUrl('kereses', false, 302);
        }
        return {
          items: data.map(mapSearchResultToArticleCard),
          totalCount: meta.limitable.rowAllCount ?? 0,
        };
      }),
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then(null);
        return throwError(err);
      })
    );

    return forkJoin([categories$, articles$]).pipe(
      map(([categories, articles]) => ({
        ...categories,
        articles,
      }))
    );
  }
}
