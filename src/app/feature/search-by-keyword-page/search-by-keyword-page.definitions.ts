import { List, ListResult } from '../../shared';
import { ArticleCard, Column } from '@trendency/kesma-ui';

export type ResolvedArticle = Readonly<{
  items: List<ArticleCard>;
  totalCount: number;
}>;

export type ResolvedData = Readonly<{
  categories: List<Column>;
  articles: ResolvedArticle;
}>;

export type SearchParams = Readonly<{
  text: string | null;
  author: string | null;
  category: Column | null;
  fromDate: Date | null;
  toDate: Date | null;
}>;

export type QueryParams = Readonly<{
  text?: string;
  author?: string;
  category?: string;
  fromDate?: string;
  toDate?: string;
  page?: string;
}>;

export type InputFocusKey = 'text' | 'author';

export type InputClearKey = 'text' | 'author' | 'category' | 'dateRange';

export type SearchResult = ListResult<ArticleCard>;

export type SearchParamsAndPage = Readonly<{
  searchParams: SearchParams;
  page: number;
}>;
