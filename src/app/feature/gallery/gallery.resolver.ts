import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService } from '../article/article.service';
import { GalleryApiService } from '../../shared';
import { GalleryPageData } from './gallery.definitions';

const MAX_RELATED_ARTICLE_TOP = 2;
const MAX_RELATED_ARTICLE_LEFT = 3;
const MAX_RELATED_ARTICLE_REST = 6;

@Injectable()
export class GalleryResolver {
  constructor(
    private readonly galleryApiService: GalleryApiService,
    private readonly router: Router,
    private readonly articleService: ArticleService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<GalleryPageData> {
    const slug: string = route.params['slug'];

    return forkJoin({
      details: this.galleryApiService.getGalleryDetails(slug),
      recommended: this.galleryApiService.getGalleryRecommendations(slug),
      relatedArticles: this.articleService.getLatestArticles(MAX_RELATED_ARTICLE_TOP + MAX_RELATED_ARTICLE_LEFT + MAX_RELATED_ARTICLE_REST),
    }).pipe(
      map(({ details, recommended, relatedArticles: { data: articles } }) => ({
        galleryDetails: details,
        recommended,
        relatedArticlesSide: (articles ?? []).slice(0, 3),
        relatedArticlesTop: (articles ?? []).slice(3, 5),
        relatedArticlesRest: (articles ?? []).slice(5, 11),
      })),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
