import { ApiResult, ArticleCard, GalleryDetails, GalleryRecommendationData } from '@trendency/kesma-ui';

export type GalleryPageData = Readonly<{
  galleryDetails: GalleryDetails;
  recommended: ApiResult<GalleryRecommendationData[]>;
  relatedArticlesTop: ArticleCard[];
  relatedArticlesRest: ArticleCard[];
  relatedArticlesSide: ArticleCard[];
}>;

export type GalleryScreenPageData = Readonly<{
  galleryDetails: GalleryDetails;
  recommended: ApiResult<GalleryRecommendationData[]>;
}>;

export type GalleryPageDataObject = Readonly<{
  pageData: { galleryDetails: GalleryDetails; recommended: GalleryRecommendationData[] };
}>;
