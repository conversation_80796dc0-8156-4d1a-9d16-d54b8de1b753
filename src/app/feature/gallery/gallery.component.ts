import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ImageLazyLoadDirective, IMetaData, PublishDatePipe, SeoService } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';
import { DEFAULT_PUBLISH_DATE_FORMAT, defaultMetaInfo, PortalConfigService, SocialRowComponent } from '../../shared';
import { GalleryData } from '@trendency/kesma-ui';

@Component({
  selector: 'app-gallery',
  templateUrl: './gallery.component.html',
  styleUrls: ['./gallery.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [RouterLink, NgIf, SocialRowComponent, PublishDatePipe, ImageLazyLoadDirective],
})
export class GalleryComponent implements OnInit, OnDestroy {
  galleryDetails: GalleryData;
  portalName: string;
  portalSubtitle: string;
  private readonly unsubscribe$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly portalConfig: PortalConfigService,
    private readonly seo: SeoService,
    private readonly router: Router,
    private readonly changeRef: ChangeDetectorRef
  ) {
    this.portalName = this.portalConfig.portalName;
    this.portalSubtitle = this.portalConfig.portalSubtitle;
    this.router.routeReuseStrategy.shouldReuseRoute = function (): boolean {
      return false;
    };
  }

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe(({ pageData: { galleryDetails } }) => {
      this.galleryDetails = galleryDetails;
      this.setMetaData();
      this.changeRef.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    if (!this.galleryDetails) {
      return;
    }

    const title = `${this.portalConfig?.portalName} - ${this.galleryDetails?.title}`;

    const metaData: IMetaData = {
      ...defaultMetaInfo(this.portalConfig),
      title,
      description: this.galleryDetails?.description,
      ogTitle: title,
      ogImage: this.galleryDetails?.highlightedImageUrl,
    };
    this.seo.setMetaData(metaData);
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
