<section class="gallery">
  <div class="gallery-column left-column" routerLink="./1">
    <img trImageLazyLoad *ngIf="galleryDetails?.images[0]?.url?.thumbnail" [src]="galleryDetails?.images[0]?.url?.thumbnail" alt="" class="gallery-image" />

    <div class="shadow-effect"></div>

    <div class="gallery-info">
      <app-social-row></app-social-row>
      <div class="gallery-date">{{ galleryDetails?.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</div>
      <h1>{{ galleryDetails?.title }}</h1>
      <!-- <div class="gallery-credit">Fotók: {{ data.credit }}</div> -->
      <div class="gallery-count">{{ galleryDetails?.images?.length }} fotó</div>
      <button class="btn gallery-button">Megnézem a galériát</button>
    </div>
  </div>

  <aside class="sidebar">
    <header>
      <div class="logo-wrappper">
        <a class="logo" [routerLink]="['/']">
          <div class="logo-figure"></div>
          <div class="text">
            <h1 class="portal-name">{{ portalName }}</h1>
            <p clasS="portal-subtitle">{{ portalSubtitle }}</p>
          </div>
        </a>
      </div>
    </header>
  </aside>
</section>
