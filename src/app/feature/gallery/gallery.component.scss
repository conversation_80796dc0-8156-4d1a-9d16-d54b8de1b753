@use 'shared' as *;

.gallery {
  display: flex;
  @include media-breakpoint-down(md) {
    flex-direction: column;
    align-items: center;
  }

  .gallery-column {
    height: 100vh;
    cursor: pointer;
    background: $black;
    position: relative;
    @include media-breakpoint-down(sm) {
      height: 65vh;
    }

    .gallery-image {
      width: 100%;
      height: 100vh;
      margin: 0 auto;
      display: flex;
      object-fit: contain;
      @include media-breakpoint-down(sm) {
        height: 65vh;
      }
    }

    .shadow-effect {
      position: absolute;
      bottom: 0;
      right: 0;
      top: 0;
      left: 0;
      margin: 0;
      background-color: $black;
      opacity: 0.549;
    }

    .gallery-info {
      top: 35%;
      width: 80%;
      left: 10%;
      color: white;
      position: absolute;
      bottom: 0;
      right: 0;
      color: #fff;
      margin: 0;
      @include media-breakpoint-down(sm) {
        top: 25%;
      }

      h1 {
        font-weight: 500;
        font-size: 60px;
        line-height: 70px;
        margin-bottom: 20px;
        @include media-breakpoint-down(sm) {
          font-size: 32px;
          line-height: 52px;
          margin-bottom: 6px;
        }
      }

      .gallery-count,
      .gallery-date {
        font-size: 14px;
        line-height: 21px;
      }

      .gallery-date {
        margin-top: 5px;
        margin-bottom: 20px;
        @include media-breakpoint-down(sm) {
          margin-bottom: 0px;
        }
      }

      .gallery-count {
        margin-left: auto;
        margin-right: 14px;
        margin-bottom: 20px;
      }

      .gallery-button {
        font-size: 16px;
        line-height: 26px;
        font-weight: 400;
        background: #3680b4;
        padding: 8px 50px;
        text-transform: uppercase;
        color: $white;
        @include media-breakpoint-down(sm) {
          font-size: 14px;
          padding: 5px 25px;
        }
      }
    }
  }

  .sidebar {
    width: 480px;
    @include media-breakpoint-down(md) {
      display: none;
    }

    .logo-wrappper {
      border-bottom: 1px solid #e0e0e0;
      width: 100%;
      padding: 10px;
      padding-left: 30px;

      .logo {
        width: calc(#{$logo-width} + 75px);
        height: 55px;
        display: flex;
        align-items: center;
        margin-right: 70px;
        @include media-breakpoint-down(md) {
          width: calc(#{$logo-width} + 10px);
          margin-right: 0;
        }
        @media screen and (max-width: $mobile-old) {
          width: calc(#{$logo-width} - 25px);
        }

        .logo-figure {
          width: 60px;
          position: relative;
          height: 40px;
          margin-right: 16px;
          @include media-breakpoint-down(md) {
            height: 33px;
            width: 45px;
          }

          &:after,
          &:before {
            width: 40px;
            height: 40px;
            display: block;
            content: ' ';
            position: absolute;
            top: 0;
            border-radius: 50%;
            @include media-breakpoint-down(md) {
              width: 30px;
              height: 30px;
            }
          }

          &:after {
            background: $secondary-color;
            right: 0;
          }

          &:before {
            background: $primary-color;
            left: 0;
          }
        }

        .text {
          font-family: $font-secondary;
          text-transform: uppercase;

          .portal-name {
            font-weight: 500;
            font-size: 30px;
            @include media-breakpoint-down(md) {
              font-size: 22.5px;
              margin-top: -2px;
            }
          }

          .portal-subtitle {
            font-weight: 300;
            font-size: 12px;
            max-width: $logo-width;
            @include media-breakpoint-down(md) {
              font-size: 9px;
            }
          }
        }

        &:link,
        &:visited,
        &:active {
          outline: none;

          .text {
            .portal-name,
            .portal-subtitle {
              color: $black;
            }
          }
        }
      }
    }
  }
}
