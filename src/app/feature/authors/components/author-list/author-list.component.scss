@use 'shared' as *;

.author {
  &-list {
    > .wrapper {
      margin: 40px auto;
    }

    h1 {
      font-size: 30px;
      line-height: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      padding: 15px 0 15px 20px;
      margin-top: -5px;
      text-transform: uppercase;
      font-weight: 500;
      margin-bottom: 30px;
      @include article-before-line();

      &:before {
        background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
      }
    }

    app-pager {
      display: block;
      margin: 30px 0;
    }
  }

  &-item {
    border-bottom: 1px solid $grey-16;
    padding: 32px 0;
    display: flex;
    align-items: center;
    gap: 20px;

    @include media-breakpoint-down(sm) {
      padding: 20px 0;
    }
  }

  &-avatar {
    img {
      border-radius: 50%;
      min-width: 100px;
      width: 100px;
      height: 100px;
      object-fit: cover;

      @include media-breakpoint-down(md) {
        min-width: 80px;
        width: 80px;
        height: 80px;
      }
    }
  }

  &-name {
    font-weight: 500;
    font-size: 24px;
    line-height: 30px;
    cursor: pointer;
  }

  &-rank {
    color: $grey-7;
    line-height: 20px;
    font-size: 18px;
    margin-top: 4px;
  }

  &-more-button {
    margin-left: auto;
    align-self: flex-end;
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 20px;
    margin-right: 4px;
    color: $black;
    text-decoration: underline;
    gap: 5px;

    .icon {
      @include icon('icons/arrow-right.svg');
      min-width: 12px;
      width: 12px;
      height: 12px;
    }

    @include media-breakpoint-down(md) {
      align-self: center;

      span {
        display: none;
      }

      .icon {
        min-width: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}
