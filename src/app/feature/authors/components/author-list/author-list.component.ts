import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { map, takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  BackendAuthorData,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
} from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { defaultMetaInfo, PagerComponent, PortalConfigService } from '../../../../shared';


@Component({
  selector: 'app-author-list',
  templateUrl: './author-list.component.html',
  styleUrls: ['./author-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, RouterLink, NgIf, AdvertisementAdoceanComponent, PagerComponent, AsyncPipe],
})
export class AuthorListComponent implements OnInit, OnDestroy {
  adverts: AdvertisementsByMedium;
  authors$: Observable<BackendAuthorData[]> = this.route.data.pipe(map((res) => res['data']?.['data']));
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data']?.['meta'].limitable));

  placeholder = `/assets/images/placeholders/${this.portalConfig.portalName.toLowerCase()}_placeholder.jpg`;

  private readonly unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef,
    private readonly portalConfig: PortalConfigService,
    private readonly router: Router,

  ) {}

  ngOnInit(): void {
    this.initAds();
    this.setMetaData();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  trackBySlug(_: number, item: BackendAuthorData): string {
    return item.slug ?? '';
  }

  setMetaData(): void {
    const description = `Szerzők - ${this.portalConfig.portalSubtitle}, ahol a helyi lakosság számára fontos témákkal foglalkozunk.`;
    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      title: `${this.portalConfig.portalName} - Szerzők`,
      description,
      ogDescription: description,
    });

    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  onSelectPage(page: number): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        ...this.route.snapshot.queryParams,
        page,
      },
    });
  }

  private initAds(): void {
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.markForCheck();
    });
  }
}
