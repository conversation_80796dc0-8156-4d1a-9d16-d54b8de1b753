import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  ArticlesByDate,
  BackendAuthorData,
  createCanonicalUrlForPageablePage,
  getStructuredDataForProfilePage,
  LimitableMeta,
  PortalConfigSetting,
} from '@trendency/kesma-ui';
import { backendDateToDate, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { FormsModule } from '@angular/forms';
import { NgClass, NgFor, NgIf } from '@angular/common';
import { ArticleCardComponent, BackendPortalConfigService, defaultMetaInfo, PagerComponent, PortalConfigService } from '../../../../shared';
import { environment } from '../../../../../environments/environment';


@Component({
  selector: 'app-author-page',
  templateUrl: './author-page.component.html',
  styleUrls: ['./author-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, FormsModule, NgFor, ArticleCardComponent, AdvertisementAdoceanComponent, PagerComponent],
})
export class AuthorPageComponent implements OnInit {
  author: BackendAuthorData;
  articlesByDate: ArticlesByDate[] = [];
  limitable?: LimitableMeta;

  PortalConfigSetting = PortalConfigSetting;

  adverts?: AdvertisementsByMedium;

  filters: Record<string, string | undefined> = {
    global_filter: undefined,
    'publishDate_order[]': 'desc',
  };

  placeholder = `/assets/images/placeholders/${this.portalConfig.portalName.toLowerCase()}_placeholder.jpg`;

  private readonly unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef,
    private readonly portalConfig: PortalConfigService,
    public readonly backendPortalConfigService: BackendPortalConfigService,
    private readonly schemaService: SchemaOrgService,

  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        map(({ data }) => data),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(({ author, articles, limitable }) => {
        const params = this.route.snapshot.queryParams;
        this.filters = {
          global_filter: params['global_filter'] || undefined,
          'publishDate_order[]': params['publishDate_order[]'] === 'asc' ? 'asc' : 'desc',
        };

        this.author = author as BackendAuthorData;
        this.articlesByDate = this.groupDataByDate(articles);
        this.limitable = limitable;

        this.schemaService.removeStructuredData();
        this.schemaService.insertSchema(getStructuredDataForProfilePage(this.author as any, environment?.siteUrl ?? ''));

        this.setMetaData();
        this.initAds();
        this.cdr.markForCheck();
      });
  }

  onSearch(): void {
    const filters = this.filters;

    // Remove empty props
    Object.entries(filters).forEach(([key, value]) => {
      if (!value) {
        delete filters[key];
      }
    });

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { ...filters },
    });
  }

  setMetaData(): void {
    // eslint-disable-next-line max-len
    const description = `${this.author.publicAuthorName} szerző cikkei - ${this.portalConfig.portalSubtitle}, ahol a helyi lakosság számára fontos témákkal foglalkozunk.`;
    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      title: `${this.portalConfig.portalName} - ${this.author.publicAuthorName}`,
      description,
      ogDescription: description,
    });

    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
  }

  onSelectPage(page: number): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        ...this.route.snapshot.queryParams,
        page,
      },
    });
  }

  private initAds(): void {
    this.adverts = undefined;
    this.cdr.markForCheck();

    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.markForCheck();
    });
  }

  private groupDataByDate(competitions: ArticleCard[]): ArticlesByDate[] {
    return competitions.reduce<ArticlesByDate[]>((acc, item) => {
      const date = format(backendDateToDate(item.publishDate as string) ?? new Date(), 'yyyy. MMMM', { locale: hu });
      let group = acc.find((g) => {
        return g.date === date;
      });
      if (!group) {
        group = { date, articles: [] };
        acc.push(group);
      }
      group?.articles.push(item);
      return acc;
    }, []);
  }
}
