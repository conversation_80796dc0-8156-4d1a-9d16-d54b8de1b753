<section class="author-page">
  <div *ngIf="author" class="wrapper">
    <div [ngClass]="{ center: !author.rank && !author.publicAuthorDescription }" class="author-data">
      <div class="author-avatar">
        <img [alt]="author.public_author_name" [src]="author.avatar?.fullSizeUrl || placeholder" loading="eager" />
      </div>
      <div class="author-info">
        <h1 class="author-name">{{ author.public_author_name }}</h1>
        <div *ngIf="author.rank" class="author-rank">{{ author.rank }}</div>
        <div *ngIf="author.publicAuthorDescription" class="author-description">
          <div class="author-description-title">Szakmai bemutat<PERSON></div>
          <div class="author-description-content">{{ author.publicAuthorDescription }}</div>
        </div>
      </div>
    </div>

    <div class="author-filter">
      <div class="author-filter-search">
        <div class="author-filter-search-input">
          <i class="icon icon-search"></i>
          <input (keydown.enter)="onSearch()" [(ngModel)]="filters['global_filter']" placeholder="Keresés a cikkekben..." type="text" />
        </div>
        <button (click)="onSearch()" class="search-btn primary author-filter-search-button" type="button">Keresés</button>
      </div>
      <div class="author-filter-select">
        <select (change)="onSearch()" [(ngModel)]="filters['publishDate_order[]']">
          <option value="desc">Lefrissebb elöl</option>
          <option value="asc">Legrégebbi elöl</option>
        </select>
      </div>
    </div>

    <div *ngIf="backendPortalConfigService.isConfigSet(PortalConfigSetting.SHOW_NUMBER_OF_ARTICLES_BY_AUTHORS_ENABLED)" class="author-page-count">
      <strong>{{ limitable?.rowAllCount ?? 0 }} db</strong> cikk összesen
    </div>

    <div class="articles">
      <ng-container *ngFor="let dateGroup of articlesByDate">
        <div class="author-page-date-group">{{ dateGroup.date }}</div>
        <ng-container *ngFor="let article of dateGroup.articles; let i = index">
          <app-article-card [article]="article" [styleID]="9"></app-article-card>
          <!-- ADS -->
          <ng-container *ngIf="i === 3">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_1 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 8">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_2 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 13">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_3 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 18">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_4 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_4 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <!-- ADS -->
        </ng-container>
      </ng-container>
    </div>

    <app-pager
      (pageChange)="onSelectPage($event)"
      *ngIf="limitable?.pageMax! > 0"
      [currentPage]="limitable?.pageCurrent"
      [rowAllCount]="limitable?.rowAllCount!"
      [rowOnPageCount]="limitable?.rowOnPageCount!"
    ></app-pager>
  </div>
</section>
