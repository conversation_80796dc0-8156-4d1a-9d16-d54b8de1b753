@use 'shared' as *;

.author {
  &-page {
    > .wrapper {
      margin: 40px auto;
    }

    &-date-group {
      font-weight: 700;
      font-size: 24px;
      line-height: 28px;
      margin: 40px 0 30px;
    }

    app-article-card {
      display: block;
      margin-bottom: 30px;
    }

    app-pager {
      display: block;
      margin: 30px 0;
    }
  }

  &-data {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid $grey-16;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }

    &.center {
      align-items: center;
    }
  }

  &-avatar {
    img {
      border-radius: 50%;
      min-width: 120px;
      width: 120px;
      height: 120px;
      object-fit: cover;
    }
  }

  &-name {
    font-size: 30px;
    font-weight: 700;
    line-height: 36px;
  }

  &-rank {
    line-height: 20px;
    font-size: 18px;
    color: $grey-7;
    margin-top: 4px;
  }

  &-description {
    border-top: 1px solid $grey-16;
    color: $black;
    margin-top: 20px;
    padding-top: 20px;
    font-size: 18px;
    line-height: 27px;

    &-title {
      font-weight: 700;
    }

    &-content {
      padding: 10px 0;
    }
  }

  &-filter {
    display: flex;
    gap: 15px;
    margin: 30px 0;
    align-items: center;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-search {
      flex: 1;
      display: flex;
      width: 100%;
      gap: 15px;

      &-input {
        flex: 1;
        position: relative;

        .icon {
          width: 24px;
          height: 24px;
          @include icon('icons/search.svg');
          position: absolute;
          top: calc(50% - 12px);
          left: 15px;
        }

        input {
          width: 100%;
          border: 1px solid $grey-16;
          border-radius: 6px;
          height: 50px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 15px 0 54px;
          font-weight: 500;
          font-size: 14px;
          line-height: 14px;
          color: $grey-7;
        }
      }

      &-button {
        background: $grey-22;
        border: 1px solid $grey-16;
        border-radius: 6px;
        height: 50px;
        color: $black;
        padding: 0 15px;

        &:hover {
          background-color: $grey-17;
        }
      }
    }

    &-select {
      select {
        border: none;
        font-weight: 700;
        font-size: 16px;
        line-height: 18px;
        background: $white;
        color: $black;
        appearance: auto;
      }
    }
  }
}
