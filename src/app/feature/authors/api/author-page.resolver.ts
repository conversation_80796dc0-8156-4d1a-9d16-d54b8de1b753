import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, share, switchMap, take, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { SeoService } from '@trendency/kesma-core';
import { ArticleCard, LimitableMeta, PortalConfigSetting, RedirectService } from '@trendency/kesma-ui';
import { ApiService, BackendPortalConfigService, mapSearchResultToArticleCard } from '../../../shared';
import { AuthorData } from './author.definitions';
import { mapSocialAuthorToAuthor } from '../utils/author-mapper';
import { ArticleSearchResult } from '../../article/article.definitions';

@Injectable({
  providedIn: 'root',
})
export class AuthorPageResolver {
  constructor(
    private readonly router: Router,
    private readonly apiService: ApiService,
    private readonly backendPortalConfigService: BackendPortalConfigService,
    private readonly seoService: SeoService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<{
    articles: ArticleCard[];
    limitable: LimitableMeta;
    author?: AuthorData;
  }> {
    const authorSlug = route.paramMap.get('authorSlug');

    // Redirect plural url to singular
    if (this.seoService.currentUrl.includes('szerzok')) {
      this.redirectService.redirectOldUrl(`szerzo/${authorSlug}`);
    }

    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const publicAuthor$ = this.apiService.getPublicAuthorSocial(authorSlug ?? '').pipe(
      share(),
      take(1),
      map(({ data }) => mapSocialAuthorToAuthor(data))
    );

    const articlesObservable$ = publicAuthor$.pipe(
      take(1),
      switchMap((author) => {
        if (!author?.id) {
          this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
          throwError(() => 'Nincs ilyen szerző');
        }
        const authorFilter = this.backendPortalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_M2M)
          ? { 'author[]': author.id }
          : { author: author.publicAuthorName };
        return this.apiService
          .searchArticles(currentPage, 10, {
            global_filter: route.queryParams['global_filter'] || '',
            'publishDate_order[]': route.queryParams['publishDate_order[]'] === 'asc' ? 'asc' : 'desc',
            ...authorFilter,
          })
          .pipe(
            tap(({ data }) => {
              if (this.redirectService.shouldBeRedirect(currentPage, data)) {
                this.redirectService.redirectOldUrl(`szerzo/${authorSlug}`, false, 302);
              }
            })
          );
      })
    );

    return forkJoin({
      articles: articlesObservable$,
      author: publicAuthor$,
    }).pipe(
      map(({ articles, author }) => ({
        articles: articles?.data?.map((sr: ArticleSearchResult) => mapSearchResultToArticleCard(sr)),
        limitable: articles?.meta?.limitable,
        author,
      })),
      catchError((err) => {
        this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
