import { ArticleCard } from '@trendency/kesma-ui';

export interface ArticlesByDate {
  date: string;
  articles: ArticleCard[];
}

export type BackendAuthorData = Readonly<
  AuthorData & {
    public_author_name: string;
    public_author_description?: string;
    avatar?: string;
    slug?: string;
    public_author?: string;
    title?: string;
    opinionAuthor?: boolean;
  }
>;

export type AuthorData = Readonly<{
  readonly facebook: string;
  readonly instagram: string;
  readonly publicAuthorDescription: string;
  readonly publicAuthorName: string;
  readonly tiktok: string;
  readonly avatar: AuthorDataAvatar;
  readonly rank?: string;
  readonly id: string;
}>;

export type AuthorDataAvatar = Readonly<{
  readonly fullSizeUrl: string;
  readonly thumbnailUrl: string;
  readonly variantId: number;
  readonly altText?: string;
}>;
