<section class="program-detail">
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="program-types">
        <span *ngFor="let programType of programDetailData?.programTypes" class="program-type">{{ programType.title }}</span>
      </div>
      <div class="breadcrumbs"></div>
      <h1 class="title">{{ programDetailData?.title }}</h1>
      <app-social-row></app-social-row>
      <div class="google-maps">
        <div class="google-maps-map" *ngIf="programDetailData?.latitude && programDetailData?.longitude">
          <app-google-maps
            [googleMapsOptions]="{
              lat: +programDetailData?.latitude,
              lng: +programDetailData?.longitude,
              zoom: 16,
              disableDefaultUI: true,
            }"
            [hasSearchIcon]="true"
          ></app-google-maps>
        </div>
        <div class="info-box" [ngClass]="{ 'no-map': !programDetailData?.latitude || !programDetailData?.longitude }">
          <div class="box-title">{{ programDetailData?.title }}</div>
          <div class="box-columns">
            <div class="info-block">
              <div class="info-title">Helyszín</div>
              <div class="info-text">{{ programDetailData?.address }}</div>
            </div>
            <div class="info-block">
              <div class="info-title">Időpont</div>
              <div class="info-text" *ngFor="let dateRange of dateRangeFiltered">
                <ng-container *ngIf="dateRange && dateRange.endDate !== dateRange?.startDate; else isOnlyStartDate">
                  <time class="program-information-date">
                    {{ dateRange?.startDate ?? '' | formatDate: 'y-l-d-h-m' }} -
                    {{ dateRange?.endDate ?? '' | programDate: dateRange?.startDate ?? '' }}
                  </time>
                </ng-container>
                <ng-template #isOnlyStartDate>
                  <time class="program-information-date">
                    {{ dateRange?.startDate ?? '' | formatDate: 'y-l-d-h-m' }}
                  </time>
                </ng-template>
              </div>
            </div>
            <div class="info-block" *ngIf="programDetailData?.externalURL">
              <div class="info-title">Weboldal</div>
              <a [href]="programDetailData?.externalURL" target="_blank" *ngIf="programDetailData?.externalURL || true" class="program-information-externalUrl">
                {{ programDetailData?.externalURL }}
              </a>
            </div>
            <div class="info-block" *ngIf="programDetailData?.phoneNumber">
              <div class="info-title">Telefonszám</div>
              <div class="info-text">{{ programDetailData?.phoneNumber }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="program-title">{{ programDetailData?.title }}</div>
      <div class="lead">{{ programDetailData?.lead }}</div>
      <ng-container *ngIf="programDetailData?.body.length > 0">
        <ng-container *ngFor="let body of programDetailData?.body">
          <ng-container *ngFor="let detail of body?.details">
            <div class="article-content" [innerHTML]="detail?.value | safe: 'html'"></div>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>
    <aside>
      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.box_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>

      <div class="sidebar" *ngIf="recommendedArticles.length > 0">
        <app-programs-sidebar [recommendedArticles]="recommendedArticles"></app-programs-sidebar>
      </div>
    </aside>
  </div>
</section>
