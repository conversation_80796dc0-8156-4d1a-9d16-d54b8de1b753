import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard } from '@trendency/kesma-ui';
import { NgClass, NgFor, NgIf } from '@angular/common';
import { GoogleMapsComponent, ProgramDate, ProgramDatePipe, ProgramDetail, ProgramsSidebarComponent, SocialRowComponent } from '../../shared';
import { FormatDatePipe, SafePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-program-detail',
  templateUrl: './program-detail.component.html',
  styleUrls: ['./program-detail.component.scss'],
  imports: [
    NgFor,
    SocialRowComponent,
    NgIf,
    GoogleMapsComponent,
    NgClass,
    AdvertisementAdoceanComponent,
    ProgramsSidebarComponent,
    SafePipe,
    FormatDatePipe,
    ProgramDatePipe,
  ],
})
export class ProgramDetailComponent implements OnInit, OnDestroy {
  programDetailData: ProgramDetail;
  recommendedArticles: ArticleCard[];
  adverts: AdvertisementsByMedium;
  dateRangeFiltered: ProgramDate[];
  private readonly destroy = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.programDetailData = this.route.snapshot.data['pageData'].programDetail;
    this.recommendedArticles = this.route.snapshot.data['pageData'].recommendedArticles;

    this.dateRangeFiltered = this.programDetailData?.dates.filter((date) => Date.parse(date.endDate) > Date.now());

    this.adStoreAdo.advertisemenets$
      .pipe(
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.destroy)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;

        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.destroy.next();
    this.destroy.complete();
  }
}
