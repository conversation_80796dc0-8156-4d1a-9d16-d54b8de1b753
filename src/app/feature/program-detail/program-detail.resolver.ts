import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ProgramsApiService } from '../programs/services/programs-api.service';
import { ProgramDetailApiData } from '../../shared';

@Injectable()
export class ProgramDetailResolver {
  constructor(
    private readonly programsApiService: ProgramsApiService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ProgramDetailApiData> | Observable<any> {
    const programSlug: string = route.params['programSlug'];

    return forkJoin({
      recommendedArticles: this.programsApiService.getProgramRecommendedArticles(),
      programDetail: this.programsApiService.getProgramDetail(programSlug),
    }).pipe(
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
