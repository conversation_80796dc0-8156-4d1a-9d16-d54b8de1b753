@use 'shared' as *;

.program-detail {
  .program-type {
    font-size: 20px;
    line-height: 21px;
    margin-right: 20px;
    text-transform: capitalize;
  }

  .title {
    font-weight: 500;
    font-size: 60px;
    line-height: 70px;
    text-transform: uppercase;
    margin-top: 20px;
    margin-bottom: 45px;
    @include media-breakpoint-down(md) {
      font-size: 30px;
      line-height: 40px;
    }
  }

  .google-maps {
    background-color: $white;
    margin: 20px auto;
    display: flex;
    flex-direction: column;
    padding: 20px;
    position: relative;
    z-index: 1;

    @include media-breakpoint-down(sm) {
      margin-left: -20px;
      margin-right: -20px;
      padding-right: 0;
      padding-left: 0;
      flex-direction: column;
      justify-content: center;
      align-content: center;
    }

    .google-maps-map {
      width: 100%;
      height: 518px;
      border-radius: 8px;
      overflow: hidden;
      position: relative;

      @include media-breakpoint-down(sm) {
        height: 210px;
      }
    }
  }

  .info-box {
    border: 1px solid $grey-16;
    border-radius: 6px;
    padding: 30px;
    max-width: 680px;
    margin: 0 auto;
    background: white;
    margin-top: -60px;
    z-index: 2;
    @include media-breakpoint-down(sm) {
      margin: 0 20px;
      margin-top: -30px;
    }

    &.no-map {
      margin-top: 0;
    }

    .box-title {
      font-weight: 500;
      font-size: 18px;
      line-height: 18px;
      text-transform: uppercase;
    }

    .box-columns {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      @include media-breakpoint-down(sm) {
        display: block;
      }

      .info-block {
        flex-direction: column;
        flex-basis: 100%;
        flex: 50%;
        margin-top: 40px;

        .info-title {
          text-transform: uppercase;
          font-weight: 500;
        }

        .info-title,
        .info-text {
          font-size: 16px;
          line-height: 30px;
        }
      }
    }
  }

  .program-title {
    font-weight: 500;
    font-size: 18px;
    line-height: 18px;
    text-transform: uppercase;
    margin-bottom: 40px;
  }

  .lead {
    margin-bottom: 20px;
  }

  .article-content {
    margin-bottom: 40px;
  }
}
