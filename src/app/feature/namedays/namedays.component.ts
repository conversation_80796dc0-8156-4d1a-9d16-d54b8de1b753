import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';
import { DateTimePickerComponent, defaultMetaInfo, Nameday, NamedaysService, PortalConfigService } from '../../shared';
import { SeoService } from '@trendency/kesma-core';
import { NgFor, NgIf, TitleCasePipe } from '@angular/common';
import { DateFnsModule } from 'ngx-date-fns';
import { format } from 'date-fns';

@Component({
  selector: 'app-namedays',
  templateUrl: './namedays.component.html',
  styleUrls: ['./namedays.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, NgFor, TitleCase<PERSON>ipe, DateFnsModule, DateTimePickerComponent],
})
export class NamedaysComponent implements OnInit, OnDestroy {
  dateValueFormat: string = `yyyy-MM-dd`;
  searchForm: UntypedFormGroup = new UntypedFormGroup({
    lookupName: new UntypedFormControl(''),
    selectedDay: new UntypedFormControl(format(new Date(), this.dateValueFormat)),
  });
  mainNames: Nameday[] = [];
  otherNames: string[] = [];
  isInNameSearchMode = false;
  foundDates: Date[] = [];

  private readonly destroySubject: Subject<boolean> = new Subject<boolean>();
  private namedays: Nameday[] = [];

  constructor(
    private readonly namedayService: NamedaysService,
    private readonly seo: SeoService,
    private readonly portalConfig: PortalConfigService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  get lookupNameControl(): UntypedFormControl {
    return this.searchForm.controls['lookupName'] as UntypedFormControl;
  }

  get lookupName(): string {
    return this.lookupNameControl.value;
  }

  set lookupName(value: string) {
    this.searchForm.controls['lookupName'].setValue(value);
  }

  ngOnInit(): void {
    this.loadTodayData();
    this.onDaySelected();

    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      ogTitle: `${this.portalConfig?.portalName} - Névnapok`,
    });
  }

  ngOnDestroy(): void {
    this.destroySubject.next(true);
    this.destroySubject.complete();
  }

  onDaySelected(): void {
    this.searchForm
      .get('selectedDay')
      ?.valueChanges.pipe(
        switchMap(() => this.namedayService.getNamedaysByDate(this.searchForm.controls['selectedDay'].value)),
        takeUntil(this.destroySubject)
      )
      .subscribe((namedays) => this.setNamedayData(namedays));
  }

  onSearchFormSubmit(): void {
    this.isInNameSearchMode = true;
    if (!this.lookupName) {
      return;
    }
    this.namedayService
      .getNamedaysByName(this.lookupName)
      .pipe(takeUntil(this.destroySubject))
      .subscribe((namedays) => this.setNameLookupData(namedays));
  }

  onNameLookupClearClick(): void {
    this.lookupName = '';
    this.foundDates = [];
    this.isInNameSearchMode = false;
    this.loadTodayData();
  }

  private setNamedayData(namedays: Nameday[]): void {
    this.namedays = namedays ?? [];
    this.mainNames = this.namedays.filter(({ isMain }) => isMain);
    this.otherNames = this.namedays.filter(({ isMain }) => !isMain).map(({ name }) => name);
    this.isInNameSearchMode = false;
    this.changeRef.detectChanges();
  }

  private setNameLookupData(namedays: Nameday[]): void {
    this.namedays = namedays ?? [];
    this.mainNames = this.namedays.slice(0, 1);
    this.otherNames = [];
    this.foundDates = this.namedays.map(({ date }) => date);
    this.isInNameSearchMode = true;
    this.changeRef.detectChanges();
  }

  private loadTodayData(): void {
    this.namedayService.getTodayNamedays().pipe(takeUntil(this.destroySubject)).subscribe(this.setNamedayData.bind(this));
  }
}
