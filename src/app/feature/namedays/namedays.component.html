@if (searchForm) {
  <section class="namedays">
    <div class="wrapper mobile-header-wrapper mobile-only">
      <div class="page-title">
        <span class="main-title">Névnapok</span>
      </div>
      <div class="desc" *ngIf="!isInNameSearchMode">{{ searchForm.controls['selectedDay'].value | dfnsParseIso | dfnsFormat: 'MMMM d.' | titlecase }}</div>
      <form class="search-input" (ngSubmit)="onSearchFormSubmit()">
        <button type="submit" class="search-button"></button>
        <input type="text" placeholder="Keresés név szerint" [formControl]="lookupNameControl" />
        <button class="clear-button"></button>
      </form>
      <div class="mobile-calendar">
        <app-date-time-picker
          [id]="'namedayCalendarMobile'"
          controlName="selectedDay"
          [formGroup]="searchForm"
          [enableTime]="false"
          [valueFormat]="dateValueFormat"
          [inline]="true"
        ></app-date-time-picker>
      </div>
    </div>

    <div class="wrapper standard-wrapper">
      <div class="nameday-content">
        <div class="page-title desktop-only">
          <div class="left">
            <span class="main-title">Névnapok</span>
            <ng-container *ngIf="!isInNameSearchMode">
              {{ searchForm.controls['selectedDay'].value | dfnsParseIso | dfnsFormat: 'MMMM d.' | titlecase }}
            </ng-container>
          </div>
          <div class="right">
            <form class="search-input" (submit)="onSearchFormSubmit()">
              <button type="submit" class="search-button"></button>
              <input type="text" placeholder="Keresés név szerint" [formControl]="lookupNameControl" />
              <button type="button" class="clear-button" (click)="onNameLookupClearClick()"></button>
            </form>
          </div>
        </div>
        <h5 class="small-title" *ngIf="!isInNameSearchMode">Kiemelt névnapok</h5>
        <div class="highlighted">
          <div class="card" *ngFor="let nameData of mainNames">
            <h3 class="name">{{ nameData.name }}</h3>
            <p class="desc">{{ nameData.meaning }}</p>
          </div>
        </div>
        <h5 class="small-title" *ngIf="!isInNameSearchMode; else nameDatesTitle">További névnapok</h5>
        <ng-template #nameDatesTitle>Névnap dátumok</ng-template>
        <div class="others">
          <p *ngIf="isInNameSearchMode; else otherNamesToday">
            <ng-container *ngFor="let otherDate of foundDates; let isLast = last">
              {{ otherDate | dfnsFormat: 'MMM d.' | titlecase }}
              <ng-container *ngIf="!isLast">,</ng-container>
            </ng-container>
          </p>
          <ng-template #otherNamesToday>
            <ng-container *ngFor="let name of otherNames; let isLast = last">
              {{ name }}
              <ng-container *ngIf="!isLast">,</ng-container>
            </ng-container>
          </ng-template>
        </div>
      </div>
      <div class="calendar desktop-only">
        <app-date-time-picker
          [id]="'namedayCalendarDesktop'"
          controlName="selectedDay"
          [formGroup]="searchForm"
          [enableTime]="false"
          [valueFormat]="dateValueFormat"
          [inline]="true"
        ></app-date-time-picker>
      </div>
    </div>
  </section>
}
