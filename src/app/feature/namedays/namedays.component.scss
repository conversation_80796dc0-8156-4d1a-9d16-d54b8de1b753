@use 'shared' as *;

section.namedays {
  .mobile-calendar {
    display: inline-block;
    margin-top: 20px;
  }

  .page-title {
    font-size: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 5px 20px;
    margin-bottom: 50px;
    margin-top: -5px;
    @include article-before-line();

    &:before {
      background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
    }

    @include media-breakpoint-down(sm) {
      margin-top: 30px;
      margin-bottom: 14px;
    }

    .main-title {
      text-transform: uppercase;
      display: inline-block;
      margin-right: 9px;
      font-weight: 500;
    }
  }

  .search-input {
    @include media-breakpoint-down(sm) {
      margin-top: 40px;
    }
  }

  .mobile-only {
    @include media-breakpoint-up(md) {
      display: none !important;
    }
  }

  .desktop-only {
    @include media-breakpoint-down(sm) {
      display: none !important;
    }
  }

  .wrapper.standard-wrapper {
    display: flex;
    gap: 30px;
    margin-top: 40px;
    padding-bottom: 100px;
    position: relative;

    > .nameday-content {
      flex: 1;

      .small-title {
        font-weight: normal;
        font-size: 16px;
        line-height: 30px;
        margin-bottom: 30px;
      }

      .highlighted {
        .card {
          margin-bottom: 60px;

          .name {
            font-weight: 500;
            font-size: 30px;
            line-height: 40px;
            margin-bottom: 20px;
          }

          .desc {
            font-weight: normal;
            font-size: 20px;
            line-height: 40px;
          }
        }
      }

      .others {
        p {
          font-weight: 500;
          font-size: 24px;
          line-height: 40px;
        }
      }
    }
  }
}
