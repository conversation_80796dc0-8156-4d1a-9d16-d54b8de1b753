import { Routes } from '@angular/router';
import { CategoryComponent } from './category.component';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { CategoryResolver } from './category.resolver';

export const CATEGORY_ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: CategoryComponent,
    canActivate: [PageValidatorGuard],
    resolve: {
      pageData: CategoryResolver,
    },
    providers: [CategoryResolver],
  },
  {
    path: ':month',
    pathMatch: 'full',
    component: CategoryComponent,
    canActivate: [PageValidatorGuard],
    resolve: {
      pageData: CategoryResolver,
    },
    providers: [CategoryResolver],
    data: { categoryRouteType: 'category-month' },
  },
];
