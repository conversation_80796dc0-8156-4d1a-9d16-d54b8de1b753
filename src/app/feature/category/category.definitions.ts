import { ApiResponseMetaList, <PERSON>piR<PERSON>ult, ArticleCard, Layout } from '@trendency/kesma-ui';

export type CategoryRouteType = 'category-layout' | 'category-year' | 'category-month';

export type CategoryResolverResponse = Readonly<{
  layoutApiResponse: Layout;
  excludedIds: string[];
  columnTitle: string;
  category: ApiResult<ArticleCard[], ApiResponseMetaList>;
  slug: string;
  year: string;
  month: string;
  columnParentSlug: string;
}>;

export type LayoutWithExcludeIds = Readonly<{
  data: Layout | null;
  excludedIds: string[];
  columnTitle: string;
  columnParentSlug: string;
}>;
