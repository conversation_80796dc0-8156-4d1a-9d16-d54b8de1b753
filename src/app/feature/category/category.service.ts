import { Injectable } from '@angular/core';
import { Params, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, ArticleCard, Layout, LayoutService, RedirectService } from '@trendency/kesma-ui';
import { ApiService } from '../../shared';
import { CategoryResolverResponse, LayoutWithExcludeIds } from './category.definitions';

@Injectable({ providedIn: 'root' })
export class CategoryService {
  constructor(
    private readonly apiService: ApiService,
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly layoutService: LayoutService,
    private readonly redirectService: RedirectService,
    private readonly router: Router
  ) {}

  getRequestForCategoryLayout(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug } = params;
    const { page } = queryParams;
    const rowOnPageCount = 10;
    const pageIndex = page ? page - 1 : 0;

    return this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      switchMap((layoutResponse) => {
        return this.apiService.getCategoryArticles(categorySlug, pageIndex, rowOnPageCount, undefined, undefined, layoutResponse.excludedIds).pipe(
          tap(({ data }) => {
            if (this.redirectService.shouldBeRedirect(page, data)) {
              this.redirectService.redirectOldUrl(`rovat/${categorySlug}`, false, 302);
            }
          }),
          catchError((error) => {
            this.router.navigate(['/', '404'], {
              state: { errorResponse: JSON.stringify(error) },
              skipLocationChange: true,
            });
            return throwError(() => error);
          }),
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) =>
            this.mapCategoryResponse(categoryResponse, categorySlug, '', '', layoutResponse as LayoutWithExcludeIds)
          )
        );
      })
    );
  }

  getRequestForCategoryByDate(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug, year, month } = params;
    const { page } = queryParams;
    const rowOnPageCount = 5;
    const pageIndex = page ? page - 1 : 0;

    const request$ =
      isNaN(year) || (month && isNaN(month))
        ? this.redirectOldArticleUrls()
        : this.apiService.getCategoryArticles(categorySlug, pageIndex, rowOnPageCount, year, month, []).pipe(
            tap(({ data }) => {
              if (this.redirectService.shouldBeRedirect(pageIndex, data)) {
                this.redirectService.redirectOldUrl(year ? `rovat/${categorySlug}/${year}/${month}` : `rovat/${categorySlug}/${month}`, false, 302);
              }
            })
          );

    return request$.pipe(
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(() => error);
      }),
      map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) => this.mapCategoryResponse(categoryResponse, categorySlug, year, month))
    );
  }

  private mapCategoryResponse(
    categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>,
    slug: string,
    year = '',
    month = '',
    layoutResponse: LayoutWithExcludeIds = { data: null, excludedIds: [], columnTitle: '', columnParentSlug: '' },
    noLayoutData = false
  ): CategoryResolverResponse {
    return {
      layoutApiResponse: (noLayoutData ? null : layoutResponse?.data) as Layout,
      columnTitle: layoutResponse.columnTitle,
      excludedIds: layoutResponse?.excludedIds,
      category: categoryResponse,
      columnParentSlug: layoutResponse.columnParentSlug ?? '',
      slug,
      year,
      month,
    };
  }

  private redirectOldArticleUrls(): Observable<any> {
    const currentUrl = this.seoService.currentUrl;
    return this.apiService.getArticleRedirect(encodeURIComponent(currentUrl)).pipe(
      tap(({ url }) => {
        if (url && this.utilsService.isBrowser()) {
          return this.redirectService.redirectOldUrl(url, true);
        }
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(null);
      })
    );
  }
}
