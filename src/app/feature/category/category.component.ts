import { ChangeDetectorRef, Component, Element<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { distinctUntilChanged, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { DomSanitizer } from '@angular/platform-browser';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  AnalyticsService,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LayoutApiData,
  PAGE_TYPES,
  SecondaryFilterAdvertType,
} from '@trendency/kesma-ui';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { NgClass, NgFor, NgIf } from '@angular/common';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import {
  ApiService,
  ArticleCardComponent,
  defaultMetaInfo,
  generateThirdPartyAdUrls,
  PagerComponent,
  PortalConfigService,
  ThirdPartyAdUrls,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';


@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.scss'],
  imports: [NgIf, NgClass, LayoutComponent, NgFor, AdvertisementAdoceanComponent, ArticleCardComponent, PagerComponent, SidebarComponent],
})
export class CategoryComponent implements OnInit, OnDestroy {
  @ViewChild('articleList') readonly articleList: ElementRef;

  rowAllCount: number;
  rowOnPageCount = 10;
  rowFrom = 0;
  categoryTitle = '';
  year = '';
  month = '';
  layoutApiData: LayoutApiData;
  slug: string;
  excludedIds: string[] = [];
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  currentPageIndex: number;

  simpleArticles: ArticleCard[];
  adverts: AdvertisementsByMedium | undefined;

  thirdPartyAdUrls: ThirdPartyAdUrls = generateThirdPartyAdUrls(this.sanitizer);
  isDesktopAd: boolean = this.utilsService.isBrowser() && window?.innerWidth > 768;
  isMobileAd: boolean = this.utilsService.isBrowser() && window?.innerWidth <= 768;
  portalName: string;

  private readonly destroy$: Subject<boolean> = new Subject<boolean>();

  constructor(
    private readonly apiService: ApiService,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly router: Router,
    private readonly portalConfig: PortalConfigService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly utilsService: UtilService,
    private readonly analyticsService: AnalyticsService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly sanitizer: DomSanitizer
  ) {
    this.portalName = portalConfig.portalName;
  }

  ngOnInit(): void {
    this.initState();

    this.route.queryParams
      .pipe(
        filter((params) => 'page' in params),
        map((params) => params['page']),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((page: string): void => {
        this.currentPageIndex = Math.abs(parseInt(page, 10) - 1);
        this.fetchSimpleArticlesPage(this.currentPageIndex);
        this.changeRef.detectChanges();
      });
  }

  onPageChange(page: number): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page },
    });
  }

  setMetaData(): void {
    if (!this.categoryTitle) {
      return;
    }
    // eslint-disable-next-line max-len
    const description = `${this.categoryTitle} rovat legfrissebb hírei, érdekességek, elgondolkodtató tartalmak - ${this.portalConfig.portalSubtitle}, ahol a helyi lakosság számára fontos témákkal foglalkozunk.`;
    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      title: `${this.portalConfig.portalName} - ${this.categoryTitle}`,
      description,
      ogDescription: description,
    });
    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  resetAds(): void {
    this.adverts = undefined;
    this.changeRef.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
    this.adStoreAdo.setArticleParentCategory('');
  }

  private initState(): void {
    this.route.data
      .pipe(
        tap(() => this.resetAds()),
        map((res) => {
          const { layoutApiResponse, columnTitle, excludedIds, category, slug, year, month, columnParentSlug } = res['pageData'];
          this.excludedIds = excludedIds;
          this.slug = slug;
          this.layoutApiData = layoutApiResponse;
          this.simpleArticles = category?.data;
          this.rowAllCount = category?.meta?.limitable.rowAllCount;
          this.rowOnPageCount = category?.meta?.limitable.rowOnPageCount;
          this.rowFrom = category?.meta?.limitable.rowFrom;
          this.currentPageIndex = category?.meta?.limitable.pageCurrent;
          this.year = year;
          this.month = month;
          this.adPageType = `column_${columnParentSlug || this.slug}`;
          this.categoryTitle = columnTitle;
          this.setMetaData();
          this.analyticsService.sendPageView({
            pageCategory: this.categoryTitle,
          });

          this.adStoreAdo.setArticleParentCategory(this.adPageType);
        }),

        switchMap((): Observable<Advertisement[]> => {
          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => {
          return this.adStoreAdo.separateAdsByMedium(ads, this.adPageType, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE);
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }

  private fetchSimpleArticlesPage(nextPage: number): void {
    this.apiService
      .getCategoryArticles(this.slug, nextPage, this.rowOnPageCount, this.year, this.month, this.excludedIds)
      .subscribe((res: ApiResult<ArticleCard[], ApiResponseMetaList>) => {
        this.simpleArticles = res.data;
        this.changeRef.detectChanges();
        if (this.articleList) {
          // TODO: move to afterviewinit to avoid settimeout
          if (this.utilsService.isBrowser()) {
            setTimeout(() => {
              this.scrollToFirstArticle();
            }, 50);
          }
        }
      });
  }

  private scrollToFirstArticle(): void {
    const offsetMargin = 50;
    window.scrollTo(0, (this.articleList.nativeElement as HTMLElement).offsetTop - offsetMargin);
  }
}
