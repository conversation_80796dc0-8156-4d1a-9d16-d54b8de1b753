<iframe
  *ngIf="isDesktopAd && portalName === 'ERDON'"
  [src]="thirdPartyAdUrls?.desktop?.frameUrl"
  allow="autoplay"
  id="aac22075"
  name="aac22075"
  scrolling="no"
  style="display: block; margin: 10px auto 30px; width: 100%; max-width: 970px; height: 250px; border: 0"
  ><a [href]="thirdPartyAdUrls?.desktop?.linkUrl" target="_blank"><img [src]="thirdPartyAdUrls?.desktop?.imgUrl" alt="" /></a
></iframe>

<iframe
  *ngIf="isMobileAd && portalName === 'ERDON'"
  [src]="thirdPartyAdUrls?.mobile?.frameUrl"
  allow="autoplay"
  id="a4fd0e2b"
  name="a4fd0e2b"
  scrolling="no"
  style="display: block; margin: 10px auto 30px; width: 100%; max-width: 300px; height: 250px; border: 0"
  ><a [href]="thirdPartyAdUrls?.mobile?.linkUrl" target="_blank"><img [src]="thirdPartyAdUrls?.mobile?.imgUrl" alt="" /></a
></iframe>

<section class="category">
  <div class="wrapper with-aside">
    <div [ngClass]="{ 'full-width': layoutApiData }" class="left-column">
      <h1 class="category-title">{{ categoryTitle }}</h1>
      <div *ngIf="layoutApiData" class="col-lg-4 section-header">
        <!-- <div class="section-header-block">
          <div class="section-header-title">Most történt</div>
        </div> -->
      </div>
      <app-layout *ngIf="layoutApiData" [adPageType]="adPageType" [configuration]="layoutApiData.content" [structure]="layoutApiData.struct"> </app-layout>
      <div #articleList class="wrapper wrapper-more-news row">
        <div class="col-12 col-lg-12">
          <div *ngIf="layoutApiData && simpleArticles" class="break-title-block">
            <div class="break-title">További {{ categoryTitle }} hírek <i class="icon icon-right-arrow"></i></div>
          </div>
          <ng-container *ngFor="let article of simpleArticles; let i = index">
            <ng-container *ngIf="i === 0 && !layoutApiData">
              <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_1 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
              <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
            </ng-container>

            <ng-container *ngIf="i === 3 && !layoutApiData">
              <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_2 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
              <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
            </ng-container>
            <ng-container *ngIf="i === 6 && !layoutApiData">
              <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_3 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
              <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
            </ng-container>
            <ng-container *ngIf="i === 9 && !layoutApiData">
              <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_4 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
              <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_4 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
              </kesma-advertisement-adocean>
            </ng-container>

            <app-article-card [article]="article" [styleID]="9"></app-article-card>
          </ng-container>
          <app-pager
            (pageChange)="onPageChange($event)"
            *ngIf="simpleArticles && rowAllCount > rowOnPageCount"
            [currentPage]="currentPageIndex"
            [rowAllCount]="rowAllCount"
            [rowOnPageCount]="rowOnPageCount"
          ></app-pager>

          <kesma-advertisement-adocean *ngIf="!layoutApiData && adverts?.desktop?.roadblock_5 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean *ngIf="!layoutApiData && adverts?.desktop?.roadblock_6 as ad" [ad]="ad" [style]="{ margin: '40px auto' }">
          </kesma-advertisement-adocean>
        </div>
      </div>
    </div>
    <aside>
      <app-sidebar *ngIf="!layoutApiData" [adPageType]="adPageType" [categorySlug]="slug" [excludedIds]="excludedIds"></app-sidebar>
    </aside>
  </div>
</section>
