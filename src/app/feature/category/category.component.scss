@use 'shared' as *;

.category {
  .left-column {
    &.full-width {
      width: 100%;
    }
  }

  h1 {
    font-size: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 15px 0 15px 20px;
    margin-top: -5px;
    text-transform: uppercase;
    font-weight: 500;
    margin-bottom: 30px;
    @include article-before-line();

    &:before {
      background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
    }
  }

  .section-header {
    padding-left: 20px;
    @include media-breakpoint-down(md) {
      display: none;
    }

    .section-header-block {
      min-height: 60px;
      padding-right: 25px;
      display: flex;
      align-items: center;
      position: relative;
      background: $grey-22;
      @include article-before-line();

      &:before {
        background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
      }

      .section-header-title {
        width: 100%;
        @include article-before-title-style;
      }
    }
  }

  .break-title-block {
    min-height: 60px;
    // padding-right: 25px;
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 30px;
    background: $grey-22;
    width: calc(100% + 25px);
    @include article-before-line();

    &:before {
      background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
    }
  }

  .break-title {
    @include article-before-title-style;
  }

  app-pager {
    display: flex;
    justify-content: center;
    margin-bottom: 50px;
    margin-top: 30px;
  }

  .wrapper-more-news {
    margin: auto;

    @include media-breakpoint-down(sm) {
      max-width: 100%;
    }

    .col-lg-8 {
      margin-left: -15px;
      @include media-breakpoint-down(sm) {
        margin: 0;
        padding: 0;
      }
    }
  }

  app-layout {
    margin-top: -30px;
  }
}
