import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { CategoryResolverResponse, CategoryRouteType } from './category.definitions';
import { CategoryService } from './category.service';

@Injectable()
export class CategoryResolver {
  constructor(private readonly categoryService: CategoryService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<CategoryResolverResponse> {
    const categoryRouteType: CategoryRouteType = route.data['categoryRouteType'];
    const params = route.params;
    const queryParams = route.queryParams;

    switch (categoryRouteType) {
      case 'category-layout':
        return this.categoryService.getRequestForCategoryLayout(params, queryParams);
      case 'category-year':
        return this.categoryService.getRequestForCategoryByDate(params, queryParams);
      case 'category-month':
        return this.categoryService.getRequestForCategoryByDate(params, queryParams);
    }
  }
}
