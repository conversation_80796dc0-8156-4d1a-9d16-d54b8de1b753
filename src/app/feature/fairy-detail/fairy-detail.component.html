<section class="fairy-detail">
  <div class="wrapper">
    <div class="page-title-special">
      <button class="button" [routerLink]="['/', 'tunderszepek']"><i class="icon-back"></i></button>
      <span class="title">T<PERSON>nd<PERSON>rszépek</span>
    </div>
    <div class="fairy-detail-top">
      <div class="left">
        <app-fairy-detail-gallery [gallery]="gallery" [personData]="personData"></app-fairy-detail-gallery>
      </div>
      <div class="right">
        <h1 class="name">{{ personData.name }}</h1>
        <div class="info-row">
          <div class="info-row-left">
            <span class="age">{{ personData.age }}</span>
            <span class="location">{{ personData.city }}</span>
          </div>
          <div class="infor-row-right">
            <button class="button button-fairy white" (click)="onToggleVoteLayer()">
              <i class="icon-heart"></i>
              {{ personData.vote_value }} pont
            </button>
            <button class="button button-fairy" (click)="onToggleVoteLayer()">Ért<PERSON>kel<PERSON></button>
            <div class="fairy-vote-layer-wrapper" [ngClass]="{ closed: !isVoteLayerOpened }">
              <div class="fairy-vote-layer-box">
                <app-fairy-vote-layers (openLoginPopup)="loginPopup.open()" (openRegistrationPopup)="registrationPopup.open()"></app-fairy-vote-layers>
                <button class="fairy-vote-close-button" (click)="onCloseVoteLayer()"></button>
              </div>
            </div>
          </div>
        </div>
        <div class="description-content" [innerHTML]="personData.description | safe: 'html'"></div>

        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>

        <div class="bottom-part">
          <app-social-row></app-social-row>
          <button class="button button-fairy" (click)="onToggleVoteLayer()">Értékelem</button>
        </div>
      </div>
    </div>

    <app-section-header sectionTitle="További versenyzők"></app-section-header>
    <div class="fairy-list">
      <div class="col-12 col-md-6 col-lg-3" *ngFor="let participant of displayedOtherParticipants" [@fadeInAnimation]>
        <app-fairy-card [styleID]="fairyCardType.SMALL_CARD_NO_VOTE" [data]="participant"></app-fairy-card>
      </div>
    </div>

    <app-pager
      [currentPage]="currentPage"
      [rowAllCount]="itemCount"
      [rowOnPageCount]="maxPerPage"
      [allowAutoScrollToTop]="false"
      (pageChange)="onPageChange($event)"
    ></app-pager>

    <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [ad]="ad"></kesma-advertisement-adocean>

    <app-galleries-bottom
      [previousCycles]="previousCycles"
      [relatedArticles]="relatedArticles"
      [adverts]="adverts"
      ad="medium_rectangle_3_above_bottom"
    ></app-galleries-bottom>
  </div>
</section>

<app-login-popup #loginPopup></app-login-popup>
<app-registration-popup #registrationPopup></app-registration-popup>
