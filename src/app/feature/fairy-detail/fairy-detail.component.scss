@use 'shared' as *;

.fairy-detail {
  padding-top: 50px;
  padding-bottom: 50px;
  width: 100%;
  @include media-breakpoint-down(md) {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .wrapper {
    .button.button-fairy {
      min-width: 137px;
      @include media-breakpoint-down(sm) {
        width: calc(50% - 10px);
        min-width: 0;
      }

      &.white {
        background-color: $white;
        color: $red;
        margin-right: 20px;
      }

      .icon-heart {
        width: 22px;
        height: 22px;
        @include icon('icons/heart-red.svg');
        margin-right: 10px;
        margin-bottom: -6px;
        display: inline-block;
      }
    }

    .page-title-special {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 30px;

      .button {
        min-width: 0;
        width: 50px;
        height: 50px;
        background: $white;
        margin-right: 30px;
        @include icon('icons/arrow-left.svg');
        background-size: 45%;
      }

      .title {
        font-weight: 500;
        font-size: 30px;
        line-height: 30px;
        text-transform: uppercase;
      }
    }

    .fairy-detail-top {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 30px;

      .left {
        width: calc(50% - 20px);
        @include media-breakpoint-down(md) {
          width: 100%;
          order: 2;
        }
      }

      .right {
        width: calc(50% - 20px);
        @include media-breakpoint-down(md) {
          width: 100%;
          order: 1;
        }

        .name {
          font-weight: 500;
          font-size: 60px;
          line-height: 70px;
          margin-bottom: 5px;
          @include media-breakpoint-down(md) {
            font-size: 30px;
            line-height: 40px;
          }
        }

        .info-row {
          margin: 45px 0;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: space-between;
          @include media-breakpoint-down(sm) {
            margin: 0 0 30px 0;
          }

          .info-row-left {
            font-weight: normal;
            font-size: 20px;
            line-height: 30px;
            @include media-breakpoint-down(sm) {
              margin-bottom: 15px;
            }

            .age {
              display: inline-block;
              margin-right: 30px;
            }

            .location {
              display: inline-block;
            }
          }

          .infor-row-right {
            position: relative;
            @include media-breakpoint-down(sm) {
              width: 100%;
            }

            .fairy-vote-layer-wrapper {
              position: absolute;
              top: 100%;
              right: 0;
              padding-top: 5px;
              z-index: 10;
              transform: scale(1);
              transition: 0.3s ease-out all;
              transform-origin: calc(100% - 60px) top;

              &.closed {
                pointer-events: none;
                transform: scale(0);
                opacity: 0;
              }

              @include media-breakpoint-down(sm) {
                position: fixed;
                bottom: 0;
                top: initial;
                right: 0;
                width: 100%;
                transform-origin: bottom center;
                &.closed {
                  transform: scale(1) translateY(100%);
                }
              }

              .fairy-vote-layer-box {
                background: $white;
                border: 1px solid $grey-19;
                box-shadow: 0px 20px 50px rgba(0, 0, 0, 0.25);
                border-radius: 6px;
                padding: 30px;
                position: relative;
                min-width: 300px;
                @include media-breakpoint-down(sm) {
                  padding: 20px;
                }
                @media screen and (max-width: 350px) {
                  padding: 10px;
                }

                &:after {
                  content: ' ';
                  width: 16px;
                  height: 16px;
                  position: absolute;
                  top: 0;
                  right: 60px;
                  background: $white;
                  border-top: 1px solid $grey-19;
                  border-left: 1px solid $grey-19;
                  display: block;
                  transform: translateY(-50%) rotate(45deg);
                  @include media-breakpoint-down(sm) {
                    display: none;
                  }
                }

                @include media-breakpoint-down(sm) {
                  border-radius: 0;
                  border-bottom-width: 0;
                  border-left-width: 0;
                  border-right-width: 0;
                }

                .fairy-vote-close-button {
                  @include icon('icons/x-small.svg');
                  position: absolute;
                  width: 16px;
                  height: 16px;
                  top: 10px;
                  right: 10px;
                }
              }
            }
          }
        }

        .description-content {
          margin-bottom: 27px;

          p {
            font-weight: 300;
            font-size: 20px;
            line-height: 40px;
            margin-bottom: 12px;
            @include media-breakpoint-down(sm) {
              font-size: 16px;
              line-height: 26px;
            }
          }
        }

        .bottom-part {
          @include media-breakpoint-down(md) {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
          }

          > .button-fairy {
            margin-bottom: 15px;
            width: 150px;
            max-width: calc(100vw - 228px);
            padding-left: 5px;
            padding-right: 5px;
            @include media-breakpoint-up(lg) {
              display: none;
            }
          }
        }

        .social-row {
          justify-content: space-between;

          .social-button.email {
            margin-right: 0;
          }

          .social-button.facebook-like {
            display: none;
          }
        }
      }
    }

    .fairy-list {
      display: flex;
      margin-left: -15px;
      margin-right: -15px;
      width: calc(100% + 30px);
      flex-wrap: wrap;
    }

    app-pager {
      display: flex;
      justify-content: center;
      margin-bottom: $block-bottom-margin;

      @include media-breakpoint-down(md) {
        margin-bottom: $block-bottom-margin-mobile;
      }
    }

    app-galleries-bottom {
      .article-card {
        &.style-6 {
          @include media-breakpoint-down(sm) {
            .article-block {
              margin-left: 0;

              .article-bottom {
                padding-right: 15px;
              }
            }
          }
        }
      }
    }
  }
}
