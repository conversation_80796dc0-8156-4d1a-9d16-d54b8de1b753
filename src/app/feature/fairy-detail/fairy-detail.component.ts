import { animate, style, transition, trigger } from '@angular/animations';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard } from '@trendency/kesma-ui';
import { sortBy } from 'lodash-es';
import { Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { IMetaData, SafePipe, SeoService } from '@trendency/kesma-core';
import { NgClass, NgFor, NgIf } from '@angular/common';
import { FairyDetailGalleryComponent } from './components/fairy-detail-gallery/fairy-detail-gallery.component';
import {
  FairyCardComponent,
  FairyCardTypes,
  FairyCycle,
  FairyGalleryInterface,
  FairyPreviewData,
  FairyVoteLayersComponent,
  GalleriesBottomComponent,
  LoginPopupComponent,
  MainFairyInterface,
  PagerComponent,
  PortalConfigService,
  RegistrationPopupComponent,
  SectionHeaderComponent,
  SocialRowComponent,
} from '../../shared';

const MAX_PARTICIPANT_PER_PAGE = 8;

@Component({
  selector: 'app-fairy-detail',
  templateUrl: './fairy-detail.component.html',
  styleUrls: ['./fairy-detail.component.scss'],
  encapsulation: ViewEncapsulation.None,
  animations: [trigger('fadeInAnimation', [transition(':enter', [style({ opacity: 0 }), animate('.300s ease-out', style({ opacity: 1 }))])])],
  imports: [
    RouterLink,
    FairyDetailGalleryComponent,
    NgClass,
    FairyVoteLayersComponent,
    NgIf,
    AdvertisementAdoceanComponent,
    SocialRowComponent,
    SectionHeaderComponent,
    NgFor,
    FairyCardComponent,
    PagerComponent,
    GalleriesBottomComponent,
    SafePipe,
    LoginPopupComponent,
    RegistrationPopupComponent,
  ],
})
export class FairyDetailComponent implements OnInit, OnDestroy {
  readonly fairyCardType = FairyCardTypes;
  isVoteLayerOpened = false;
  personData: MainFairyInterface;
  gallery: FairyGalleryInterface[] = [];
  relatedArticles: ArticleCard[] = [];
  otherParticipants: FairyPreviewData[] = [];
  displayedOtherParticipants: FairyPreviewData[];
  previousCycles: FairyCycle[];
  currentPage = 0;
  itemCount = 0;
  maxPerPage = MAX_PARTICIPANT_PER_PAGE;
  adverts: AdvertisementsByMedium;
  private readonly unsubscribe$ = new Subject<boolean>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly portalConfig: PortalConfigService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        switchMap((res) => {
          const {
            pageData: { participantsData, relatedArticles, personData },
          } = res;
          this.personData =
            participantsData.main_girl_data?.slug === personData?.main_girl_data?.slug ? participantsData.main_girl_data : personData.main_girl_data;
          this.gallery = this.personData.galeria;
          this.relatedArticles = relatedArticles;
          this.otherParticipants = participantsData.all_girl_data;
          this.previousCycles = participantsData.previous_cycles;
          this.itemCount = this.otherParticipants.length;
          this.setMetaData();
          this.pageChange(0);

          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads, 'gallery')),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  onToggleVoteLayer(): void {
    this.isVoteLayerOpened = !this.isVoteLayerOpened;
  }

  onCloseVoteLayer(): void {
    this.isVoteLayerOpened = false;
  }

  onPageChange(activePage: number): void {
    this.pageChange(activePage - 1);
  }

  private pageChange(currentPage: number): void {
    this.currentPage = currentPage;
    const itemIndex = this.currentPage * this.maxPerPage;
    this.displayedOtherParticipants = this.otherParticipants.slice(itemIndex, itemIndex + this.maxPerPage);
  }

  private setMetaData(): void {
    const { name, description, galeria } = this.personData || {};
    if (!this.personData) {
      return;
    }

    const images: FairyGalleryInterface[] = sortBy(
      (galeria || []).filter((item) => item.facebook_kep || item.kis_ajanlokep),
      ['kis_ajanlokep', 'facebook_kep']
    ).reverse();
    const thumbnailImage = images.length !== 0 ? images[0] : undefined;
    const title = `${this.portalConfig?.portalName} - ${name}`;

    const metaData: IMetaData = {
      title,
      description: description || this.portalConfig.portalSubtitle,
      ogTitle: title,
      ogImage: thumbnailImage?.thumbnail_src,
      ogType: 'article',
      articlePublishedTime: thumbnailImage?.created_time?.toISOString(),
    };

    this.seo.setMetaData(metaData);
  }
}
