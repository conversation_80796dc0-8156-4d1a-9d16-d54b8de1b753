<section class="fairy-detail-gallery" #mainWrapper>
  <div class="big-image-carousel">
    <div
      #carousel
      class="wrapper centered-carousel-obj"
      (click)="onMainSlickClick($event)"
      *ngIf="galleryItems?.length; else galleryPlaceholder"
      [@fadeInOutAnimation]
    >
      <ng-container *ngFor="let item of galleryItems; let i = index">
        <div class="slide">
          <!-- use the main click event from wrapper element for routing (only this solution works with clones) -->
          <div
            trImageLazyLoad
            [ngStyle]="{ 'background-image': item.url.fullSize ? 'url(\'' + item.url.fullSize + '\')' : 'none' }"
            class="card-wrapper"
            [attr.data-index]="i"
          >
            <button class="position-button"></button>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="arrows-container">
      <!-- custom arrows: workaround of infinite step bug (diff animation) -->
      <button class="arrow arrow-prev" (click)="onPrevClick()"><i class="arrow-icon"></i></button>
      <button class="arrow arrow-next" (click)="onNextClick()"><i class="arrow-icon"></i></button>
    </div>
  </div>

  <div class="thumbnail-carousel">
    <div
      #thumbnailCarousel
      class="wrapper centered-carousel-obj"
      (click)="onThumbnailClick($event)"
      *ngIf="galleryItems?.length; else thumbnailsPlaceholder"
      [@fadeInOutAnimation]
    >
      <ng-container *ngFor="let item of galleryItems; let i = index">
        <div class="slide">
          <!-- use the main click event from wrapper element for routing (only this solution works with clones) -->
          <div
            trImageLazyLoad
            [ngStyle]="{ 'background-image': item.url.thumbnail ? 'url(\'' + item.url.thumbnail + '\')' : 'none' }"
            class="card-wrapper"
            [attr.data-index]="i"
          >
            <div class="link-card" [attr.data-index]="i"></div>
            <button class="position-button" [attr.data-index]="i"></button>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="arrows-container">
      <!-- custom arrows: workaround of infinite step bug (diff animation) -->
      <button class="arrow arrow-prev" (click)="onThumbnailPrevClick()"><i class="arrow-icon"></i></button>
      <button class="arrow arrow-next" (click)="onThumbnailNextClick()"><i class="arrow-icon"></i></button>
    </div>
  </div>
</section>
<app-gallery-layer
  [isOpened]="isGalleryOpened"
  [isFairy]="true"
  [personData]="personData"
  [galleryItems]="galleryItems"
  [itemIndex]="actualElemId"
  (galleryClose)="isGalleryOpened = false"
  *ngIf="galleryItems?.length"
></app-gallery-layer>

<ng-template #galleryPlaceholder>
  <div class="wrapper centered-carousel-obj">
    <div class="slide">
      <div class="card-wrapper"></div>
    </div>
  </div>
</ng-template>

<ng-template #thumbnailsPlaceholder>
  <div class="wrapper centered-carousel-obj">
    <div class="slide">
      <div class="card-wrapper">
        <div class="link-card"></div>
      </div>
    </div>
  </div>
</ng-template>
