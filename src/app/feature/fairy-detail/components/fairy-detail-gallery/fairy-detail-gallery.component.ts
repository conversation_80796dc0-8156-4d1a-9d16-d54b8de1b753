import { animate, style, transition, trigger } from '@angular/animations';
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ImageLazyLoadDirective, UtilService } from '@trendency/kesma-core';
import { NgFor, NgIf, NgStyle } from '@angular/common';
import { FairyGalleryItem, GalleryLayerComponent, MainFairyInterface } from '../../../../shared';
import { GalleryImage } from '@trendency/kesma-ui';
import { fairyGalleryItemToGalleryImage } from '../../fairy-detail.utils';

@Component({
  selector: 'app-fairy-detail-gallery',
  templateUrl: './fairy-detail-gallery.component.html',
  styleUrls: ['./fairy-detail-gallery.component.scss'],
  encapsulation: ViewEncapsulation.None,
  animations: [trigger('fadeInOutAnimation', [transition(':enter', [style({ opacity: 0 }), animate('.300s ease-out', style({ opacity: 1 }))])])],
  imports: [NgIf, NgFor, NgStyle, GalleryLayerComponent, ImageLazyLoadDirective],
})
export class FairyDetailGalleryComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() personData: MainFairyInterface;
  galleryItems: GalleryImage[];
  actualElemId = 0;
  tnActualElemId = 0;
  tnStep = 3;
  isGalleryOpened = false;
  @ViewChild('carousel', { static: false }) private readonly carousel: ElementRef<HTMLDivElement>;
  @ViewChild('thumbnailCarousel', { static: false }) private readonly thumbnailCarousel: ElementRef<HTMLDivElement>;
  private readonly unsubscribe$ = new Subject<boolean>();
  private readonly galleryChange = new Subject<FairyGalleryItem[]>();

  constructor(
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  @Input() set gallery(value: FairyGalleryItem[]) {
    this.isGalleryOpened = false;
    if (this.galleryItems) {
      this.galleryItems = [];
      this.galleryChange.next(value);
      return;
    }
    this.galleryItems = (value || []).map(fairyGalleryItemToGalleryImage);
  }

  ngOnInit(): void {
    this.galleryChange.pipe(takeUntil(this.unsubscribe$)).subscribe((items) => {
      setTimeout(() => {
        this.galleryItems = (items || []).map(fairyGalleryItemToGalleryImage);
        this.changeRef.detectChanges();
        setTimeout(() => {
          this.initSlick();
        }, 100);
      }, 100);
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  ngAfterViewInit(): void {
    this.initSlick();
  }

  onMainSlickClick(event: MouseEvent): void {
    const srcElement: HTMLImageElement = event.target as HTMLImageElement;
    const slickIndex = parseInt(srcElement?.dataset?.['index'] ?? '0', 10);

    if (!isNaN(slickIndex)) {
      this.openGallery(slickIndex);
    }
  }

  onThumbnailClick(event: MouseEvent): void {
    const srcElement: HTMLImageElement = event.target as HTMLImageElement;
    const slickIndex = parseInt(srcElement?.dataset?.['index'] ?? '0', 10);
    if (!isNaN(slickIndex)) {
      $(this.carousel?.nativeElement).slick('slickGoTo', slickIndex);
    }
  }

  onNextClick(): void {
    $(this.carousel?.nativeElement).slick('slickNext');
  }

  onPrevClick(): void {
    $(this.carousel?.nativeElement).slick('slickPrev');
  }

  onThumbnailNextClick(): void {
    $(this.thumbnailCarousel?.nativeElement).slick('slickNext');
  }

  onThumbnailPrevClick(): void {
    $(this.thumbnailCarousel?.nativeElement).slick('slickPrev');
  }

  private initSlick(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    $(this.carousel.nativeElement)
      .slick({
        dots: false,
        autoplay: false,
        lazyLoad: 'ondemand',
        centerMode: false,
        slidesToShow: 1,
        slidesToScroll: 1, // not working with centermode true!
        speed: 1500,
        autoplaySpeed: 3000,
        variableWidth: false,
        arrows: false,
        infinite: true,
        responsive: [
          {
            variableWidth: false,
            breakpoint: 1024,
            settings: {
              slidesToScroll: 1,
              slidesToShow: 1,
            },
          },
        ],
      })
      .on('afterChange', (_event: any, _slick: any, currentSlide: number) => {
        this.actualElemId = currentSlide;
      });

    $(this.thumbnailCarousel.nativeElement)
      .slick({
        dots: false,
        autoplay: false,
        lazyLoad: 'ondemand',
        centerMode: false,
        slidesToShow: 3,
        slidesToScroll: this.tnStep, // not working with centermode true!
        speed: 1500,
        autoplaySpeed: 3000,
        variableWidth: true,
        arrows: false,
        infinite: true,
        responsive: [
          {
            variableWidth: false,
            breakpoint: 1024,
            settings: {
              slidesToScroll: 1,
              slidesToShow: 1,
            },
          },
        ],
      })
      .on('afterChange', (_event: any, _slick: any, currentSlide: number) => {
        this.tnActualElemId = currentSlide;
      });
  }

  private openGallery(index: number): void {
    this.isGalleryOpened = true;
    this.goto(index);
    this.tnGoto(index);
  }

  private goto(itemIndex: number): void {
    this.actualElemId = itemIndex;
    $(this.carousel?.nativeElement).slick('slickGoTo', this.actualElemId);
  }

  private tnGoto(itemIndex: number): void {
    this.tnActualElemId = itemIndex;
    $(this.thumbnailCarousel?.nativeElement).slick('slickGoTo', this.tnActualElemId);
  }
}
