@use 'shared' as *;

.big-image-carousel {
  position: relative;
  margin-bottom: 20px;
  @include media-breakpoint-down(sm) {
    width: calc(100% + 30px);
    margin-left: -15px;
    margin-right: -15px;
    margin-bottom: 10px;
    padding-top: 45px;
  }

  .arrow {
    position: absolute;
    top: 0;
    height: 100%;
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    @include media-breakpoint-down(sm) {
      height: 40px;
      width: 30px;
    }

    .arrow-icon {
      width: 30px;
      height: 30px;
    }

    &.arrow-prev {
      left: 0;
      background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 100%);

      .arrow-icon {
        @include icon('icons/arrow-left-narrow.svg');
      }

      @include media-breakpoint-down(sm) {
        right: 70px;
        left: initial;
      }
    }

    &.arrow-next {
      right: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 100%);

      .arrow-icon {
        @include icon('icons/arrow-right-narrow.svg');
      }

      @include media-breakpoint-down(sm) {
        right: 20px;
      }
    }
  }

  .slide {
    &:not(.slick-slide):not(:first-child) {
      display: none;
    }

    .card-wrapper {
      cursor: pointer;
      @include imgRatio(680%, 900%);
      @include imgZoom();
    }
  }
}

.thumbnail-carousel {
  position: relative;
  @include media-breakpoint-down(sm) {
    width: calc(100% + 30px);
    margin-left: -15px;
    margin-right: -15px;
  }

  .arrow {
    position: absolute;
    top: 0;
    height: 100%;
    width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    .arrow-icon {
      width: 15px;
      height: 15px;
      display: block;
    }

    &.arrow-prev {
      left: 0;
      background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 100%);
      padding-right: 20px;

      .arrow-icon {
        @include icon('icons/arrow-left.svg');
      }
    }

    &.arrow-next {
      right: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 100%);
      padding-left: 20px;

      .arrow-icon {
        @include icon('icons/arrow-right.svg');
      }
    }
  }

  .slide {
    cursor: pointer;

    &:not(.slick-slide):not(:first-child) {
      display: none;
    }

    padding: 0 10px 0 0;

    .card-wrapper {
      width: 200px;
      height: 264px;
      background-position: center;
      background-size: cover;
      cursor: pointer;

      @include media-breakpoint-down(sm) {
        width: 150px;
        height: 198px;
      }
    }
  }
}
