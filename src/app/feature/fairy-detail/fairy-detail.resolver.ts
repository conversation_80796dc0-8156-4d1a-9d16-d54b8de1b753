import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService } from '../article/article.service';
import { FairyService } from '../fairy-landing/fairy.service';
import { COLUMN_SLUG, FairyDetailResolverData } from './components/fairy-detail.definitions';
import { ApiResponseMetaList, ApiResult, ArticleCard } from '@trendency/kesma-ui';

const MAX_RELATED_ARTICLE = 6;

@Injectable()
export class FairyDetailResolver {
  constructor(
    private readonly fairyService: FairyService,
    private readonly articleService: ArticleService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<FairyDetailResolverData> {
    return forkJoin({
      participantsData: this.fairyService.getPersonDetails(route.params['personSlug']),
      personData: this.fairyService.getArchivePersonDetails(route.params['personSlug']),
      relatedArticles: this.articleService.getSidebarArticleRecommendations(MAX_RELATED_ARTICLE, COLUMN_SLUG).pipe(
        catchError(() => {
          return of({
            meta: { dataCount: 0 } as ApiResponseMetaList,
            data: [],
          } as ApiResult<ArticleCard[], ApiResponseMetaList>);
        })
      ),
    }).pipe(
      catchError((err) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map(({ participantsData, personData, relatedArticles }) => ({
        participantsData,
        personData,
        relatedArticles: relatedArticles?.data || [],
      }))
    );
  }
}
