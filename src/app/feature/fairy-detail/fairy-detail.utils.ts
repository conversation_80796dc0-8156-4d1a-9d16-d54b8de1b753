import { GalleryImage } from '@trendency/kesma-ui';
import { FairyGalleryItem } from '../../shared';

export const fairyGalleryItemToGalleryImage = (fairyItem: FairyGalleryItem): GalleryImage =>
  fairyItem && {
    id: fairyItem.id?.toString(),
    title: fairyItem.name,
    caption: fairyItem.name,
    altText: fairyItem.name,
    url: {
      thumbnail: fairyItem.thumbnail2_src,
      fullSize: fairyItem.thumbnail_src,
    },
    photographer: '',
    source: '',
    creationDate: fairyItem.created_time,
  };
