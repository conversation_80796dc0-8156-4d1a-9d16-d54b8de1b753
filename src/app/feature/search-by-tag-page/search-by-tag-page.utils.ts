import { isNil, pickBy } from 'lodash-es';
import { QueryParams, SearchParamsAndPage } from './search-by-tag-page.definitions';
import { not } from '../../shared';

export const mapSearchParamsAndPageToQueryParams = ({ searchParams: { sortDirection }, page }: SearchParamsAndPage): QueryParams =>
  pickBy(
    {
      sortDirection,
      page: String(page),
    },
    not(isNil)
  );

export const mapQueryParamsToSearchParamsAndPage = (queryParams: QueryParams): SearchParamsAndPage => ({
  searchParams: {
    sortDirection: queryParams.sortDirection || 'desc',
  },
  page: Number(queryParams.page) || 0,
});
