import { ArticleCard, Tag } from '@trendency/kesma-ui';
import { List, ListResult } from '../../shared';

export type ResolvedData = Readonly<{
  tag: Tag;
  results: List<ArticleCard>;
  totalResultCount: number;
}>;

export type SearchParams = Readonly<{
  sortDirection: SortDirection;
}>;

export type QueryParams = Readonly<{
  sortDirection?: SortDirection;
  page?: string;
}>;

export type SortDirection = 'asc' | 'desc';

export type SearchResult = ListResult<ArticleCard>;

export type SearchParamsAndPage = Readonly<{
  searchParams: SearchParams;
  page: number;
}>;
