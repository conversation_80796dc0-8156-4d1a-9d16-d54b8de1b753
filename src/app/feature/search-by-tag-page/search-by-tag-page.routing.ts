import { Routes } from '@angular/router';
import { SearchByTagPageComponent } from './search-by-tag-page.component';
import { ArticleCardsByTagResolver } from './search-by-tag-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const SEARCH_BY_TAG_PAGE_ROUTES: Routes = [
  {
    path: ':tag',
    component: SearchByTagPageComponent,
    resolve: {
      resolvedData: ArticleCardsByTagResolver,
    },
    providers: [ArticleCardsByTagResolver],
    canActivate: [PageValidatorGuard],
  },
];
