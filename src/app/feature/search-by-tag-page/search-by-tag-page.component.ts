import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ArticleService } from '../article/article.service';
import { Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, map, switchMap, takeUntil } from 'rxjs/operators';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { Location, NgIf } from '@angular/common';
import { isEqual } from 'lodash-es';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, createCanonicalUrlForPageablePage, Tag } from '@trendency/kesma-ui';
import {
  defaultMetaInfo,
  List,
  mapSearchResultToArticleCard,
  MAX_RESULTS_PER_PAGE,
  MegyeiUtils,
  PortalConfigService,
  SearchPageBaseComponent,
} from '../../shared';
import { ResolvedData, SearchResult } from './search-by-tag-page.definitions';
import { mapQueryParamsToSearchParamsAndPage, mapSearchParamsAndPageToQueryParams } from './search-by-tag-page.utils';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-search-by-tag-page',
  templateUrl: './search-by-tag-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./search-by-tag-page.component.scss'],
  imports: [SearchPageBaseComponent, NgIf, FormsModule, ReactiveFormsModule, SidebarComponent],
})
export class SearchByTagPageComponent implements OnInit, OnDestroy {
  searchForm = new UntypedFormGroup({
    sortDirection: new UntypedFormControl(),
  });

  tag: Tag;
  page = 0;

  results: List<ArticleCard>;
  totalResultCount: number;
  isLoading = false;

  private readonly searchRequest$ = new Subject<void>();
  private readonly searchResult$: Observable<SearchResult>;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly articleService: ArticleService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly location: Location,
    private readonly seo: SeoService,
    private readonly megyeiUtils: MegyeiUtils,
    private readonly changeRef: ChangeDetectorRef
  ) {
    this.bindMethods();
    this.searchResult$ = this.searchRequest$.pipe(switchMap(() => this.fetchResults()));
  }

  ngOnInit(): void {
    this.populateDataFromResolver();
    this.populateSearchFormAndPageFromQueryParams();
    this.setSeoMetaData();

    this.resetPageOnSearchFormChanges();
    this.searchOnSearchParamsChanges();
    this.setLocalResultDataOnSearchResultArrival();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  search(): void {
    this.updateQueryParams();
    this.searchRequest$.next();
    this.changeRef.detectChanges();
  }

  onPageChange(page: number): void {
    this.page = page - 1;
    this.search();
  }

  private populateDataFromResolver(): void {
    const { tag, results, totalResultCount } = this.route.snapshot.data['resolvedData'] as ResolvedData;

    this.tag = tag;
    this.results = results;
    this.totalResultCount = totalResultCount;
  }

  private populateSearchFormAndPageFromQueryParams(): void {
    const { searchParams, page } = mapQueryParamsToSearchParamsAndPage(this.route.snapshot.queryParams);
    this.searchForm.setValue(searchParams);
    this.page = page;
  }

  private resetPageOnSearchFormChanges(): void {
    this.searchForm.valueChanges.pipe(distinctUntilChanged(isEqual), takeUntil(this.destroy$)).subscribe(() => this.resetPage());
  }

  private searchOnSearchParamsChanges(): void {
    this.searchForm.valueChanges.pipe(distinctUntilChanged(), debounceTime(500)).subscribe(() => this.search());
  }

  private setLocalResultDataOnSearchResultArrival(): void {
    this.searchResult$.pipe(takeUntil(this.destroy$)).subscribe(({ items, totalCount }) => {
      this.results = items;
      this.totalResultCount = totalCount;
      this.changeRef.markForCheck();
    });
  }

  private updateQueryParams(): void {
    const queryParams = mapSearchParamsAndPageToQueryParams({ searchParams: this.searchForm.value, page: this.page + 1 });
    const urlTree = this.router.createUrlTree([], { relativeTo: this.route, queryParams });
    this.location.go(urlTree.toString());
  }

  private fetchResults(): Observable<SearchResult> {
    this.isLoading = true;
    const hasOrderByAsc = this.searchForm.value.sortDirection === 'asc';
    return this.articleService.searchArticleByTags([this.tag.slug], this.page, MAX_RESULTS_PER_PAGE, hasOrderByAsc).pipe(
      map(({ data, meta }) => ({
        items: data
          .map((result) => ({
            ...result,
            tag: this.tag,
          }))
          .map(mapSearchResultToArticleCard),
        totalCount: meta.limitable.rowAllCount ?? 0,
      })),
      finalize(() => (this.isLoading = false)),
      takeUntil(this.destroy$)
    );
  }

  private resetPage(): void {
    this.page = 0;
  }

  private setSeoMetaData(): void {
    const capitalizedTag: string = this.megyeiUtils.capitalizeString(this.tag?.title ?? '');
    const capitalizedPortalSubtitle = this.megyeiUtils.capitalizeString(this.portalConfig?.portalSubtitle ?? '');
    const title = capitalizedTag ? `${capitalizedTag} - ${this.portalConfig?.portalName}` : `Keresés - ${this.portalConfig?.portalName}`;
    // eslint-disable-next-line max-len
    const desc = `${capitalizedTag} ${defaultMetaInfo(this.portalConfig).labelDescriptionMetaSuffix} - ${this.portalConfig?.portalName} - ${capitalizedPortalSubtitle}`;

    const seoMetaData: IMetaData = {
      ...defaultMetaInfo(this.portalConfig),
      title,
      ogTitle: title,
      twitterTitle: title,
      description: desc,
      ogDescription: desc,
      twitterDescription: desc,
    };

    this.seo.setMetaData(seoMetaData);
    const canonical = createCanonicalUrlForPageablePage('cimke', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  private bindMethods(): void {
    this.fetchResults = this.fetchResults.bind(this);
    this.resetPage = this.resetPage.bind(this);
    this.search = this.search.bind(this);
  }
}
