<app-search-page-base
  [title]="tag?.title"
  [results]="results"
  [isLoading]="isLoading"
  [totalResultCount]="totalResultCount"
  [page]="page"
  [isTagVisible]="true"
  (pageChange)="onPageChange($event)"
>
  <h1 class="title" slot="title">
    <span class="keyword" *ngIf="tag?.title">{{ tag.title }}</span>
    <span class="subtitle">címke</span>
  </h1>

  <form class="search-inputs" [formGroup]="searchForm" slot="search-inputs">
    <div class="custom-select-box-flyer">
      <label class="custom-select-label-flyer" for="sortDirection">Rendezés</label>
      <select class="custom-select-flyer" formControlName="sortDirection" id="sortDirection">
        <option value="desc">Legfrissebb elöl</option>
        <option value="asc">Leg<PERSON>ge<PERSON><PERSON> elöl</option>
      </select>
      <i class="flyer-icon"></i>
    </div>
  </form>

  <ng-container slot="result-count">
    <span class="result-count-keyword">{{ tag?.title }}</span
    >{{ tag?.title ? ' címkére' : '' }} {{ totalResultCount }} db találat
  </ng-container>

  <ng-container slot="sidebar">
    <app-sidebar></app-sidebar>
  </ng-container>
</app-search-page-base>
