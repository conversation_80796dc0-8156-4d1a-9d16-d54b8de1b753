import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Data, Router } from '@angular/router';
import { ArticleService } from '../article/article.service';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { QueryParams } from './search-by-tag-page.definitions';
import { Response } from 'express';
import { RESPONSE, UtilService } from '@trendency/kesma-core';
import { RedirectService } from '@trendency/kesma-ui';
import { mapSearchResultToArticleCard, MAX_RESULTS_PER_PAGE, PortalConfigService } from '../../shared';

@Injectable()
export class ArticleCardsByTagResolver {
  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly articleService: ArticleService,
    private readonly router: Router,
    @Inject(RESPONSE) @Optional() private readonly response: Response,
    private readonly utilsService: UtilService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const { tag } = route.params;
    const { page = 0, sortDirection } = route.queryParams as QueryParams;

    return forkJoin({
      articleCardResult: this.articleService.searchArticleByTags([tag], Number(page), MAX_RESULTS_PER_PAGE, sortDirection === 'asc'),
      tag: this.articleService.getTagBySlug(tag),
    }).pipe(
      tap((data: Data) => {
        if (data['articleCardResult'].data.length === 0) {
          const meta = data['articleCardResult']?.meta;

          if (this.redirectService.shouldBeRedirect(+page, data['articleCardResult']?.data)) {
            this.redirectService.redirectOldUrl(`cimke/${tag}`, false, 302);
          }

          // if redirect needed (merged tag)
          if (meta?.redirect?.tag?.slug) {
            const { siteUrl } = this.portalConfig;
            const redirectUrl = `${siteUrl}/cimke/${meta.redirect.tag.slug}`;

            // client side (basic redirection)
            if (this.utilsService.isBrowser()) {
              window.location.href = redirectUrl;
              return;
            }

            // server side (SSR - 301 redirect with express js response injector)
            this.response.status(301);
            this.response.setHeader('location', redirectUrl);
          }
        }
      }),
      map(({ articleCardResult, tag }) => ({
        tag,
        results: articleCardResult?.data?.map(mapSearchResultToArticleCard),
        totalResultCount: articleCardResult?.meta?.limitable?.rowAllCount,
      })),
      catchError(() => {
        this.router.navigate(['/', '404'], { skipLocationChange: true });
        return of(null);
      })
    );
  }
}
