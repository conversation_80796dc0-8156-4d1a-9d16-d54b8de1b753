@if (isBrowser) {
  <kesma-layout-editor [layoutComponentRef]="layoutComponent" [placeholderOverrides]="placeholderOverrides"></kesma-layout-editor>
  <ng-template
    #layoutComponent
    let-structure="structure"
    let-content="content"
    let-contentComponentsWrapper="contentComponentsWrapper"
    let-contentComponentsInnerWrapper="contentComponentsInnerWrapper"
    let-blockTitleWrapper="blockTitleWrapper"
    let-editorFrameSize="editorFrameSize"
  >
    <app-layout
      [layoutType]="LayoutPageType.HOME"
      [structure]="structure"
      [configuration]="content"
      [contentComponentsWrapper]="contentComponentsWrapper"
      [contentComponentsInnerWrapper]="contentComponentsInnerWrapper"
      [blockTitleWrapper]="blockTitleWrapper"
      [editorFrameSize]="editorFrameSize"
    ></app-layout>
  </ng-template>

  <ng-template #placeholderOverrides let-layoutElement="layoutElement" let-contentDisplay="contentDisplay">
    <ng-container [ngSwitch]="layoutElement.contentType">
      <ng-container *ngSwitchCase="LayoutElementContentType.Ad">
        <kesma-advertisement-placeholder [layoutElement]="layoutElement"></kesma-advertisement-placeholder>
      </ng-container>
      <ng-container *ngSwitchDefault [ngTemplateOutlet]="contentDisplay"></ng-container>
    </ng-container>
  </ng-template>
}
