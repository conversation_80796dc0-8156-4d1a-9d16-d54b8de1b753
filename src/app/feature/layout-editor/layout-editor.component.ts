import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import {
  AdvertisementPlaceholderComponent,
  LayoutEditorComponent as KesmaLayoutEditorComponent,
  LayoutElementContentType,
  LayoutPageType,
} from '@trendency/kesma-ui';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NgSelectModule } from '@ng-select/ng-select';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-layout-editor',
  templateUrl: './layout-editor.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgSelectModule,
    DragDropModule,
    KesmaLayoutEditorComponent,
    LayoutComponent,
    NgS<PERSON>,
    Ng<PERSON><PERSON>Case,
    AdvertisementPlaceholderComponent,
    Ng<PERSON><PERSON>Def<PERSON>,
    NgTemplateOutlet,
  ],
})
export class LayoutEditorComponent implements OnInit {
  readonly utilService = inject(UtilService);
  readonly seoService = inject(SeoService);

  isBrowser = this.utilService.isBrowser();

  readonly LayoutElementContentType = LayoutElementContentType;
  readonly LayoutPageType = LayoutPageType;

  ngOnInit(): void {
    this.seoService.setMetaData({
      robots: 'noindex, nofollow',
    });
  }
}
