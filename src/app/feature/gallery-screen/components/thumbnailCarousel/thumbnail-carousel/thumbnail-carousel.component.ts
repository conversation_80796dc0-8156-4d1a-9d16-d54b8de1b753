import { Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, Input, Output } from '@angular/core';
import { GalleryImage, SwiperBaseComponent } from '@trendency/kesma-ui';
import { NgFor, NgIf } from '@angular/common';
import { List } from '../../../../../shared';

@Component({
  selector: 'app-thumbnail-carousel',
  templateUrl: './thumbnail-carousel.component.html',
  styleUrls: ['./thumbnail-carousel.component.scss'],
  imports: [NgFor, NgIf],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ThumbnailCarouselComponent extends SwiperBaseComponent<never> {
  @Input() galleryItems: List<GalleryImage> = [];
  @Output() thumbnailClicked = new EventEmitter<number>();

  swiperBreakpoints = {
    768: {
      slidesPerView: 4,
      slidesPerGroup: 4,
      speed: 1500,
    },
    1024: {
      slidesPerView: 5,
      slidesPerGroup: 5,
    },
  };

  onThumbnailSlickClick(i: number): void {
    this.thumbnailClicked.emit(i);
  }

  slideNext(): void {
    this.swipeNext();
  }

  slidePrev(): void {
    this.swipePrev();
  }
}
