<div class="wrapper">
  <swiper-container
    #swiper
    [breakpoints]="swiperBreakpoints"
    slides-per-view="2.4"
    slides-per-group="2"
    speed="1000"
    rewind="true"
    center-insufficient-slides="true"
  >
    <swiper-slide *ngFor="let item of galleryItems; let i = index">
      <div class="slide" (click)="onThumbnailSlickClick(i)">
        <img *ngIf="item?.url?.thumbnail" [src]="item?.url?.fullSize || item?.url?.thumbnail" class="card-wrapper" />
      </div>
    </swiper-slide>
  </swiper-container>
</div>
<button class="arrow arrow-prev" (click)="slidePrev()"><i class="arrow-icon"></i></button>
<button class="arrow arrow-next" (click)="slideNext()"><i class="arrow-icon"></i></button>
