@use 'shared' as *;

$arrowInBreakpoint: 1606px;

.arrow {
  position: absolute;
  top: 0;
  height: 100%;
  width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  @include media-breakpoint-down(sm) {
    width: 60px;
  }

  .arrow-icon {
    width: 20px;
    height: 20px;
    min-width: 20px !important;
    min-height: 20px !important;
    display: block;
  }

  &.arrow-prev {
    left: 0;
    background: linear-gradient(270deg, rgba($black, 0) 0%, rgba($black, 0.7) 100%);
    padding-right: 20px;
    padding-left: 28px;

    .arrow-icon {
      @include icon('icons/icon-arrow-left-thumbnail.svg');
    }

    @media screen and (min-width: $arrowInBreakpoint) {
      left: -50px;
    }
  }

  &.arrow-next {
    right: 0;
    background: linear-gradient(90deg, rgba($black, 0) 0%, rgba($black, 0.7) 100%);
    padding-right: 28px;
    padding-left: 20px;

    .arrow-icon {
      @include icon('icons/icon-arrow-right-thumbnail.svg');
    }

    @media screen and (min-width: $arrowInBreakpoint) {
      right: -50px;
    }
  }
}

.slide {
  padding: 0 20px;
  cursor: pointer;
  @include media-breakpoint-down(sm) {
    padding: 0 10px 0 0;
  }

  .card-wrapper {
    height: 120px;
    object-fit: cover;
    aspect-ratio: 16/9;
  }
}
