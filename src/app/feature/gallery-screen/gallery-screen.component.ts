import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GalleryScreenService } from './gallery-screen.service';
import { FullscreenDirective, ImageLazyLoadDirective, IMetaData, PublishDatePipe, SeoService, StorageService } from '@trendency/kesma-core';
import { ThumbnailCarouselComponent } from './components/thumbnailCarousel/thumbnail-carousel/thumbnail-carousel.component';
import { Ng<PERSON>lass, NgFor, NgIf, NgStyle } from '@angular/common';
import {
  DEFAULT_PUBLISH_DATE_FORMAT,
  defaultMetaInfo,
  GalleryApiService,
  GalleryCardComponent,
  GalleryRecommendationResponse,
  List,
  PortalConfigService,
  SocialRowComponent,
} from '../../shared';
import { GalleryData, GalleryImage, GalleryRecommendationData } from '@trendency/kesma-ui';
import { AdultComponent } from '../article/components/adult/adult.component';

@Component({
  selector: 'app-gallery-screen',
  templateUrl: './gallery-screen.component.html',
  styleUrls: ['./gallery-screen.component.scss'],
  imports: [
    NgIf,
    AdultComponent,
    NgClass,
    SocialRowComponent,
    NgStyle,
    NgFor,
    GalleryCardComponent,
    ThumbnailCarouselComponent,
    PublishDatePipe,
    ImageLazyLoadDirective,
    FullscreenDirective,
  ],
})
export class GalleryScreenComponent implements AfterViewInit, OnDestroy, OnInit {
  selectedItem: GalleryImage;
  actualElemId = 1;
  tnActualElemId = 0;
  tnStep = 5;
  isSocialOpen = false;
  isUserAdultChoice: boolean;
  isFullscreen = false;
  galleryItems: List<GalleryImage> = [];
  gallery: GalleryData;
  recommendations: GalleryRecommendationData[] = [];
  private readonly unsubscribe$ = new Subject<void>();
  @ViewChild('carousel') private readonly carousel: ElementRef<HTMLDivElement>;

  constructor(
    private readonly router: Router,
    private readonly cd: ChangeDetectorRef,
    private readonly portalConfig: PortalConfigService,
    private readonly galleryApiService: GalleryApiService,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly galleryScreenService: GalleryScreenService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly storage: StorageService
  ) {}

  get isUserAdultChoiceFromStorage(): boolean {
    return this.storage.getSessionStorageData('isAdultChoice', false) ?? false;
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    this.handleEvent(event);
  }

  ngOnInit(): void {
    const {
      galleryDetails,
      recommended,
    }: {
      galleryDetails: GalleryData;
      recommended: GalleryRecommendationResponse;
    } = this.route.snapshot.data['pageData'];
    const galleryIndex = this.route.snapshot.params['index'];
    this.gallery = galleryDetails;
    this.isUserAdultChoice = this.isUserAdultChoiceFromStorage;
    this.galleryItems = galleryDetails.images;
    this.recommendations = recommended?.data;
    this.actualElemId = parseInt(galleryIndex, 10) - 1 ? parseInt(galleryIndex, 10) - 1 : 0;
    this.tnActualElemId = parseInt(galleryIndex, 10) - 1 ? parseInt(galleryIndex, 10) - 1 : 0;
    this.setMetaData();

    this.galleryApiService
      .getGalleryRecommendations(this.gallery?.slug)
      .pipe(takeUntil(this.unsubscribe$))

      .subscribe((recommendedData): void => {
        this.recommendations = recommendedData?.data;
        this.changeRef.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    this.setCurrentItem();
    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onUserAdultChoose(isAdult: boolean): void {
    this.isUserAdultChoice = isAdult;
  }

  onCloseClick(): void {
    this.isFullscreen = false;
    this.router.navigate([this.galleryScreenService.getSourceRoute() ?? 'galeriak']);
  }

  onClickGalleryRecommendation(): void {
    this.isFullscreen = false;
  }

  onMainSlickClick(event: MouseEvent): void {
    const srcElement: HTMLImageElement = event.target as HTMLImageElement;
    const slickIndex = parseInt(srcElement.dataset?.['index'] ?? '', 10);

    if (!isNaN(slickIndex)) {
      $(this.carousel.nativeElement).slick('slickGoTo', slickIndex);
    } else if (srcElement?.dataset?.['routerlink']) {
      const newRoute = srcElement?.getAttribute('data-routerlink')?.split(',');
      this.router.navigate(newRoute ?? []);
    }
  }

  onThumbnailSlickClick(index: number): void {
    this.actualElemId = index;
    this.setCurrentItem();
    this.router.navigate([`../${index + 1}`], { relativeTo: this.route });
  }

  onNextClick(): void {
    this.actualElemId = this.actualElemId < this.galleryItems.length ? this.actualElemId + 1 : 0;
    this.setCurrentItem();
    this.router.navigate([`../${this.actualElemId + 1}`], { relativeTo: this.route });
  }

  onPervClick(): void {
    this.actualElemId = this.actualElemId - 1 > -1 ? this.actualElemId - 1 : this.galleryItems.length;
    this.setCurrentItem();
    this.router.navigate([`../${this.actualElemId + 1}`], { relativeTo: this.route });
  }

  goto(itemIndex: number): void {
    this.actualElemId = itemIndex;
    this.router.navigate([`../${itemIndex}`], { relativeTo: this.route });
  }

  tnGoto(itemIndex: number): void {
    this.tnActualElemId = itemIndex;
    this.router.navigate([`../${itemIndex}`], { relativeTo: this.route });
  }

  onSocialOpen(): void {
    this.isSocialOpen = !this.isSocialOpen;
  }

  onBackButtonClick(): void {
    this.isFullscreen = false;
    this.router.navigate(['galeriak']);
  }

  onToggleFullscreen(fullScreenDirective: FullscreenDirective): void {
    this.isFullscreen = !this.isFullscreen;
    fullScreenDirective.toggleFullScreen();
  }

  private handleEvent(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowLeft':
        this.onPervClick();
        break;
      case 'ArrowRight':
        this.onNextClick();
        break;
      case 'Escape':
        this.onCloseClick();
        break;
      default:
        return;
    }
    event.preventDefault();
    event.stopPropagation();
  }

  private setCurrentItem(): void {
    this.selectedItem = this.galleryItems[this.actualElemId];
  }

  private setMetaData(): void {
    if (!this.gallery) {
      return;
    }

    const title = `${this.portalConfig?.portalName} - ${this.gallery?.title}`;

    const metaData: IMetaData = {
      ...defaultMetaInfo(this.portalConfig),
      title,
      description: this.gallery?.description,
      ogTitle: title,
      ogImage: this.gallery.highlightedImageUrl,
    };
    this.seo.setMetaData(metaData);
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
