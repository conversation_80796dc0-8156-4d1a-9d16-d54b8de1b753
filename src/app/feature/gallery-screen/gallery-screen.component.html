<ng-container *ngIf="!isUserAdultChoice && gallery?.isAdult; else galleryContent">
  <app-adult (isUserAdult)="onUserAdultChoose($event)"></app-adult>
</ng-container>

<ng-template #galleryContent>
  <section class="gallery-layer opened" #mainWrapper *ngIf="galleryItems" trFullscreen #fullscreen="trFullscreen">
    <button class="fullscreen-button" [ngClass]="{ 'min-icon': isFullscreen, 'full-icon': !isFullscreen }" (click)="onToggleFullscreen(fullscreen)"></button>
    <button class="layer-close-button" (click)="onCloseClick()"></button>
    <div class="wrapper">
      <div class="gallery-header" *ngIf="actualElemId + 1 <= galleryItems?.length; else galleryRecommendationHeader">
        <div class="gallery-header-left">
          <button type="button" class="button back-button" (click)="onBackButtonClick()"></button>
          <h1 class="name">{{ gallery?.title || selectedItem?.caption }}</h1>
        </div>
        <div class="gallery-header-middle" *ngIf="!isFullscreen && actualElemId + 1 <= galleryItems?.length">
          <div class="d-flex">
            <div class="left-column center">
              <div class="selected-image-count">{{ actualElemId + 1 }} / {{ galleryItems?.length }}</div>
              <div class="selected-caption">{{ selectedItem?.caption }}</div>
            </div>
            <div class="right-column">
              <button class="arrow arrow-next" (click)="onSocialOpen()">
                <i class="arrow-icon" [ngClass]="{ 'arrow-icon-down': !isSocialOpen, 'arrow-icon-up': isSocialOpen }"></i>
              </button>
            </div>
          </div>
          <app-social-row *ngIf="isSocialOpen"></app-social-row>
        </div>

        <div class="gallery-header-right gallery-header-info">
          <div class="modal-count">{{ galleryItems?.length }} fotó</div>
          <div class="modal-date">{{ gallery?.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</div>
          <div class="modal-date" *ngIf="selectedItem?.source">Forrás: {{ selectedItem?.source }}</div>
          <div class="modal-credit" *ngIf="selectedItem?.photographer; else galleryPhotographer">Fotó: {{ selectedItem?.photographer }}</div>
          <ng-template #galleryPhotographer>
            <div class="modal-credit" *ngIf="gallery?.photographer">Fotók: {{ gallery.photographer }}</div>
          </ng-template>
        </div>
      </div>

      <div class="big-image-carousel normal-gallery" [ngClass]="{ 'gallery-page': actualElemId + 1 > galleryItems?.length, fullscreen: isFullscreen }">
        <div class="wrapper centered-carousel-obj">
          <div class="slide" *ngIf="actualElemId < this.galleryItems.length; else recommendationBlock">
            <div
              class="card-wrapper"
              trImageLazyLoad
              [ngStyle]="{
                'background-image': galleryItems[actualElemId]?.url?.fullSize ? 'url(\'' + galleryItems[actualElemId]?.url?.fullSize + '\')' : 'none',
              }"
              [ngClass]="{ fullscreen: isFullscreen }"
            >
              <div class="link-card"></div>
              <button class="position-button"></button>
            </div>
          </div>
          <ng-template #recommendationBlock>
            <div class="slide recommendation">
              <div class="recommendations-container">
                <div class="recommendations">
                  <ng-container *ngIf="recommendations?.length">
                    <app-gallery-card
                      *ngFor="let galleryCard of recommendations"
                      [recommended]="galleryCard"
                      class="gallery-card-component"
                      (click)="onClickGalleryRecommendation()"
                      [cardStyle]="2"
                    ></app-gallery-card>
                  </ng-container>
                </div>
              </div>
            </div>
          </ng-template>
        </div>

        <div class="arrows-container">
          <!-- custom arrows: workaround of infinite step bug (diff animation) -->
          <button class="arrow arrow-prev" (click)="onPervClick()"><i class="arrow-icon"></i></button>
          <button class="arrow arrow-next" (click)="onNextClick()"><i class="arrow-icon"></i></button>
        </div>
      </div>
      <div #carousel class="wrapper centered-carousel-obj" (click)="onMainSlickClick($event)"></div>

      <div class="thumbnail-carousel" [ngClass]="{ disable: isFullscreen || actualElemId + 1 > galleryItems?.length }">
        <app-thumbnail-carousel [galleryItems]="galleryItems" (thumbnailClicked)="onThumbnailSlickClick($event)"></app-thumbnail-carousel>
      </div>
    </div>
  </section>
</ng-template>

<ng-template #galleryRecommendationHeader>
  <div class="gallery-header gallery-recommendation-header">
    <div class="section-header-block">
      <h2 class="section-header-title">Galériák</h2>
    </div>
  </div>
</ng-template>
