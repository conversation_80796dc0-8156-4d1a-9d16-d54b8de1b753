import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService } from '../article/article.service';
import { GalleryScreenPageData } from '../gallery/gallery.definitions';
import { GalleryApiService } from '../../shared';

const MAX_RELATED_ARTICLE_TOP = 2;
const MAX_RELATED_ARTICLE_LEFT = 3;
const MAX_RELATED_ARTICLE_REST = 6;

@Injectable()
export class GalleryScreenResolver {
  constructor(
    private readonly galleryApiService: GalleryApiService,
    private readonly router: Router,
    private readonly articleService: ArticleService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<GalleryScreenPageData> {
    const slug: string = route.params['slug'];

    return forkJoin({
      details: this.galleryApiService.getGalleryDetails(slug),
      recommended: this.galleryApiService.getGalleryRecommendations(slug),
      relatedArticles: this.articleService.getLatestArticles(MAX_RELATED_ARTICLE_TOP + MAX_RELATED_ARTICLE_LEFT + MAX_RELATED_ARTICLE_REST),
    }).pipe(
      map(({ details, recommended }) => ({
        galleryDetails: details,
        recommended,
      })),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
