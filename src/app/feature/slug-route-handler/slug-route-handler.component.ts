import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ArticleComponent } from '../article/article.component';
import { StaticPageComponent } from '../static-page/static-page.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-slug-route-handler',
  templateUrl: './slug-route-handler.component.html',
  styleUrls: ['./slug-route-handler.component.scss'],
  imports: [NgIf, StaticPageComponent, ArticleComponent],
})
export class SlugRouteHandlerComponent implements OnInit {
  staticPage: boolean;

  constructor(private readonly route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.data.subscribe((res) => {
      this.staticPage = !(res?.['articlePageData']?.article || res?.['articlePageData']?.data);
    });
  }
}
