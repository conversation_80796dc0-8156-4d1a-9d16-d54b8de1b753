import { Routes } from '@angular/router';
import { SlugRouteHandlerComponent } from './slug-route-handler.component';
import { StaticPageResolver } from '../static-page/static-page.resolver';
import { ArticleResolverService } from '../article/article.resolver';

export const STATIC_PAGE_ROUTES: Routes = [
  {
    path: '',
    component: SlugRouteHandlerComponent,
    resolve: {
      staticPageData: StaticPageResolver,
      articlePageData: ArticleResolverService,
    },
    providers: [StaticPageResolver, ArticleResolverService],
  },
];
