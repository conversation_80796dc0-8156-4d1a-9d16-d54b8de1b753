<section class="send-news">
  <div class="wrapper">
    <div class="top">
      <div class="page-title">
        <div class="left">
          <span class="page-title-text">H<PERSON><PERSON>üldés</span>
        </div>
      </div>
    </div>
    <div class="left">
      <h1 class="main-title">Van egy jó sztorija? Írja meg nekünk!</h1>
      <p class="lead">Valami érdekeset látott, hallott, fényképezett, videózott? Tud valamit, amit mi nem? Írja meg nekünk!</p>
    </div>
    <div class="right">
      <ng-container *ngIf="!sendSuccess">
        <div class="form-block">
          <form>
            <h4 class="input-block-title">Hír beküld<PERSON>e</h4>

            <div class="input-wrapper" [ngClass]="{ error: false }">
              <div class="input-header">
                <label for="input1">Név</label>
                <div class="error">Hibás név</div>
              </div>
              <input id="input1" type="text" placeholder="Add meg a teljes neved" />
              <div class="error-mobile">Hibás név</div>
            </div>

            <div class="input-wrapper" [ngClass]="{ error: true }">
              <div class="input-header">
                <label for="input2">Név</label>
                <div class="error">Hibás email cím</div>
              </div>
              <input id="input2" type="text" placeholder="Add meg az e-mail címed" />
              <div class="error-mobile">Hibás email cím</div>
            </div>

            <div class="input-wrapper" [ngClass]="{ error: false }">
              <div class="input-header">
                <label for="input3">Település</label>
                <div class="error">Hibás település név</div>
              </div>
              <input id="input3" type="text" placeholder="Add meg a település nevét" />
              <div class="error-mobile">Hibás település név</div>
            </div>

            <div class="input-wrapper" [ngClass]="{ error: true }">
              <div class="input-header">
                <label for="input4">Szöveg</label>
                <div class="error">Hibás szöveg</div>
              </div>
              <textarea id="input4" type="text" placeholder="Írd le a történeted"></textarea>
              <div class="error-mobile">Hibás szöveg</div>
            </div>

            <div class="input-wrapper">
              <div class="input-header">
                <label>Fotó/videó feltöltése</label>
                <div class="info">Max 5 db, max 10 mb</div>
              </div>
              <div class="file-drop">
                <ngx-file-drop dropZoneLabel="Drop files here" (onFileDrop)="dropped($event)" (onFileOver)="fileOver($event)" (onFileLeave)="fileLeave($event)">
                  <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
                    <span class="desc">Húzz ide egy fájlt, vagy</span>
                    <button class="button" type="button" (click)="openFileSelector()">Fájl kiválasztása</button>
                  </ng-template>
                </ngx-file-drop>
                <div class="upload-list">
                  <ng-container *ngFor="let item of files; let i = index">
                    <div class="upload-list-elem {{ item.relativePath | fileType }} {{ fileSizes[i] | fileSizeLimit: 10000000 }}">
                      <div class="file-name">{{ item.relativePath }}</div>
                      <div class="file-size">{{ fileSizes[i] | fileSize }}</div>
                      <div class="file-format-wrong">Nem megfelelő formátum</div>
                      <div class="file-size-wrong">Túl nagy fájlméret</div>
                      <button class="remove-file" (click)="fileRemove()"></button>
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>

            <div class="form-block-bottom">
              <div class="left">
                <div class="custom-input">
                  <input type="checkbox" id="input6" />
                  <label for="input6">Megismerem és elfogadom a bama.hu honlap felhasználási feltételeit.</label>
                </div>
              </div>
              <div class="right">
                <button class="button" (click)="dummySuccessToggle()">Küldés</button>
              </div>
            </div>
          </form>
        </div>
      </ng-container>
      <ng-container *ngIf="sendSuccess">
        <div class="status-box">
          <div class="box-inner-poser">
            <i class="success-icon"></i>
            <p class="box-title">Sikeresen beküldve</p>
            <p class="box-lead">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus condimentum velit nec posuere porttitor. Mauris vitae nibh vel eros pretium
              auctor at vitae metus.
            </p>
            <div class="box-bottom">
              <button class="button" (click)="dummySuccessToggle()">További hír beküldése</button>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</section>
