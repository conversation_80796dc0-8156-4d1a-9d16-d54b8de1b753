import { Component } from '@angular/core';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry, NgxFileDropModule } from 'ngx-file-drop';
import { Ng<PERSON><PERSON>, Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { FileSizePipe } from '@trendency/kesma-core';
import { FileSizeLimitPipe, FileTypePipe } from '../../shared';

@Component({
  selector: 'app-send-news',
  templateUrl: './send-news.component.html',
  styleUrls: ['./send-news.component.scss'],
  imports: [NgIf, Ng<PERSON>lass, NgxFileDropModule, NgFor, FileSizePipe, FileTypePipe, FileSizeLimitPipe],
})
export class SendNewsComponent {
  files: NgxFileDropEntry[] = [];
  fileSizes: number[];
  sendSuccess = false;

  dropped(files: NgxFileDropEntry[]): void {
    this.files = files;
    this.fileSizes = [];
    for (const droppedFile of files) {
      // Is it a file?
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          this.fileSizes.push(file.size);

          // Here you can access the real file
          console.log(droppedFile.relativePath, file);

          /**
           // You could upload it like this:
           const formData = new FormData()
           formData.append('logo', file, relativePath)

           // Headers
           const headers = new HttpHeaders({
           'security-token': 'mytoken'
           })

           this.http.post('https://mybackend.com/api/upload/sanitize-and-save-logo', formData, { headers: headers, responseType: 'blob' })
           .subscribe(data => {
           // Sanitized logo returned from backend
           })
           **/
        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
        console.log(droppedFile.relativePath, fileEntry);
      }
    }

    console.log(this.files);
    console.log(this.fileSizes);
  }

  fileOver(event: Event): void {
    console.log(event);
  }

  fileLeave(event: Event): void {
    console.log(event);
  }

  fileRemove(): void {
    console.log('todo: remove file and filesize');
  }

  dummySuccessToggle(): void {
    this.sendSuccess = !this.sendSuccess;
  }
}
