@use 'shared' as *;

.send-news {
  .wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-bottom: 90px;
    @include media-breakpoint-down(sm) {
      padding-bottom: 40px;
    }

    .top {
      width: 100%;
      padding-top: 50px;
      @include media-breakpoint-down(sm) {
        padding-top: 0;
      }

      .page-title {
        font-size: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 20px 0 20px 20px;
        margin-bottom: 50px;
        @include article-before-line();

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }

        @include media-breakpoint-down(sm) {
          margin-top: 30px;
          margin-bottom: 14px;
          flex-wrap: wrap;
          padding-left: 0;
          padding-top: 0;

          &:before {
            height: 60px;
          }
        }

        .left {
          @include media-breakpoint-down(sm) {
            width: 100%;
          }

          .page-title-text {
            font-weight: 500;
            font-size: 30px;
            line-height: 30px;
            padding-right: 5px;
            text-transform: uppercase;
            @include media-breakpoint-down(sm) {
              font-size: 20px;
              line-height: 30px;
              height: 60px;
              display: flex;
              align-items: center;
              padding: 0 0 0 20px;
            }
          }
        }
      }
    }

    .left,
    .right {
      width: calc(50% - 15px);
      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }

    .left {
      .main-title {
        font-weight: 500;
        font-size: 40px;
        line-height: 50px;
        margin-bottom: 20px;
        @include media-breakpoint-down(md) {
          font-size: 26px;
          line-height: 32px;
        }
      }

      .lead {
        font-weight: normal;
        font-size: 30px;
        line-height: 50px;
        margin-bottom: 20px;
        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 34px;
        }
      }
    }

    .right {
      .form-block {
        border: 1px solid $grey-16;
        border-radius: 6px;
        padding: 50px;
        @include media-breakpoint-down(md) {
          padding: 20px;
        }

        .form-block-bottom {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          margin-top: 45px;

          .left {
            width: calc(100% - 200px);
            @include media-breakpoint-down(sm) {
              width: 100%;
            }
          }

          .right {
            width: 200px;
            display: flex;
            justify-content: flex-end;
            align-items: flex-start;
            @include media-breakpoint-down(sm) {
              margin-top: 25px;
              width: 100%;
              justify-content: center;
            }
          }
        }
      }

      .status-box {
        padding: 50px 15px;
        width: 100%;
        border: 1px solid $grey-16;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;

        .box-inner-poser {
          width: 441px;
          text-align: center;
          max-width: 100%;

          .success-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            margin-bottom: 30px;
            background: $green;
            @include icon('icons/white-check.svg');
            background-size: 55%;
          }

          .box-title {
            font-weight: 500;
            font-size: 30px;
            line-height: 40px;
            margin-bottom: 30px;
          }

          .box-lead {
            font-weight: normal;
            font-size: 16px;
            line-height: 30px;
            margin-bottom: 30px;
          }

          .box-bottom {
            display: flex;
            justify-content: center;
          }
        }
      }
    }
  }
}
