import { animate, style, transition, trigger } from '@angular/animations';
import { ChangeDetectorRef, Component, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  RedirectService,
} from '@trendency/kesma-ui';
import { combineLatest, Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { chunkArray, IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { NgFor, NgIf } from '@angular/common';
import {
  ArticleCardComponent,
  ArticleCardTypes,
  defaultMetaInfo,
  DossierService,
  List,
  PagerComponent,
  PortalConfigService,
  SectionHeaderComponent,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { CombineDossierRequests, DOSSIER_MAIN_NEWS_COUNT, DOSSIER_OTHER_NEWS_PER_PAGE } from './dossier-list.definitions';
import { dossierToArticleCard } from './dossier-list.utils';

@Component({
  selector: 'app-dossier-list',
  templateUrl: './dossier-list.component.html',
  styleUrls: ['./dossier-list.component.scss'],
  encapsulation: ViewEncapsulation.None,
  animations: [trigger('fadeInOutAnimation', [transition(':enter', [style({ opacity: 0 }), animate('.300s ease-out', style({ opacity: 1 }))])])],
  imports: [NgIf, NgFor, ArticleCardComponent, AdvertisementAdoceanComponent, SectionHeaderComponent, PagerComponent, SidebarComponent],
})
export class DossierListComponent implements OnInit, OnDestroy {
  readonly articleCardType = ArticleCardTypes;

  @ViewChild('articleList') readonly articleList: ElementRef;

  slug: string;
  title: string;
  description?: string;
  thumbnail?: string;
  articles: List<ArticleCard> = [];
  articleSections: ArticleCard[][] = [];
  otherNewsPage: List<ArticleCard> = [];
  currentPage = 0;
  maxCount = 0;
  maxPerPage = DOSSIER_OTHER_NEWS_PER_PAGE;
  mainNewsCount = DOSSIER_MAIN_NEWS_COUNT;
  articleIds: string[] = [];
  adverts: AdvertisementsByMedium | undefined;

  private readonly unsubscribe$ = new Subject<boolean>();

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly dossierService: DossierService,
    private readonly seo: SeoService,
    private readonly portalConfig: PortalConfigService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly redirectService: RedirectService
  ) {}

  ngOnInit(): void {
    combineLatest([this.route.data.pipe(map((res): ApiResult<ArticleCard[], ApiResponseMetaList> => res['data'])), this.route.queryParams])
      .pipe(
        tap(() => this.resetAds()),
        switchMap(([{ data, meta }, queryParam]: CombineDossierRequests): Observable<Advertisement[]> => {
          this.slug = this.route.snapshot.params['dossierSlug'];
          this.title = meta?.['title'] as string;

          this.description = meta?.['description'] as string;
          this.thumbnail = meta?.['thumbnail'] as string;

          this.articles = (data ?? []).map((article, index) =>
            index < DOSSIER_MAIN_NEWS_COUNT
              ? {
                  ...article,
                  preTitle: this.title,
                }
              : article
          );
          if (this.articles) {
            this.articleSections = chunkArray(this.articles.slice(0, this.mainNewsCount), 6, 2);
            this.otherNewsPage = this.articles.slice(this.mainNewsCount);
            this.maxCount =
              ((meta?.limitable?.rowAllCount ?? 0) < this.mainNewsCount + this.maxPerPage)
                ? this.otherNewsPage.length
                : ((meta?.limitable?.rowAllCount ?? 0) - this.mainNewsCount);
            this.articleIds = this.articles.map(({ id }) => id as string);
          }

          this.currentPage = queryParam?.['page'] ? queryParam['page'] - 1 : 0;
          this.fetchCurrentPage(this.currentPage);

          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((): void => {
        this.setMetaData();

        this.changeRef.detectChanges();
      });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.changeRef.detectChanges();
  }

  onPageChange(pageNumber: number): void {
    this.currentPage = pageNumber - 1; // Convert 1-based pager page to 0-based internal page
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: pageNumber }, // URL uses 1-based page numbers
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    if (!this.articles?.length) {
      return;
    }

    const metaData: IMetaData & { ogImage?: string } = {
      ...defaultMetaInfo(this.portalConfig),
      title: `${this.portalConfig?.portalName} - ${this.title}`,
      description: this.description || this.portalConfig.portalSubtitle,
      ogTitle: this.title,
    };
    if (this.thumbnail) {
      (metaData.ogImage as string) = this.thumbnail;
    }

    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage(`dosszie`, this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  private fetchCurrentPage(page: number): void {
    // Don't fetch on page 0 - use the data from resolver
    if (page === 0) {
      return;
    }

    // page parameter is already 0-based from the calling context
    const currentPage = page || 0;

    // For pagination, we need to calculate the correct page for the API
    // The first page (page 0) shows articles from the resolver (positions 15-22)
    // Page 1 should show articles 23-30, page 2 should show 31-38, etc.
    // So we need to add an offset to account for the main news articles
    const pagesFromMainNews = Math.floor(this.mainNewsCount / this.maxPerPage);
    const apiPage = pagesFromMainNews + currentPage;

    this.dossierService
      .getDossierArticles(this.slug, this.maxPerPage, apiPage)
      .pipe(takeUntil(this.unsubscribe$))
      .pipe(
        map(({ data, meta }) => ({
          data: (data || []).map(dossierToArticleCard),
          meta,
        }))
      )
      .subscribe(({ data: articles, meta }) => {
        if (this.redirectService.shouldBeRedirect(currentPage, articles)) {
          this.redirectService.redirectOldUrl(`dosszie/${this.slug}`, false, 302);
        }
        this.otherNewsPage = articles;
        this.maxCount = (meta?.limitable?.rowAllCount ?? 0) - this.mainNewsCount;
        if (this.articleList && this.utilsService.isBrowser() && page) {
          setTimeout(() => {
            this.scrollToFirstArticle();
          }, 50);
        }
        this.changeRef.detectChanges();
      });
  }

  private scrollToFirstArticle(): void {
    const offsetMargin = 50;
    window.scrollTo(0, (this.articleList.nativeElement as HTMLElement)?.offsetTop - offsetMargin);
  }
}
