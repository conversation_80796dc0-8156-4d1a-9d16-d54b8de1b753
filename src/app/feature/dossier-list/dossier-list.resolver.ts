import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, ArticleCard, RedirectService } from '@trendency/kesma-ui';
import { DossierService } from '../../shared';
import { DOSSIER_MAIN_NEWS_COUNT, DOSSIER_OTHER_NEWS_PER_PAGE } from './dossier-list.definitions';
import { dossierToArticleCard } from './dossier-list.utils';

@Injectable({
  providedIn: 'root',
})
export class DossierListResolver {
  constructor(
    private readonly dossierService: DossierService,
    private readonly redirectService: RedirectService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    const { dossierSlug } = route.params;
    const { page } = route.queryParams;
    const currentPage = page ? parseInt(page, 10) - 1 : 0;

    return this.dossierService.getDossierArticles(dossierSlug, DOSSIER_MAIN_NEWS_COUNT + DOSSIER_OTHER_NEWS_PER_PAGE, currentPage).pipe(
      map(({ data, meta }) => ({
        data: (data || []).map(dossierToArticleCard),
        meta,
      })),
      tap(({ data }) => {
        if (this.redirectService.shouldBeRedirect(currentPage, data)) {
          this.redirectService.redirectOldUrl(`dosszie/${dossierSlug}`, false, 302);
        }
      }),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(() => error);
      })
    );
  }
}
