@use 'shared' as *;

section.dossier-list {
  .wrapper {
    > .left-column {
      .top-section {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .short-lead {
          font-size: 20px;
          line-height: 21px;
          position: relative;
        }

        .time {
          color: $grey-7;
          font-size: 14px;
          line-height: 21px;
        }
      }

      h1 {
        font-weight: 500;
        font-size: 60px;
        line-height: 70px;
        margin-bottom: 30px;
        @include media-breakpoint-down(md) {
          font-size: 30px;
          line-height: 40px;
        }
      }

      .dossier-header {
        font-size: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 15px 0 15px 20px;
        margin-bottom: 50px;
        margin-top: -5px;
        text-transform: uppercase;
        font-weight: 500;
        @include article-before-line();

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }
      }

      .lead {
        font-weight: normal;
        font-size: 30px;
        line-height: 50px;
        margin-bottom: $block-bottom-margin;
        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
          font-size: 20px;
          line-height: 30px;
        }
      }

      app-social-row {
        margin-bottom: calc(#{$block-bottom-margin} - 15px);

        @include media-breakpoint-down(md) {
          margin-bottom: calc(#{$block-bottom-margin-mobile} - 15px);
        }
      }

      .block-voting {
        width: 680px;
        max-width: 100%;
        margin: 0 auto $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin: 0 auto $block-bottom-margin-mobile;
        }
      }

      .block-big-image {
        width: 100%;
        @include imgRatio(16%, 9%);
        margin-bottom: $block-bottom-margin;
        @include imgZoom();

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }
      }

      .block-recommendation {
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }

        @include media-breakpoint-down(sm) {
          width: calc(100% + 30px);
          margin-left: -15px;
          margin-right: -15px;

          app-article-card .article-block[class] {
            border-radius: 0;
            border-left-width: 0;
            border-right-width: 0;
            padding-left: 0;
            padding-right: 15px;
          }
        }
      }

      .block-content {
        > * {
          margin-bottom: $block-bottom-margin;

          @include media-breakpoint-down(md) {
            margin-bottom: $block-bottom-margin-mobile;
          }

          @include media-breakpoint-down(sm) {
            margin-bottom: 32px;
          }
        }

        p,
        li,
        span {
          font-size: 20px;
          line-height: 40px;
        }

        img {
          margin-bottom: 48px;
        }

        a {
          color: $blue;
          font-weight: bold;
          text-decoration: underline;
        }

        strong,
        b {
          font-weight: bold;
        }

        i,
        em {
          font-style: italic;
        }

        img {
          margin: 0 auto;
          max-width: 100%;
          height: auto;
          display: block;
        }

        ol,
        ul {
          li {
            padding-left: 15px;
            position: relative;
            margin-bottom: 20px;

            &:before {
              width: 6px;
              height: 6px;
              background: $grey-7;
              position: absolute;
              left: 0;
              top: 18px;
              content: ' ';
              display: block;
              opacity: 0.5;
            }
          }
        }

        .table-scroller {
          width: 100%;
          overflow: auto;
        }

        table {
          width: 100%;
          border: 0;

          tr {
            td {
              background: $grey-25;
              border: 0;
              border-bottom: 2px solid $white;
              font-size: 16px;
              line-height: 130%;
              padding: 22px 15px;
              @include media-breakpoint-down(md) {
                padding: 10px 15px;
              }
            }
          }
        }
      }

      .block-big-image {
        background-size: cover;
        background-position: center;
        @include media-breakpoint-down(md) {
          margin-left: -15px;
          margin-right: -15px;
          width: calc(100% + 30px);
        }
      }

      app-quiz,
      app-dossier-recommendation,
      app-tag-row {
        display: block;
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }
      }

      .minute-by-minute-list {
        app-minute-by-minute {
          display: block;
          margin-bottom: 30px;
        }
      }

      app-section-header {
        .section-header {
          margin-bottom: 0;
        }
      }

      .block-show-comments {
        display: block;
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }

        .show-comments-btn {
          display: block;
          width: 100%;
          height: 50px;
        }
      }

      .cards-container {
        padding-top: 30px;
        display: flex;
        margin-bottom: $block-bottom-margin;
        flex-wrap: wrap;
        width: calc(100% + 30px);
        margin-left: -15px;
        margin-right: -15px;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }

        &.main-cards {
          app-article-card {
            .article-block {
              .article-bottom {
                .article-sub-title {
                  // padding-left: 24px;

                  &:before {
                    content: ' ';
                    display: inline-block;
                    @include icon('icons/dossier.svg');
                    width: 16px;
                    height: 12px;
                    margin-right: 8px;
                    position: relative;
                  }
                }
              }
            }
          }
        }
      }

      .block-comments {
        display: block;
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }
      }
    }

    > aside {
      .cards-container {
        display: block;
      }
    }
  }
}
