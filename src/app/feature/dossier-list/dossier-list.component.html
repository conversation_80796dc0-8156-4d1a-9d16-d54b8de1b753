<section class="dossier-list">
  <div class="wrapper with-aside" *ngIf="articles?.length">
    <article class="left-column">
      <div class="dossier-header">Dosszié</div>

      <h1 class="dossier-title">{{ title }}</h1>

      <p class="lead">
        {{ description || 'Dosszié tartalma' }}
      </p>

      <ng-container *ngFor="let section of articleSections; let i = index">
        <div class="cards-container main-cards">
          <div class="col-12 col-md-6" *ngFor="let article of section">
            <app-article-card [styleID]="articleCardType.STYLE5" [article]="article" [isTagVisible]="true"> </app-article-card>
          </div>
        </div>

        <ng-container *ngIf="i === 0">
          <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_1 as ad" [ad]="ad"> </kesma-advertisement-adocean>
          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        </ng-container>

        <ng-container *ngIf="i === 1">
          <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        </ng-container>

        <ng-container *ngIf="i === 2">
          <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        </ng-container>
      </ng-container>

      <!-- cikk ajánló blokk -->
      <ng-container *ngIf="otherNewsPage.length">
        <div #articleList>
          <app-section-header sectionTitle="Dosszié további cikkei"></app-section-header>
        </div>
        <div class="cards-container">
          <div class="col-12" *ngFor="let article of otherNewsPage" [@fadeInOutAnimation]>
            <app-article-card
              [styleID]="articleCardType.STYLE6"
              [article]="article"
              [isTagVisible]="true"
              [isSubTitleVisible]="true"
              [isLeadVisible]="true"
            ></app-article-card>
          </div>
        </div>
        <app-pager
          [currentPage]="currentPage"
          [rowAllCount]="maxCount"
          [rowOnPageCount]="maxPerPage"
          [allowAutoScrollToTop]="false"
          (pageChange)="onPageChange($event)"
          *ngIf="maxCount > maxPerPage"
        ></app-pager>
      </ng-container>
    </article>

    <aside>
      <app-sidebar [excludedIds]="articleIds"></app-sidebar>
    </aside>
  </div>
</section>
