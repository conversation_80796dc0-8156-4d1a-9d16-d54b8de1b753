import { format } from 'date-fns';
import { ArticleCard, DossierArticle } from '@trendency/kesma-ui';
import { truncateToUtc } from '@trendency/kesma-core';

export const dossierToArticleCard = (rec: DossierArticle): ArticleCard => {
  const { slug, title, preTitle, publishDate, thumbnail, lead, length, columnTitle, columnSlug, thumbnailFocusedImages } = rec;
  const utcDate = format(truncateToUtc(publishDate) ?? new Date(), 'yyyy-MM-dd');
  const [publishYear, publishMonth] = utcDate.split('-');
  return {
    id: undefined,
    title,
    preTitle,
    slug,
    publishDate,
    publishMonth,
    publishYear,
    thumbnail: thumbnail
      ? {
          url: thumbnail,
        }
      : undefined,
    category: {
      name: columnTitle,
      slug: columnSlug,
    },
    readingTime: length?.toString(),
    columnTitle,
    columnSlug,
    lead,
    label: {
      text: columnTitle,
    },
    thumbnailFocusedImages,
  };
};
