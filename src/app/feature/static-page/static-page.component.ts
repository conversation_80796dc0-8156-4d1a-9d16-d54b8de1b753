import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AnalyticsService, CustomStaticPageType, LayoutApiData } from '@trendency/kesma-ui';
import { BypassPipe, EmbeddingService, RunScriptsDirective, SeoService } from '@trendency/kesma-core';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgFor, NgIf, NgSwitch } from '@angular/common';
import { defaultMetaInfo, PortalConfigService } from '../../shared';
import { IComponentData, StaticPageResponse } from './static-page.definitions';

@Component({
  selector: 'app-static-page',
  templateUrl: './static-page.component.html',
  styleUrls: ['./static-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, <PERSON><PERSON><PERSON>, NgSwitch, LayoutComponent, BypassPipe, RunScriptsDirective],
})
export class StaticPageComponent implements OnInit, AfterViewInit {
  title: string;
  staticPageResponse: StaticPageResponse;
  body: IComponentData[];
  customStaticPageType: CustomStaticPageType;
  layoutApiData: LayoutApiData;
  adPageType = 'custom_built_page';

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly route: ActivatedRoute,
    private readonly embedding: EmbeddingService,
    private readonly seo: SeoService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly analyticsService: AnalyticsService
  ) {}

  ngOnInit(): void {
    this.route.data.subscribe((res) => {
      this.customStaticPageType = res['staticPageData']?.meta?.customStaticPageType || CustomStaticPageType.StaticPage;
      switch (this.customStaticPageType) {
        case CustomStaticPageType.StaticPage:
          this.staticPageResponse = res['staticPageData'].data;
          this.title = this.staticPageResponse.title;
          this.body = this.staticPageResponse.body;
          this.setMetaData();
          this.analyticsService.sendPageView(undefined, 'Statikus oldal');
          break;
        case CustomStaticPageType.CustomPage:
          this.title = res['staticPageData'].meta.customBuiltPageTitle;
          this.layoutApiData = res['staticPageData'].data;
          this.setMetaData();
          this.analyticsService.sendPageView(undefined, 'Egyedi oldal');
          break;
      }
      this.changeRef.detectChanges();
    });
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
  }

  setMetaData(): void {
    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      ogTitle: `${this.portalConfig?.portalName} - ${this.title}`,
    });
  }
}
