<section class="block static-page">
  <div *ngIf="!customStaticPageType || customStaticPageType === 'staticPage'" class="wrapper">
    <div class="static-page-container">
      <h1 class="static-page-title">{{ title }}</h1>
      <ng-container *ngFor="let element of body">
        <ng-container [ngSwitch]="element.type">
          <div *ngIf="element" class="article-text-formatter">
            <div *ngFor="let item of element?.details" [innerHTML]="item?.value | bypass: 'html'" trRunScripts></div>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
  <div *ngIf="customStaticPageType === 'customBuiltPage'" class="wrapper">
    <div class="static-page-container">
      <app-layout [adPageType]="adPageType" [configuration]="layoutApiData.content" [structure]="layoutApiData.struct"></app-layout>
    </div>
  </div>
</section>
