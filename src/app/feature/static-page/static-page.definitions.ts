export interface StaticPageResponse {
  body: IComponentData[];
  id: string;
  publishDate: IDate;
  slug: string;
  title: string;
  type: string;
}
export type IDate = Readonly<{
  date: string;
}>;

export enum CustomStaticPageType {
  StaticPage = 'staticPage',
  CustomPage = 'customBuiltPage',
}

export type IComponentFieldData = Readonly<{
  type: string;
  inputType: string;
  key: string;
  uuid: string;
  value: SubsequentDossierValue;
  multiple?: boolean;
  properties?: any;
  valueDetails?: any;
}>;

export type SubsequentDossierValue = Readonly<{
  coverImage: string;
  headerColor: string;
  id: string;
  isActive: boolean;
  isDeleted: string;
  slug: string;
  relatedArticles: DossierRelatedArticles[];
  title: string;
}>;

export type DossierRelatedArticles = Readonly<{
  columnId: string;
  columnParentId?: string;
  columnParentSlug?: string;
  columnParentTitle?: string;
  columnSlug: string;
  columnTitle: string;
  publishDate: string;
  slug: string;
  id: string;
  title: string;
}>;

export type IComponentData = Readonly<{
  type: string;
  uuid: string;
  key: string;
  subComponents: IComponentData[];
  details: IComponentFieldData[];
  autoplay?: boolean;
  adId?: number;
  cssClass?: string;
}>;
