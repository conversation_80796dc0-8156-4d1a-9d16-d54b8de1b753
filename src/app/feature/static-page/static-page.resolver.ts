import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Response } from 'express';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ArticleService } from '../article/article.service';
import { StaticPageService } from './static-page.service';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { ApiService } from '../../shared';

@Injectable()
export class StaticPageResolver {
  private isStaticPage = false;

  constructor(
    private readonly staticPageService: StaticPageService,
    private readonly articleService: ArticleService,
    private readonly router: Router,
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly apiService: ApiService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const previewHash = route.queryParams['previewHash'];
    const param = route.params['slug'];
    const request$ = previewHash
      ? this.staticPageService.getStaticPagePreview(param, previewHash)
      : (param ? this.apiService.getArticlesByFoundationTag(param) : of({})).pipe(
          switchMap((searchResult: any) => {
            if (param && (searchResult?.data ?? []).length === 0) {
              return this.redirectStaticPageUrls().pipe(
                switchMap(() => {
                  if (this.isStaticPage) {
                    return this.staticPageService.getStaticPage(param).pipe(
                      catchError((error) => {
                        this.router.navigate(['/', '404'], {
                          state: { errorResponse: JSON.stringify(error) },
                          skipLocationChange: true,
                        });
                        return throwError(error);
                      })
                    );
                  }

                  return of(null);
                })
              );
            }
            return of(searchResult);
          })
        );
    return request$.pipe(
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }

  private redirectStaticPageUrls(): Observable<any> {
    const currentUrl = this.seoService.currentUrl;
    return this.articleService.getArticleRedirect(encodeURIComponent(currentUrl)).pipe(
      map(({ url }) => {
        if (url && this.utilsService.isBrowser()) {
          window.location.href = url.replace('localhost', `localhost:4200`);
        } else if (url && this.response) {
          this.response.status(301);
          // port is missing from the response and `process.env.PORT` reflects
          // the devserver's port when running w/ local devserver -> replacing w/ the predefined
          if (url.match(/^https?:\/\/localhost\//)) {
            url = url.replace(/^https?:\/\/localhost/, environment.siteUrl ?? '');
          }
          this.response.setHeader('location', url);
        } else if (url === null) {
          this.isStaticPage = true;
        }
        return of({});
      })
    );
  }
}
