import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class StaticPageService {
  constructor(private readonly reqService: ReqService) {}

  getStaticPage(slug: string): Observable<unknown> {
    return this.reqService.get(`/custom-static-page-by-slug/${slug}`);
  }

  getStaticPagePreview(slug: string, previewHash: string): Observable<unknown> {
    const params = new HttpParams().append('previewHash', previewHash);
    return this.reqService.get(`/content-page/static-page/${slug}/preview/view`, { params });
  }
}
