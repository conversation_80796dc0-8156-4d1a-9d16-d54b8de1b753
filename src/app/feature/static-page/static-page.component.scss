@use 'shared' as *;

.static-page {
  .wrapper {
    margin-top: 25px;
    padding-bottom: 45px;

    .static-page-container {
      .static-page-title {
        font-size: 60px;
        font-weight: 700;
        line-height: 74px;
        font-family: $font-secondary;
        margin-bottom: 40px;

        @include media-breakpoint-down(sm) {
          font-size: 30px;
          font-weight: 700;
          line-height: 40px;
        }
      }
    }
  }
}

.article-text-formatter {
  p,
  li,
  td {
    font-size: 14px;
    font-weight: 400;
    font-style: normal;
    line-height: 24px;
    text-align: left;
    margin: 1.2rem 0;

    a {
      color: #1877f2;
      font-weight: 500;
    }
  }

  ul {
    list-style-position: inside;
    list-style-type: disc;
  }

  ol {
    list-style: decimal;
  }

  ul,
  ol {
    padding-left: 18px;
    margin: 1.2rem 0;

    li {
      padding-left: 0;
      display: list-item;
      margin: 0.2rem 0;
    }
  }

  table {
    margin: auto;

    td {
      padding: 12px 15px;
      border: 1px solid #c4c4c4;
      font-size: 14px;
      color: #586771;
    }
  }

  .article-text-table {
    display: block;
    overflow: auto;
    margin: 35px 0;

    table {
      width: calc(100% - 2px);

      tr {
        &:first-child {
          td {
            border: 0;
          }
        }

        td {
          padding: 12px 15px;
          border: 1px solid #c4c4c4;
          font-size: 14px;
          color: #586771;
        }
      }
    }
  }

  figure.image {
    display: block;
    max-width: 100%;
    width: 100vw;

    img {
      width: 100%;
      height: auto;
    }

    figcaption {
      text-align: center;
      opacity: 0.5;
      font-style: italic;
      padding-top: 5px;
      font-size: 13px;
    }
  }

  p {
    font-size: 18px;
    line-height: 32px;
    font-weight: 400;
    margin: 0.7rem 0;
    @include media-breakpoint-down(md) {
      margin: 26px 0;
    }
  }

  .quote {
    margin: 40px 0;

    p {
      color: $red;
      font-family: $font-secondary;
      font-style: italic;
      letter-spacing: normal;
      font-size: 26px !important;
      line-height: 44px;

      &:before {
        content: '\201E';
      }

      &:after {
        content: '\201D';
      }
    }

    @include media-breakpoint-down(md) {
      margin: 26px 0;
    }
  }

  .highlight {
    margin: 40px 0px;
    display: block;
    position: relative;
    margin-block-start: 1em;
    margin-block-end: 1em;
    border-left: 8px solid $blue;
    padding-top: 3px;
    padding-bottom: 3px;

    @include media-breakpoint-down(md) {
      margin-top: 26px;
      margin-bottom: 26px;
    }

    p {
      font-family: $font-secondary;
      position: relative;
      display: inline;
      width: auto;
      line-height: 1;
      color: $white;
      white-space: pre-wrap;

      border-bottom: 0 solid $blue;
      border-top: 0 solid $blue;
      border-width: 0.2em 0px;
      background-color: $blue;
      padding-left: 4px;
      padding-right: 4px;
      font-size: 24px;
      font-weight: 400;
      line-height: 36px;
    }
  }

  .border-text {
    margin: 40px 0;
    padding: 40px;
    border-radius: 3px;
    border: 1px solid $white-6;
    background-color: $white;
    @include media-breakpoint-down(md) {
      margin: 26px 0;
    }

    h2 {
      font-size: 25px;
      font-weight: 400;
      font-family: $font-secondary;
      margin-bottom: 25px;
    }

    p {
      font-size: 16px;
      font-weight: 300;
      line-height: 26px;
    }
  }

  .raw-html-embed {
    width: 100%;

    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
    display: block;

    > * {
      margin: 0 auto;
    }
  }
}

.video-container {
  position: relative;
  padding-bottom: 47%;
  padding-top: 30px;
  height: 0;
  margin-top: 20px;
  margin-bottom: 20px;
}

.video-container iframe,
.video-container object,
.video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
