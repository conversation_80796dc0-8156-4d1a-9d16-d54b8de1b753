import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-layout-preview',
  templateUrl: './layout-preview.component.html',
  styleUrls: ['./layout-preview.component.scss'],
  imports: [NgIf, LayoutComponent],
})
export class LayoutPreviewComponent implements OnInit {
  layoutApiData: {
    struct: any[];
    content: any[];
  };
  adPageType: string;

  constructor(private readonly route: ActivatedRoute) {}

  ngOnInit(): void {
    this.layoutApiData = this.route.snapshot.data['layoutData'];
    const pageType = this.route.snapshot.queryParams['pageType'];
    const column = this.route.snapshot.queryParams['column'];
    if (pageType === 'home' || pageType === 'homepage') {
      this.adPageType = 'main_page';
    } else if (pageType === 'column' && column) {
      this.adPageType = `column_${column}`;
    } else if (pageType === 'opinion') {
      this.adPageType = 'opinion';
    } else if (pageType === 'custombuiltpage') {
      this.adPageType = 'all_articles_and_sub_pages';
    }
  }
}
