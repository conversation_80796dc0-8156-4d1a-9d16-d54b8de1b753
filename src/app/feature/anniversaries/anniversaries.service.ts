import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { format, parseISO } from 'date-fns';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { PortalUtilsService } from '../../shared';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { Anniversary, BackendAnniversary } from './anniversaries.definitions';
import { mapBackendAnniversariesToAnniversaries } from './anniversaries.utils';

@Injectable({
  providedIn: 'root',
})
export class AnniversariesService {
  constructor(
    private readonly reqService: ReqService,
    private readonly utilsService: PortalUtilsService
  ) {}

  getAnniversaries(params?: Record<string, string>): Observable<ApiResult<Anniversary[], ApiResponseMetaList>> {
    const options: IHttpOptions = { params };

    return this.reqService.get<ApiResult<BackendAnniversary[], ApiResponseMetaList>>('/mediaworks/on-this-day', options).pipe(
      map((r: ApiResult<BackendAnniversary[], ApiResponseMetaList>) => ({
        ...r,
        data: r.data.map((a) => mapBackendAnniversariesToAnniversaries(a)),
      }))
    );
  }

  getAnniversariesByDate(date?: string | Date, maxPerPage = 10, pageLimit = 0): Observable<ApiResult<Anniversary[], ApiResponseMetaList>> {
    const params: Record<string, string> = {
      rowCount_limit: maxPerPage.toString(),
      page_limit: pageLimit.toString(),
    };
    if (date) {
      params['date'] = format(typeof date === 'string' ? parseISO(date) : date, 'MM-dd');
    }

    return this.getAnniversaries(params);
  }

  getTodayAnniversaries(maxPerPage?: number, page?: number): Observable<ApiResult<Anniversary[], ApiResponseMetaList>> {
    return this.getAnniversariesByDate(this.utilsService.now(), maxPerPage, page);
  }
}
