<section class="anniversaries">
  <div class="wrapper mobile-header-wrapper mobile-only">
    <div class="page-title">
      <span class="main-title">MAI ÉVFORDULÓK</span>
    </div>
    <div class="desc">
      {{ filterForm.controls['selectedDay'].value | dfnsParseIso | dfnsFormat: 'MMMM d.' | titlecase }}
      ezen a napon
    </div>
    <div class="mobile-calendar">
      <app-date-time-picker
        [id]="'anniversariesCalendarMobile'"
        controlName="selectedDay"
        [formGroup]="filterForm"
        [enableTime]="false"
        [valueFormat]="dateValueFormat"
        [inline]="true"
      ></app-date-time-picker>
    </div>
  </div>

  <div class="wrapper standard-wrapper with-aside">
    <div class="left-column">
      <div class="page-title desktop-only">
        <div class="left">
          <span class="main-title">MAI ÉVFORDULÓK</span>
          {{ filterForm.controls['selectedDay'].value | dfnsParseIso | dfnsFormat: 'MMMM d.' | titlecase }} ezen a napon
        </div>
      </div>

      <div class="results">
        <ng-container *ngFor="let anniversary of anniversaries">
          <div class="anniversaries-card">
            <div class="left">
              <a class="title"> {{ anniversary.title }} </a>
              <p class="lead" [innerHTML]="anniversary.content || anniversary.description"></p>
            </div>
            <div class="right">
              <div class="date-sign">
                <div class="year-ago">
                  <div class="poser">
                    <strong>{{ anniversary.yearsAgo }}</strong> éve
                  </div>
                </div>
                <div class="year">{{ anniversary.date | dfnsFormat: 'yyyy' }}</div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
      <app-pager [currentPage]="currentPage" [rowAllCount]="maxCount" [rowOnPageCount]="maxPerPage" (pageChange)="onPageChange($event)"></app-pager>
    </div>
    <aside>
      <div class="calendar desktop-only">
        <app-date-time-picker
          [id]="'anniversariesCalendarDesktop'"
          controlName="selectedDay"
          [formGroup]="filterForm"
          [enableTime]="false"
          [valueFormat]="dateValueFormat"
          [inline]="true"
        ></app-date-time-picker>
      </div>
      <app-sidebar columnSlug="ezen-a-napon"></app-sidebar>

      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.box_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
    </aside>
  </div>
</section>
