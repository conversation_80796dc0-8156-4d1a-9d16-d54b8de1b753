@use 'shared' as *;

section.anniversaries {
  .mobile-only {
    @include media-breakpoint-up(lg) {
      display: none !important;
    }
  }

  .desktop-only {
    @include media-breakpoint-down(md) {
      display: none !important;
    }
  }

  .page-title {
    font-size: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 20px;
    margin-bottom: 30px;
    @include article-before-line();

    &:before {
      background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
    }

    .main-title {
      text-transform: uppercase;
      display: inline-block;
      margin-right: 9px;
      font-weight: 500;
    }
  }

  .wrapper.mobile-header-wrapper {
    .page-title {
      margin-top: 30px;
      margin-bottom: 10px;
      padding: 15px;

      .main-title {
        font-size: 20px;
        line-height: 30px;
      }
    }

    .desc {
      font-weight: 300;
      font-size: 20px;
      line-height: 30px;
    }
  }

  .wrapper.standard-wrapper {
    position: relative;

    .left-column {
      padding-bottom: 50px;
      width: calc(100% - #{$aside-width} - 40px);

      @include media-breakpoint-down(md) {
        width: 100%;
      }

      .anniversaries-card {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-bottom: 60px;

        .left {
          width: calc(100% - 97px - 20px);
          @include media-breakpoint-down(md) {
            width: calc(100% - 69px - 20px);
          }

          .title {
            font-weight: 500;
            font-size: 30px;
            line-height: 40px;
            margin-bottom: 20px;
            display: block;
            @include media-breakpoint-down(md) {
              font-size: 20px;
              line-height: 30px;
            }
          }

          .lead {
            font-weight: normal;
            font-size: 20px;
            line-height: 40px;
            @include media-breakpoint-down(md) {
              font-size: 16px;
              line-height: 24px;
            }
          }
        }

        .right {
          width: 97px;
          @include media-breakpoint-down(md) {
            width: 69px;
          }

          .date-sign {
            width: 95px;
            border-radius: 6px;
            border: 1px solid $primary-color;
            @include media-breakpoint-down(md) {
              width: 66px;
            }

            .year-ago {
              color: $white;
              text-transform: uppercase;
              height: 95px;
              background: linear-gradient(180deg, $secondary-color 0%, $primary-color 100%);
              display: flex;
              align-items: center;
              justify-self: center;
              flex-wrap: wrap;
              text-align: center;
              border-top-left-radius: 6px;
              border-top-right-radius: 6px;
              @include media-breakpoint-down(md) {
                height: 66px;
              }

              .poser {
                display: block;
                text-align: center;
                width: 100%;
                font-weight: bold;
                font-size: 14px;
                line-height: 100%;

                strong {
                  display: block;
                  text-align: center;
                  font-weight: 500;
                  font-size: 50px;
                  line-height: 40px;
                  width: 100%;
                  margin-bottom: 5px;
                  @include media-breakpoint-down(md) {
                    font-size: 40px;
                    line-height: 30px;
                  }
                }
              }
            }

            .year {
              color: $primary-color;
              display: block;
              text-align: center;
              font-weight: 500;
              font-size: 16px;
              line-height: 40px;
            }
          }
        }
      }

      app-pager {
        display: flex;
        justify-content: center;
      }
    }

    > aside {
      width: $aside-width;

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }
  }

  .calendar {
    background: $white-4;
    text-align: center;
    padding: 20px 10px;

    app-date-time-picker {
      display: inline-block;
    }
  }

  .mobile-calendar {
    margin-top: 20px;
  }
}
