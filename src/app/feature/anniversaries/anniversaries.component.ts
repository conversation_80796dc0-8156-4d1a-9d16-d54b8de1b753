import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  LimitableMeta,
  RedirectService,
} from '@trendency/kesma-ui';
import { format, parseISO } from 'date-fns';
import { sortBy } from 'lodash-es';
import { Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { Anniversary } from './anniversaries.definitions';
import { AnniversariesService } from './anniversaries.service';
import { SeoService } from '@trendency/kesma-core';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { NgFor, NgIf, TitleCasePipe } from '@angular/common';
import { DateTimePickerComponent, defaultMetaInfo, PagerComponent, PortalConfigService } from '../../shared';
import { DateFnsModule } from 'ngx-date-fns';
import { FormControl, FormGroup } from '@angular/forms';

const MAX_RESULTS_PER_PAGE = 8;

@Component({
  selector: 'app-anniversaries',
  templateUrl: './anniversaries.component.html',
  styleUrls: ['./anniversaries.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [NgFor, PagerComponent, SidebarComponent, NgIf, AdvertisementAdoceanComponent, TitleCasePipe, DateFnsModule, DateTimePickerComponent],
})
export class AnniversariesComponent implements OnInit, OnDestroy {
  dateValueFormat: string = `yyyy-MM-dd`;
  filterForm: FormGroup = new FormGroup({
    selectedDay: new FormControl(format(new Date(), this.dateValueFormat)),
  });
  anniversaries: Anniversary[] = [];
  maxCount = 0;
  maxPerPage = MAX_RESULTS_PER_PAGE;
  currentPage = 0;
  adverts: AdvertisementsByMedium;
  private readonly destroySubject: Subject<boolean> = new Subject<boolean>();

  constructor(
    private readonly anniversariesService: AnniversariesService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly portalConfig: PortalConfigService,
    private readonly seo: SeoService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly redirectService: RedirectService
  ) {}

  ngOnInit(): void {
    this.route.queryParams
      .pipe(
        switchMap((): Observable<Advertisement[]> => {
          this.loadData();

          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.destroySubject)
      )
      .subscribe((ads) => {
        this.adverts = ads;

        this.changeRef.detectChanges();
      });
    this.loadData();
    this.onDaySelected();

    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      ogTitle: `${this.portalConfig?.portalName} - Évfordulók`,
    });
  }

  ngOnDestroy(): void {
    this.destroySubject.next(true);
    this.destroySubject.complete();
  }

  onDaySelected(): void {
    this.filterForm
      .get('selectedDay')
      ?.valueChanges.pipe(takeUntil(this.destroySubject))
      .subscribe(() => {
        this.router.navigate([], {
          queryParams: {
            ...this.route.snapshot.queryParams,
            page: 1,
            date: format(this.filterForm.controls['selectedDay'].value, 'yyyy-MM-dd'),
          },
        });
      });
  }

  onPageChange(newPage: number): Promise<boolean> {
    this.currentPage = newPage - 1;
    return this.router.navigate([], { queryParams: { ...this.route.snapshot.queryParams, page: newPage } });
  }

  private setAnniversaryData(anniversaries: Anniversary[], pagination: LimitableMeta): void {
    this.anniversaries = sortBy(anniversaries, 'yearsAgo');
    this.currentPage = pagination.pageCurrent ?? 0;
    this.maxCount = pagination.rowAllCount ?? 0;
  }

  private loadData(): void {
    const { date, page } = this.route.snapshot.queryParams;
    this.currentPage = (page ?? 1) - 1;
    date ? this.loadSpecificDateData(date) : this.loadTodayData();
  }

  private loadTodayData(): void {
    this.anniversariesService
      .getTodayAnniversaries(this.maxPerPage, this.currentPage)
      .pipe(takeUntil(this.destroySubject))
      .subscribe(({ data: anniversaries, meta: { limitable: pagination } }) => {
        if (this.redirectService.shouldBeRedirect(this.currentPage, anniversaries)) {
          this.redirectService.redirectOldUrl('evfordulok', false, 302);
        }
        this.setAnniversaryData(anniversaries, pagination);
        this.changeRef.detectChanges();
      });
  }

  private loadSpecificDateData(queryDate: string): Promise<boolean> | undefined {
    let selectedDate;
    try {
      selectedDate = format(parseISO(queryDate), this.dateValueFormat); // but format throws error on invalid date
      this.filterForm.get('selectedDay')?.patchValue(selectedDate);
    } catch (e) {
      // check for query parameter date -> if it's invalid redirect to see today
      return this.router.navigate([], { queryParams: {} });
    }

    this.anniversariesService
      .getAnniversariesByDate(selectedDate, this.maxPerPage, this.currentPage)
      .pipe(takeUntil(this.destroySubject))
      .subscribe(({ data: anniversaries, meta: { limitable: pagination } }) => {
        this.setAnniversaryData(anniversaries, pagination);
        this.changeRef.detectChanges();
      });

    return;
  }
}
