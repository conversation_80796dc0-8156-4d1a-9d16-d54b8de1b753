import { ProgramRecommendationItem, ProgramSelects, ProgramTypes } from '../../shared';
import { ArticleCard, Meta } from '@trendency/kesma-ui';

export interface ProgramTypesResult extends MetaResult {
  data: ProgramTypes[];
}

export interface ProgramSelectsResult extends MetaResult {
  data: ProgramSelects[];
}

export interface RouteDataProgramsMapped {
  programTypes: ProgramTypes[];
  selectTypes: ProgramSelects[];
  recommendedArticles: ArticleCard[];
  programRecommendations: ProgramRecommendationsResult;
}

export interface RouteDataPrograms {
  pageData: RouteDataProgramsMapped;
}

interface MetaResult {
  meta: Meta;
}

export interface ProgramRecommendationsResult extends MetaResult {
  data: ProgramRecommendationItem[];
}
