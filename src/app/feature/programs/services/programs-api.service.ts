import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import { ProgramDetail, ProgramDetailResult, ProgramQueryParams, ProgramRecommendations, ProgramSelects, ProgramTypes } from '../../../shared';
import { ProgramRecommendationsResult, ProgramSelectsResult, ProgramTypesResult } from '../programs.definitions';
import { ArticleCard, ArticleResponse } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class ProgramsApiService {
  constructor(private readonly reqService: ReqService) {}

  getProgramTypes(): Observable<ProgramTypes[]> {
    return this.reqService.get<ProgramTypesResult>('program/program-types').pipe(map((data: ProgramTypesResult) => data.data));
  }

  getProgramSelectTypes(): Observable<ProgramSelects[]> {
    return this.reqService
      .get<ProgramSelectsResult>('source/program/program-recommendation/program_types')
      .pipe(map((data: ProgramSelectsResult) => data.data));
  }

  getProgramRecommendations(queryParams: ProgramQueryParams, programLimit = 10): Observable<ProgramRecommendationsResult> {
    const requestQueryParams: ProgramQueryParams = {
      ...queryParams,
      rowCount_limit: String(programLimit),
    };

    return this.reqService.get<ProgramRecommendationsResult>('program/program-recommendation-search', {
      params: requestQueryParams,
    });
  }

  getHighlightedProgramRecommendations(programLimit = 3): Observable<ProgramRecommendationsResult> {
    return this.reqService.get<ProgramRecommendationsResult>('program/very-recommended-ones', {
      params: { rowCount_limit: String(programLimit), page_limit: '0' },
    });
  }

  getProgramRecommendedArticles(): Observable<ArticleCard[]> {
    return this.reqService.get<ArticleResponse>('content-page/articles-by-tag?tagSlug=programok').pipe(map((data: ArticleResponse) => data.data));
  }

  getProgramCategory(categorySlug: string): Observable<ProgramRecommendations[]> {
    return this.reqService.get<ProgramRecommendations[]>(`program/program-recommendation-search`, {
      params: {
        program_type: categorySlug,
      },
    });
  }

  getProgramDetail(programSlug: string): Observable<ProgramDetail> {
    return this.reqService.get<ProgramDetailResult>(`program/program-recommendation/${programSlug}`).pipe(map((data: ProgramDetailResult) => data.data));
  }
}
