import { Injectable } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { format } from 'date-fns';
import { ProgramQueryParams, ProgramTypes } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class ProgramsSearchService {
  constructor(private readonly router: Router) {}

  initSearchForm(queryParams: ProgramQueryParams, availableTypes: ProgramTypes[]): UntypedFormGroup {
    const searchForm = new UntypedFormGroup({
      global_filter: new UntypedFormControl(queryParams.global_filter || ''),
      date_from: new UntypedFormControl(queryParams.date_from ? queryParams.date_from : ''),
      date_until: new UntypedFormControl(queryParams.date_until ? queryParams.date_until : ''),
      program_type: new UntypedFormControl(this.fillTypes(availableTypes, this.toArray(queryParams['program_type[]']))),
    });
    return searchForm;
  }

  navigateByQuery(searchForm: UntypedFormGroup): void {
    const queryParams: ProgramQueryParams = this.formToQuery(searchForm);
    this.router.navigate(['/', 'program-ajanlo'], {
      queryParams,
    });
  }

  private formToQuery(searchForm: UntypedFormGroup): ProgramQueryParams {
    let queryParams: ProgramQueryParams = {};
    if (searchForm.value.global_filter) {
      queryParams = { global_filter: searchForm.value.global_filter };
    }
    if (searchForm.value.program_type?.length > 0) {
      queryParams = { ...queryParams, 'program_type[]': searchForm.value.program_type };
    }
    if (searchForm.value.date_from) {
      queryParams = { ...queryParams, date_from: format(searchForm.value.date_from, 'yyyy-MM-dd') };
    }
    if (searchForm.value.date_until) {
      queryParams = { ...queryParams, date_until: format(searchForm.value.date_until, 'yyyy-MM-dd') };
    }
    return queryParams;
  }

  private toArray(item: any): any[] {
    if (!item) {
      return [];
    }

    return typeof item !== 'undefined' && item instanceof Array ? item : [item];
  }

  private fillTypes(availableTypes: ProgramTypes[], types: string[]): string[] {
    if (types.length > 0) {
      return types;
    }
    return availableTypes.map((item) => item.slug);
  }
}
