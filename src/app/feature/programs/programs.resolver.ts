import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ProgramsApiService } from './services/programs-api.service';
import { RouteDataProgramsMapped } from './programs.definitions';
import { RedirectService } from '@trendency/kesma-ui';

@Injectable()
export class ProgramsResolver {
  constructor(
    private readonly programsService: ProgramsApiService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<RouteDataProgramsMapped> {
    const queryParams = route.queryParams;
    return forkJoin({
      recommendedArticles: this.programsService.getProgramRecommendedArticles(),
      programTypes: this.programsService.getProgramTypes(),
      selectTypes: this.programsService.getProgramSelectTypes(),
      programRecommendations: this.programsService.getProgramRecommendations(queryParams),
    }).pipe(
      tap(({ programRecommendations }) => {
        if (this.redirectService.shouldBeRedirect(queryParams?.['page'], programRecommendations?.data)) {
          this.redirectService.redirectOldUrl('program-ajanlo', false, 302);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
