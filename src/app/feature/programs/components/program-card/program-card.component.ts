import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { FormatDatePipe, ImageLazyLoadDirective } from '@trendency/kesma-core';
import { ProgramDatePipe, ProgramRecommendationItem } from '../../../../shared';

@Component({
  selector: 'app-program-card',
  templateUrl: './program-card.component.html',
  styleUrls: ['./program-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, NgClass, NgIf, NgStyle, RouterLink, FormatDatePipe, ProgramDatePipe, ImageLazyLoadDirective],
})
export class ProgramCardComponent {
  @Input() programRecommendations: ProgramRecommendationItem[];
}
