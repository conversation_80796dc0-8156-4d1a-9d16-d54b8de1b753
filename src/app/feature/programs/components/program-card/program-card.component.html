<div class="program-list">
  <ng-container *ngFor="let elem of programRecommendations; let i = index">
    <div class="element-wrapper">
      <div class="element-card" [ngClass]="{ last: i === programRecommendations?.length - 1 }">
        <div class="col-lg-6">
          <div class="image" *ngIf="!!elem?.image" trImageLazyLoad [title]="elem.title" [ngStyle]="{ 'background-image': 'url(' + elem.image + ')' }"></div>
        </div>
        <div class="col-lg-6">
          <div class="poser">
            <a [routerLink]="['/', 'program-ajanlo', elem?.slug]">
              <p class="title">{{ elem.title }}</p>
            </a>
            <div class="desc" *ngIf="elem?.lead">{{ elem?.lead }}</div>
          </div>
          <ng-container *ngIf="elem?.endDate && elem?.endDate !== elem?.startDate; else isOnlyStartDate">
            <div class="time">
              {{ elem?.startDate ?? '' | programDate: elem?.endDate ?? '' }} -
              {{ elem?.endDate ?? '' | programDate: elem?.startDate ?? '' }}
            </div>
          </ng-container>
          <ng-template #isOnlyStartDate>
            <div class="time">{{ elem?.startDate ?? '' | formatDate: 'h-m' }}</div>
          </ng-template>
          <div *ngIf="elem?.address" class="location">
            {{ elem?.address }}
          </div>
          <div *ngIf="elem?.phoneNumber">
            {{ elem.phoneNumber }}
          </div>
          <div class="program-types">
            <span class="program-type" *ngFor="let type of elem.types">{{ type.title }}</span>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
