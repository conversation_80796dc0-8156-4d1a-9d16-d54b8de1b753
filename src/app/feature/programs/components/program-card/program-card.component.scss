@use 'shared' as *;

.element-card {
  display: flex;
  border: 2px solid $grey-19;
  padding: 16px;
  margin: 15px 0;
  @include media-breakpoint-down(md) {
    display: block;
  }

  .image {
    @include imgRatio(289%, 162%);
    width: 100%;
    @include imgZoom();

    @include media-breakpoint-down(md) {
      margin-bottom: 16px;
    }
  }

  .title {
    font-size: 30px;
    line-height: 36px;
    text-transform: uppercase;
    margin-bottom: 20px;
  }

  .desc {
    font-size: 18px;
  }

  .program-types {
    margin: 15px 0;
    margin-right: -15px;
    margin-bottom: 5px;

    .program-type {
      display: inline-block;
      color: $white;
      background-color: $blue;
      padding: 7px 14px;
      border-radius: 6px;
      font-size: 12px;
      margin-right: 10px;
      margin-bottom: 10px;
      text-transform: uppercase;
      line-height: 15px;
      font-weight: 600;
      font-family: $font-inter;
    }
  }
}
