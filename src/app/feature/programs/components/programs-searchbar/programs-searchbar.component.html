<form class="programs-searchbar" [formGroup]="searchForm">
  <div class="searchbar-top">
    <div class="event-search-label">Esemé<PERSON> kere<PERSON></div>
    <div class="detailed-search-label only-desktop">R<PERSON>zletes kereső</div>
  </div>
  <div class="searchbar-middle">
    <div class="custom-search" [ngClass]="{ active: textSearchFocused }">
      <i (click)="onSearch()" class="icon icon-search"></i>
      <input type="text" class="text-search" formControlName="global_filter" (focus)="inputFocus()" (blur)="inputBlur()" placeholder="pl. kiállítás" />
      <i class="icon icon-clear" (click)="onClearValue()"></i>
    </div>
    <div class="detailed-search-row">
      <div class="detailed-search-label only-mobile">Részletes kereső</div>
      <button class="arrow" (click)="onDetailedSearchToggle()">
        <i class="arrow-icon" [ngClass]="{ 'arrow-icon-down': !isDetailedSearchOpen, 'arrow-icon-up': isDetailedSearchOpen }"></i>
      </button>
    </div>
  </div>
  <div class="searchbar-bottom" *ngIf="isDetailedSearchOpen">
    <div class="searchbar-date">
      <div class="date-from">
        <div class="input-label">Kezdődátum</div>
        <div class="inline-calendar-card">
          <app-date-time-picker
            [id]="'selectorDateFrom'"
            controlName="date_from"
            [formGroup]="searchForm"
            [enableTime]="false"
            [valueFormat]="dateValueFormat"
            [inline]="true"
          ></app-date-time-picker>
        </div>
      </div>
      <div class="date-to">
        <div class="input-label">Végdátum</div>
        <div class="inline-calendar-card">
          <app-date-time-picker
            [id]="'selectorDateTo'"
            controlName="date_until"
            [formGroup]="searchForm"
            [enableTime]="false"
            [valueFormat]="dateValueFormat"
            [inline]="true"
          ></app-date-time-picker>
        </div>
      </div>
    </div>
    <div class="searchbar-types">
      <div class="input-label">Típus</div>
      <button
        class="searchbar-type"
        [ngClass]="{ active: type?.slug ?? '' | isSelected: searchForm.get('program_type')?.value }"
        *ngFor="let type of programTypes"
        (click)="onClickType(type?.slug ?? '')"
      >
        {{ type?.title }}
      </button>
    </div>
    <div class="button-row" (click)="onSearch()">
      <button class="search-button">Keresés</button>
    </div>
  </div>
</form>
