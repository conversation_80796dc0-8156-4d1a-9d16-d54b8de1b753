@use 'shared' as *;

.programs-searchbar {
  .searchbar-top {
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 14px;
    line-height: 21px;
    text-transform: uppercase;
    margin-bottom: 12px;
    color: $black;

    .only-desktop {
      @include media-breakpoint-down(sm) {
        display: none;
      }
    }
  }

  .searchbar-middle {
    display: flex;
    justify-content: space-between;
    @include media-breakpoint-down(sm) {
      display: block;
    }

    .custom-search {
      color: $black-2;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 15px;
      padding: 12px 0 12px 0;
      transition: all ease 0.1s;
      overflow: hidden;
      width: 70%;
      border: 2px solid $grey-19;
      padding: 10px;

      @media (max-width: 1135px) {
        margin-right: 25px;
      }
      @include media-breakpoint-down(md) {
        margin-right: 15px;
      }

      @include media-breakpoint-down(sm) {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
      }

      &.active {
        border: 1px solid $grey-16;
        box-sizing: border-box;
        border-radius: 6px;
        padding-right: 20px;
        padding-left: 16px;

        .text-search {
          width: calc(100% - 56px);

          &::placeholder {
            color: transparent;
          }
        }

        .icon-search {
          margin-right: 15px;
          cursor: pointer;
        }

        .icon-clear {
          width: 12px;
          height: 12px;
          margin-left: 5px;
          cursor: pointer;
          margin-top: 3px;
        }
      }

      .text-search {
        padding: 0;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;

        &::placeholder {
          font-weight: 500;
          font-size: 14px;
          line-height: 21px;
        }
      }

      .text-search {
        width: 165px;
      }

      .author-search {
        width: 83px;
      }

      .icon-search {
        @include icon('icons/search.svg');
        margin-right: 7px;
        min-width: 24px;
        min-height: 24px;
      }

      .icon-author {
        @include icon('icons/author.svg');
        margin-right: 6px;
        min-width: 24px;
        min-height: 24px;
      }

      .icon-clear {
        @include icon('icons/x.svg');
        width: 0;
        min-width: 0;
      }
    }

    .detailed-search-row {
      @include media-breakpoint-down(md) {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .detailed-search-label {
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        text-transform: uppercase;
      }

      .only-mobile {
        @include media-breakpoint-up(md) {
          display: none;
        }
      }

      .arrow {
        height: 100%;
        padding: 17px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        background-color: $blue;
        border-radius: 6px;

        .arrow-icon {
          width: 18px;
          height: 18px;
          color: white;
          @include icon('icons/arrow-right-white.svg');

          &.arrow-icon-down {
            transform: rotate(90deg);
          }

          &.arrow-icon-up {
            transform: rotate(-90deg);
          }
        }
      }
    }
  }

  .searchbar-bottom {
    .input-label {
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;
      margin-bottom: 10px;
    }

    .searchbar-date {
      display: flex;
      width: 100%;
      margin-top: 20px;
      justify-content: space-between;
      @include media-breakpoint-down(sm) {
        display: block;
      }

      .date-from,
      .date-to {
        width: 46%;
        @include media-breakpoint-down(sm) {
          width: 100%;
          margin-bottom: 40px;
        }
      }
    }

    .searchbar-types {
      margin-top: 20px;

      .searchbar-type {
        display: inline-block;
        color: black;
        padding: 7px 14px;
        border-radius: 6px;
        font-size: 12px;
        margin-right: 10px;
        text-transform: uppercase;
        line-height: 15px;
        font-weight: 600;
        font-family: $font-inter;
        margin-bottom: 6px;

        &.active {
          background-color: $blue;
          color: $white;
        }
      }
    }

    .button-row {
      margin-top: 20px;
      display: flex;
      justify-content: center;

      .search-button {
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        background-color: $grey-22;
        border: 1px solid $grey-16;
        border-radius: 6px;
        padding: 14px 76px;
      }
    }
  }
}
