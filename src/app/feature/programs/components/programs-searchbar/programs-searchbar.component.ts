import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormGroup } from '@angular/forms';
import { <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { IsSelectedPipe } from '../../pipes/isSelected.pipe';
import { DateTimePickerComponent, ProgramTypes } from '../../../../shared';

@Component({
  selector: 'app-programs-searchbar',
  templateUrl: './programs-searchbar.component.html',
  styleUrls: ['./programs-searchbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, ReactiveFormsModule, NgClass, NgIf, NgFor, IsSelectedPipe, DateTimePickerComponent],
})
export class ProgramsSearchbarComponent {
  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output() search = new EventEmitter();
  @Output() clearGlobalFilter = new EventEmitter();
  @Output() toggleType = new EventEmitter<string>();
  @Output() dateFromSelect = new EventEmitter<Date | null>();
  @Output() dateUntilSelect = new EventEmitter<Date | null>();

  @Input() searchForm: UntypedFormGroup;
  @Input() programTypes: ProgramTypes[];

  dateValueFormat: string = `yyyy-MM-dd`;

  isDetailedSearchOpen = false;
  textSearchFocused = false;

  onDetailedSearchToggle(): void {
    this.isDetailedSearchOpen = !this.isDetailedSearchOpen;
  }

  inputFocus(): void {
    this.textSearchFocused = true;
  }

  inputBlur(): void {
    setTimeout((): void => {
      this.textSearchFocused = false;
    }, 100);
  }

  onSearch(): void {
    this.search.emit();
  }

  onClearValue(): void {
    this.clearGlobalFilter.emit();
  }

  onClickType(selectedType: string): void {
    this.toggleType.emit(selectedType);
  }
}
