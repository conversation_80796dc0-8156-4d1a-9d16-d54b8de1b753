import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { NgClass, NgIf } from '@angular/common';
import { ProgramsSearchbarComponent } from '../programs-searchbar/programs-searchbar.component';
import { ProgramCardComponent } from '../program-card/program-card.component';
import {
  defaultMetaInfo,
  PagerComponent,
  PortalConfigService,
  ProgramQueryParams,
  ProgramRecommendationItem,
  ProgramsSidebarComponent,
  ProgramTypes,
} from '../../../../shared';
import { ProgramsSearchService } from '../../services/programs-search.service';
import { ProgramRecommendationsResult, RouteDataProgramsMapped } from '../../programs.definitions';
import { ProgramsApiService } from '../../services/programs-api.service';


const MAX_RESULTS_PER_PAGE = 10;

@Component({
  selector: 'app-programs',
  templateUrl: './programs.component.html',
  styleUrls: ['./programs.component.scss'],
  imports: [ProgramsSearchbarComponent, NgClass, ProgramCardComponent, NgIf, PagerComponent, AdvertisementAdoceanComponent, ProgramsSidebarComponent],
})
export class ProgramsComponent implements OnInit, OnDestroy {
  programTypes: ProgramTypes[];
  recommendedArticles: ArticleCard[];
  programRecommendations: ProgramRecommendationItem[];
  limit: {
    pageCurrent: number;
    pageMax: number;
    rowAllCount: number;
    rowFrom: number;
    rowOnPageCount: number;
  };
  searchForm: UntypedFormGroup;
  maxPerPage = MAX_RESULTS_PER_PAGE;
  adverts: AdvertisementsByMedium | undefined;

  private readonly destroy$ = new Subject<boolean>();

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly searchService: ProgramsSearchService,
    private readonly programsApiService: ProgramsApiService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,

  ) {}

  ngOnInit(): void {
    this.setPropertiesFromResolvedData(this.route.snapshot.data['pageData']);
    this.setMetaData();

    this.route.queryParams
      .pipe(
        tap(() => this.resetAds()),
        switchMap((queryParams: ProgramQueryParams) => {
          return this.programsApiService.getProgramRecommendations(queryParams);
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((data: ProgramRecommendationsResult) => {
        this.programRecommendations = data?.data;
        this.limit = data?.meta?.limitable || {
          pageCurrent: 1,
          pageMax: 1,
          rowAllCount: 0,
          rowFrom: 0,
          rowOnPageCount: 0,
        };
        this.programRecommendations = this.addListedLocations(this.programRecommendations) ?? [];
        this.changeRef.detectChanges();
      });

    this.route.data
      .pipe(
        tap(() => this.resetAds()),
        switchMap((): Observable<Advertisement[]> => {
          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.destroy$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;

        this.changeRef.detectChanges();
      });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.changeRef.detectChanges();
  }

  onSelectPage(page: number): void {
    const query = {
      ...this.route.snapshot.queryParams,
      rowCount_limit: 10,
      page_limit: page - 1,
    };

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: query,
    });
  }

  onSearch(): void {
    this.searchService.navigateByQuery(this.searchForm);
  }

  onClearGlobalFilter(): void {
    const queryParams: ProgramQueryParams = this.route.snapshot.queryParams;
    this.searchForm.get('global_filter')?.setValue(null);
    this.router.navigate(['/', 'program-ajanlo'], {
      queryParams,
    });
  }

  onToggleType(typeSlug: string): void {
    let programTypes: string[] = this.searchForm.get('program_type')?.value;
    const isTypeToggled = programTypes.indexOf(typeSlug);
    programTypes = isTypeToggled > -1 ? programTypes.filter((item) => item !== typeSlug) : [...programTypes, typeSlug];
    this.searchForm.get('program_type')?.setValue(programTypes);
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private setPropertiesFromResolvedData(data: RouteDataProgramsMapped): void {
    this.programTypes = data.programTypes;
    this.recommendedArticles = data.recommendedArticles;
    this.searchForm = this.searchService.initSearchForm(this.route.snapshot.queryParams, this.programTypes);
    this.programRecommendations = data.programRecommendations?.data;
    this.limit = data.programRecommendations.meta.limitable;
    this.programRecommendations = this.addListedLocations(this.programRecommendations) ?? [];
  }

  private setMetaData(): void {
    const title = `${this.portalConfig.portalName} - Programajánló`;

    const metaData: IMetaData = {
      ...defaultMetaInfo(this.portalConfig),
      title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  private addListedLocations(response: ProgramRecommendationItem[]): ProgramRecommendationItem[] | undefined {
    if (!response) {
      return;
    }
    return response.map((item) => {
      let listedLocations: string[] = [];
      item?.locations.forEach((location) => {
        listedLocations = [...listedLocations, ` ${location?.title}`];
      });

      return { ...item, listedLocations };
    });
  }
}
