<section class="programs-section">
  <div class="wrapper with-aside">
    <div class="left-column">
      <h1 class="programs-section-title"><span class="text">Programajánló</span></h1>
      <div class="search-container">
        <app-programs-searchbar
          [programTypes]="programTypes"
          [searchForm]="searchForm"
          (search)="onSearch()"
          (clearGlobalFilter)="onClearGlobalFilter()"
          (toggleType)="onToggleType($event)"
        ></app-programs-searchbar>
      </div>
      <div class="programs-wrapper">
        <div class="content" [ngClass]="{ 'full-width': recommendedArticles?.length === 0 }">
          <app-program-card [programRecommendations]="programRecommendations"></app-program-card>
          <ng-container *ngIf="!programRecommendations?.length">
            <p class="no-results">A kiválasztott paraméterre jelenleg nincs találat.</p>
          </ng-container>
          <app-pager [currentPage]="limit?.pageCurrent" [pageMax]="limit?.pageMax" [rowOnPageCount]="maxPerPage" (pageChange)="onSelectPage($event)">
          </app-pager>
        </div>
      </div>
    </div>
    <aside>
      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.box_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>

      <div class="sidebar" *ngIf="recommendedArticles.length > 0">
        <app-programs-sidebar [recommendedArticles]="recommendedArticles"></app-programs-sidebar>
      </div>
    </aside>
  </div>
</section>
