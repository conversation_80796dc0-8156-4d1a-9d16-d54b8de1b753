<div class="weather-map" *ngIf="current">
  <div class="left">
    <div class="info-box">
      <div class="info-box-top">
        <h4 class="info-box-title">{{ current.skyView }}</h4>
        <div class="time-info">
          <i class="icon icon-day"></i>
          <span class="text">{{ current.sunrise | slice: 0 : 5 }}</span>
          <i class="icon icon-night"></i>
          <span class="text">{{ current.sunset | slice: 0 : 5 }}</span>
        </div>
      </div>
      <div class="info-box-mid">
        <i [class]="'icon-weather ' + current.icon2"></i>
        <div class="deg-actual">{{ current.temperature }}°</div>
        <div class="deg-minmax">
          <div class="deg-max">{{ current.maxTemperature }}°</div>
          <div class="deg-min">{{ current.minTemperature }}°</div>
        </div>
      </div>
      <div class="info-box-bottom">
        <div class="information">
          <p class="info-label">Csapadék</p>
          <p class="info-data">{{ current.humidity }} mm</p>
        </div>
        <div class="information">
          <p class="info-label">Légnyomás</p>
          <p class="info-data">{{ current.airPressure }} hPa</p>
        </div>
        <div class="information">
          <p class="info-label">Szélerősség</p>
          <p class="info-data">{{ current.wind }} km/h</p>
        </div>
        <div class="information">
          <p class="info-label">Szélirány</p>
          <p class="info-data">{{ current.windDirection }}</p>
        </div>
        <div class="information">
          <p class="info-label">UV</p>
          <p class="info-data">{{ uv.text }}</p>
        </div>
      </div>
    </div>
  </div>
  <div class="right">
    <div class="map">
      <div class="map-deg-box" [class]="cityData.class" *ngFor="let cityData of filteredCities">
        <div class="bordered-box" *ngIf="cityData.weather">
          <i class="icon-weather" [class]="cityData.weather.icon2"></i>
          <p class="deg-max">{{ cityData.weather.maxTemperature }}°</p>
          <p class="deg-min">{{ cityData.weather.minTemperature }}°</p>
        </div>
        <p class="location-name">{{ cityData.city }}</p>
      </div>
    </div>
  </div>
</div>
