import { Component, Input } from '@angular/core';
import { CityWeatherCurrent, MapCityData, WeatherCity, WeatherUv } from '../../../../shared';
import { NgFor, NgIf, SlicePipe } from '@angular/common';

const FILTERED_CITIES = ['Győr', 'Budapest', 'Miskolc', 'Debrecen', 'Pécs', 'Szeged'];

const CITY_CLASSES: Record<string, string> = {
  Győr: 'gyor',
  Budapest: 'budapest',
  Miskolc: 'miskolc',
  Debrecen: 'debrecen',
  Pécs: 'pecs',
  Szeged: 'szeged',
};

@Component({
  selector: 'app-weather-map',
  templateUrl: './weather-map.component.html',
  styleUrls: ['./weather-map.component.scss'],
  imports: [NgIf, NgFor, SlicePipe],
})
export class WeatherMapComponent {
  @Input() current: CityWeatherCurrent;

  @Input() set cities(cities: Record<WeatherCity, CityWeatherCurrent>) {
    this.filteredCities = FILTERED_CITIES.map((city) => ({
      city,
      weather: cities[city as WeatherCity],
      class: CITY_CLASSES[city],
    }));
  }

  @Input() uv: WeatherUv;

  filteredCities: MapCityData[] = [];
}
