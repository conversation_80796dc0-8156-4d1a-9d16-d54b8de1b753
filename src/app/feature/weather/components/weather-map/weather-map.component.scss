@use 'shared' as *;

.icon-weather {
  width: 100%;
  height: 27px;
  display: inline-block;

  &.snowflake {
    @include icon('weather/snowflake.svg');
  }

  &.cloud-thunder-heavy {
    @include icon('weather/cloud-thunder-heavy.svg');
  }

  &.wind-sun {
    @include icon('weather/wind-sun.svg');
  }

  &.cloud-hurricane {
    @include icon('weather/cloud-hurricane.svg');
  }

  &.cloud-hail {
    @include icon('weather/cloud-hail.svg');
  }

  &.clouds {
    @include icon('weather/clouds.svg');
  }

  &.cloud-rain {
    @include icon('weather/cloud-rain.svg');
  }

  &.cloud-snow {
    @include icon('weather/cloud-snow.svg');
  }

  &.cloud-snow {
    @include icon('weather/cloud-snow.svg');
  }

  &.sunny {
    @include icon('weather/sunny.svg');
  }
}

.weather-map {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;

  .left {
    width: calc(100% - 20px - 680px);
    @include media-breakpoint-down(lg) {
      width: calc(100% - 20px - 550px);
    }
    @include media-breakpoint-down(md) {
      width: 100%;
    }

    .info-box {
      border: 1px solid $grey-19;
      border-radius: 6px;
      padding: 30px 40px;
      width: 100%;
      @include media-breakpoint-down(sm) {
        padding: 20px;
      }

      .info-box-top {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .info-box-title {
          font-size: 20px;
          line-height: 100%;
          color: $black;
          font-weight: normal;
          @include media-breakpoint-down(md) {
            font-size: 16px;
          }
        }

        .time-info {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .icon {
            width: 20px;
            height: 20px;
            display: inline-block;
            margin-right: 10px;

            &.icon-day {
              @include icon('weather/day.svg');
            }

            &.icon-night {
              @include icon('weather/night.svg');
              margin-left: 30px;
            }
          }

          .text {
            font-weight: 300;
            font-size: 12px;
            line-height: 18px;
          }
        }
      }

      .info-box-mid {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        align-items: center;
        margin: 30px 0 20px;
        @include media-breakpoint-down(md) {
          justify-content: space-between;
        }

        .icon-weather {
          width: 100px;
          height: 100px;
          margin-right: 40px;
          @include media-breakpoint-down(md) {
            margin-right: 0;
          }
        }

        .deg-actual {
          margin-right: 30px;
          font-weight: 300;
          font-size: 80px;
          @include media-breakpoint-down(md) {
            margin-right: 0;
          }
        }

        .deg-minmax {
          font-weight: 300;
          font-size: 30px;

          .deg-max {
            color: $red;
          }

          .deg-min {
            color: $blue;
          }
        }
      }

      .info-box-bottom {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: calc(100% + 10px);
        margin-left: -5px;
        margin-right: -5px;

        .information {
          margin: 15px 5px 0;

          .info-label {
            font-weight: 300;
            font-size: 12px;
            line-height: 18px;
            color: $grey-13;
            margin-bottom: 5px;
          }

          .info-data {
            font-weight: 300;
            font-size: 16px;
            line-height: 24px;
            color: $black;
          }
        }
      }
    }
  }

  .right {
    width: 680px;
    @include media-breakpoint-down(lg) {
      width: 550px;
    }
    @include media-breakpoint-down(md) {
      width: 100%;
      margin-top: 40px;
    }

    .map {
      width: 100%;
      @include imgRatio(334%, 209%);
      @include icon('weather/map.svg');
      background-color: transparent;
      position: relative;

      .map-deg-box {
        display: inline-block;
        text-align: center;
        font-weight: normal;
        font-size: 12px;
        line-height: 18px;
        color: $grey-11;
        position: absolute;
        top: 0;
        left: 0;
        transform: translate(-50%, -50%);
        @include media-breakpoint-down(xs) {
          font-size: 9px;
        }

        &.gyor {
          left: 24%;
          top: 22%;
        }

        &.budapest {
          left: 45%;
          top: 32%;
        }

        &.miskolc {
          left: 70%;
          top: 8%;
        }

        &.debrecen {
          left: 82%;
          top: 31%;
        }

        &.pecs {
          left: 30%;
          top: 80%;
        }

        &.szeged {
          left: 60%;
          top: 68%;
        }

        .bordered-box {
          border: 1px solid $grey-16;
          background: $white;
          border-radius: 6px;
          width: 60px;
          height: 87px;
          margin: 0 auto;
          font-weight: 300;
          font-size: 16px;
          line-height: 117%;
          padding-top: 9px;
          @include media-breakpoint-down(xs) {
            font-size: 9px;
            width: 30px;
            height: 43px;
            padding-top: 4px;
          }

          .icon-weather {
            @include media-breakpoint-down(xs) {
              width: 11px;
              height: 11px;
            }
          }

          .deg-max {
            color: $red;
          }

          .deg-min {
            color: $blue;
          }
        }

        .location-name {
          margin-top: 10px;
        }
      }
    }
  }
}
