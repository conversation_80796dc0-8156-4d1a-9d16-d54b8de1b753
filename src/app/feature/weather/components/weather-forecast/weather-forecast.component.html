<app-section-header sectionTitle="9 napos előrejelzés"></app-section-header>

<div class="forecast">
  <ng-container *ngFor="let day of nineDayForecast">
    <div class="day" *ngIf="day">
      <div class="top">
        <p class="dayname">{{ day.date | dfnsFormat: 'EEEE' | titlecase }}</p>
        <p class="date">{{ day.date | dfnsFormat: 'MMM d.' | titlecase }}</p>
      </div>
      <div class="mid">
        <i class="weather-icon" [class]="day.icon2" [title]="day.description"></i>
        <p class="deg-avg">{{ (day.maxTemperature + day.minTemperature) / 2 }}°</p>
        <p class="deg-max">{{ day.maxTemperature }}°</p>
        <p class="deg-min">{{ day.minTemperature }}°</p>
      </div>
      <div class="bottom">
        <i class="icon-rain"></i>
        <p class="bottom-text">{{ day.rain }} mm</p>
        <i class="icon-wind"></i>
        <p class="bottom-text">{{ day.wind }} km/h</p>
      </div>
    </div>
  </ng-container>
</div>
