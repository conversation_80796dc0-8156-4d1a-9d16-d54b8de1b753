import { Component, Input } from '@angular/core';
import { SectionHeaderComponent, WeatherForecast } from '../../../../shared';
import { Ng<PERSON><PERSON>, NgIf, TitleCasePipe } from '@angular/common';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'app-weather-forecast',
  templateUrl: './weather-forecast.component.html',
  styleUrls: ['./weather-forecast.component.scss'],
  imports: [SectionHeaderComponent, NgFor, NgIf, TitleCasePipe, DateFnsModule],
})
export class WeatherForecastComponent {
  nineDayForecast: WeatherForecast[] = [];

  @Input()
  set forecast(value: WeatherForecast[]) {
    this.nineDayForecast = value.slice(0, 10);
  }
}
