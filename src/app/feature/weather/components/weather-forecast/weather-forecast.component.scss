@use 'shared' as *;

.forecast {
  display: flex;
  justify-content: space-between;
  text-align: center;
  margin-bottom: 28px;
  margin-top: 40px;
  flex-wrap: wrap;
  @include media-breakpoint-down(md) {
    overflow: auto;
    flex-wrap: nowrap;
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
  }

  .day {
    width: calc(11.1111% - 12px);
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    margin-bottom: 12px;

    @include media-breakpoint-down(lg) {
      width: calc(20% - 12px);
    }
    @include media-breakpoint-down(md) {
      width: 91px;
      flex-shrink: 0;
      margin-left: 15px;
      &:last-child {
        margin-right: 15px;
      }
    }

    .top {
      padding: 20px 5px;
      border-bottom: 1px solid #e5e5e5;
      font-weight: 300;
      font-size: 12px;
      line-height: 18px;

      .dayname {
        margin-bottom: 3px;
      }

      .date {
        color: #808080;
      }
    }

    .mid {
      padding: 20px 5px;
      border-bottom: 1px solid #e5e5e5;

      .weather-icon {
        width: 100%;
        height: 37px;
        display: inline-block;

        &.snowflake {
          @include icon('weather/snowflake.svg');
        }

        &.cloud-thunder-heavy {
          @include icon('weather/cloud-thunder-heavy.svg');
        }

        &.wind-sun {
          @include icon('weather/wind-sun.svg');
        }

        &.cloud-hurricane {
          @include icon('weather/cloud-hurricane.svg');
        }

        &.cloud-hail {
          @include icon('weather/cloud-hail.svg');
        }

        &.clouds {
          @include icon('weather/clouds.svg');
        }

        &.cloud-rain {
          @include icon('weather/cloud-rain.svg');
        }

        &.cloud-snow {
          @include icon('weather/cloud-snow.svg');
        }

        &.cloud-snow {
          @include icon('weather/cloud-snow.svg');
        }

        &.sunny {
          @include icon('weather/sunny.svg');
        }
      }

      .deg-avg {
        font-weight: 300;
        font-size: 30px;
        line-height: 45px;
        margin: 20px auto;
      }

      .deg-max {
        font-weight: 300;
        font-size: 16px;
        line-height: 24px;
        color: $red;
        margin-bottom: 8px;
      }

      .deg-min {
        font-weight: 300;
        font-size: 16px;
        line-height: 24px;
        color: $blue;
      }
    }

    .bottom {
      padding: 20px 5px;

      .icon-rain {
        width: 21px;
        height: 16px;
        @include icon('weather/rain.svg');
      }

      .bottom-text {
        font-weight: 300;
        font-size: 14px;
        line-height: 21px;
      }

      .icon-wind {
        margin-top: 19px;
        width: 21px;
        height: 16px;
        @include icon('weather/wind.svg');
      }
    }
  }
}
