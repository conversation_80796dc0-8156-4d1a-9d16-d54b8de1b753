<section class="weather">
  <div class="wrapper with-aside">
    <div class="weather-top">
      <div class="page-title desktop-only">
        <div class="left">
          <span class="main-title">Időj<PERSON>rás</span>
        </div>
        <div class="right">
          <a href="https://koponyeg.hu" class="koponyeg-logo"></a>
        </div>
      </div>
      <div class="region-list">
        <button
          *ngFor="let region of orderedRegions; let i = index"
          type="button"
          class="region"
          [ngClass]="{
            active: city === region,
            'hidden-on-desktop': i >= regionsListLimit,
          }"
          (click)="onChangeRegion(region)"
        >
          {{ region }}
        </button>
        <ng-container *ngIf="regionsListLimit < orderedRegions.length">
          <app-overflow-button
            [items]="orderedRegions.slice(regionsListLimit)"
            [selectedItem]="city"
            (itemClick)="onChangeRegion($event)"
          ></app-overflow-button>
        </ng-container>
      </div>
      <app-weather-map [current]="cityWeatherData.current" [cities]="citiesCurrent" [uv]="cityWeatherData.uv"> </app-weather-map>
    </div>
    <div class="left-column">
      <app-weather-forecast [forecast]="cityWeatherData.forecast"></app-weather-forecast>
      <app-weather-forecast-text [dailyWeather]="cityWeatherData.text"></app-weather-forecast-text>
    </div>
    <aside>
      <app-sidebar [excludedIds]="recommendedArticles"></app-sidebar>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.box_1 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    </aside>
  </div>
</section>
