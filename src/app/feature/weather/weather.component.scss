@use 'shared' as *;

.weather {
  .wrapper {
    @include media-breakpoint-down(sm) {
      margin-top: 0;
    }

    .weather-top {
      width: 100%;
      margin-bottom: 40px;

      .page-title {
        font-size: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 15px 0 15px 20px;
        margin-bottom: 50px;
        margin-top: -5px;
        @include article-before-line();

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }

        @include media-breakpoint-down(sm) {
          margin-top: 30px;
          margin-bottom: 14px;
        }

        .left {
          .main-title {
            text-transform: uppercase;
            display: inline-block;
            margin-right: 9px;
            font-weight: 500;
          }
        }

        .right {
          .koponyeg-logo {
            width: 138px;
            height: 43px;
            @include icon('weather/koponyeg.png');
            @include media-breakpoint-down(lg) {
              width: 115px;
              height: 34px;
            }
          }
        }
      }

      .region-list {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;

        @include media-breakpoint-down(lg) {
          overflow: auto;
          flex-wrap: nowrap;
          margin-bottom: 40px;
          margin-top: 20px;
        }

        .region {
          font-weight: normal;
          font-size: 16px;
          line-height: 24px;
          color: $black;
          border-radius: 6px;
          padding: 8px 10px;
          white-space: nowrap;

          &.hidden-on-desktop {
            @include media-breakpoint-up(md) {
              display: none;
            }
          }

          &.active {
            color: $blue;
            background-color: rgba($blue, 0.2);

            &:before {
              content: ' ';
              width: 14px;
              height: 14px;
              margin-right: 5px;
              margin-bottom: -1px;
              @include icon('icons/bluearrow.svg');
            }
          }
        }

        app-overflow-button {
          margin-top: 8px;

          @include media-breakpoint-down(sm) {
            display: none;
          }
        }
      }
    }

    aside {
      margin-top: -30px;
    }
  }
}
