import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium } from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import {
  CityWeatherCurrent,
  defaultMetaInfo,
  OverflowButtonComponent,
  PortalConfigService,
  WEATHER_REGIONS,
  WeatherCity,
  WeatherDailyShort,
  WeatherData,
  WeatherForecast,
  WeatherService,
  WeatherUv,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { WeatherForecastTextComponent } from './components/weather-forecast-text/weather-forecast-text.component';
import { WeatherForecastComponent } from './components/weather-forecast/weather-forecast.component';
import { WeatherMapComponent } from './components/weather-map/weather-map.component';

@Component({
  selector: 'app-weather',
  templateUrl: './weather.component.html',
  styleUrls: ['./weather.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgFor,
    NgClass,
    NgIf,
    OverflowButtonComponent,
    WeatherMapComponent,
    WeatherForecastComponent,
    WeatherForecastTextComponent,
    SidebarComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class WeatherComponent implements OnInit, OnDestroy {
  readonly regionsListLimit = 7;
  readonly regions = WEATHER_REGIONS;
  readonly orderedRegions: string[];
  regionActive = 0;
  recommendedArticles = [];
  city: string;
  cityWeatherData: {
    current: CityWeatherCurrent;
    forecast: WeatherForecast[];
    text: WeatherDailyShort[];
    uv: WeatherUv;
  } = {
    current: {} as any,
    forecast: [],
    text: [],
    uv: {} as any,
  };
  citiesCurrent: Record<WeatherCity, CityWeatherCurrent> = {} as any;
  adverts: AdvertisementsByMedium;
  private readonly destroySubject: Subject<boolean> = new Subject<boolean>();
  private weatherData: WeatherData;

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly weatherService: WeatherService,
    private readonly seo: SeoService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef
  ) {
    this.city = portalConfig.city;
    this.orderedRegions = [portalConfig.city].concat(this.regions.filter((r) => r !== portalConfig.city));
  }

  ngOnInit(): void {
    this.weatherService
      .getFullWeatherData()
      .pipe(
        switchMap((weatherData): Observable<Advertisement[]> => {
          this.weatherData = weatherData;
          this.citiesCurrent = this.weatherData.current.reduce(
            (all, weather) => ({
              ...all,
              [weather.city]: weather,
            }),
            {}
          ) as Record<WeatherCity, CityWeatherCurrent>;
          this.setCityWeather();

          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.destroySubject)
      )
      .subscribe((ads) => {
        this.adverts = ads;

        this.changeRef.detectChanges();
      });
    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      ogTitle: `${this.portalConfig?.portalName} - Időjárás`,
    });
  }

  ngOnDestroy(): void {
    this.destroySubject.next(true);
    this.destroySubject.complete();
  }

  onChangeRegion(region: string): void {
    this.city = region;
    this.setCityWeather();
  }

  private setCityWeather(): void {
    this.cityWeatherData = {
      current: this.weatherData.current.find(({ city }) => city === this.city) as CityWeatherCurrent,
      forecast: this.weatherData.forecast[this.city as WeatherCity].slice(0, 9),
      text: this.weatherData.text,
      uv: this.weatherData.uv.find(({ city }) => city === this.city) as WeatherUv,
    };
  }
}
