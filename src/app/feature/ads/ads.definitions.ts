export type AdItem = Readonly<{
  id: string;
  category_id: string;
  category_name: string;
  category_title?: string;
  category_slug?: string;
  picture_id: string;
  title?: string;
  lead: string;
  description: string;
  ad_city: string;
  source_type: string;
  source_name: string;
  source_id: string;
  external_type: string;
  external_id: string;
  active: string;
  highlighted: number;
  start_date: string;
  end_date: string;
  created_at: string;
  search_index: string;
  is_grief: string;
  pictureFullSizeUrl: string;
}>;

export type CategoryItem = Readonly<{
  id: string;
  title: string;
  description: string;
  parent_id: string;
  slug: string;
}>;

export type AdsByCategoryResponse = Readonly<{
  totalCount: number;
  totalPage: number;
  data: AdItem[];
}>;

export type SwitchMapResponse = Readonly<{
  searchAds?: AdItem[];
  validCategories?: CategoryItem[];
  adsByCategory?: AdsByCategoryResponse;
  validAds?: AdItem[];
}>;
