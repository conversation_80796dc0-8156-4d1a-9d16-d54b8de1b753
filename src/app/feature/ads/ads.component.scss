@use 'shared' as *;

section.flyers {
  .wrapper {
    .left-column {
      .page-title {
        font-size: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 5px 0 5px 20px;
        margin-bottom: 50px;
        margin-top: -5px;
        @include article-before-line();

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }

        @include media-breakpoint-down(sm) {
          margin-top: 30px;
          margin-bottom: 14px;
          flex-wrap: wrap;
          padding-left: 0;
          padding-top: 0;

          &:before {
            height: 60px;
          }
        }

        .left {
          @include media-breakpoint-down(sm) {
            width: 100%;
          }

          .page-title-text {
            font-weight: 500;
            font-size: 30px;
            line-height: 30px;
            padding-right: 5px;

            &,
            &:visited,
            &:active,
            &:link {
              color: $base-text-color;
            }

            @include media-breakpoint-down(sm) {
              font-size: 20px;
              line-height: 30px;
              height: 60px;
              display: flex;
              align-items: center;
              padding: 0 0 0 20px;
            }
          }
        }

        .right {
          display: flex;
          justify-content: flex-end;

          @include media-breakpoint-down(sm) {
            justify-content: flex-start;
            width: 100%;
            flex-wrap: wrap;
            .search-input {
              margin: 10px 0;
              width: 100%;
            }
          }
        }
      }

      .categories {
        margin: 25px 0px;

        @include media-breakpoint-down(sm) {
          margin: 10px 0px;
        }

        .tag {
          display: inline-block;
          font-weight: 600;
          font-size: 12px;
          line-height: 15px;
          color: $base-text-color;
          font-family: $font-inter;
          padding: 8px 14px;
          text-transform: uppercase;
          margin: 5px 5px;

          &:hover {
            color: $link-color;
          }
        }
      }

      .search-details {
        display: flex;
        align-items: center;
        margin-bottom: 40px;

        .clear-search {
          width: 50px;
          height: 50px;
          background: $grey-22;
          border: 1px solid $grey-16;
          box-sizing: border-box;
          border-radius: 6px;
          margin-right: 17px;
          @include icon('icons/arrow-left.svg');
          background-size: 40%;
        }

        .details-text {
          font-weight: 500;
          font-size: 14px;
          line-height: 21px;
          color: $grey-7;
        }
      }

      .results-two-columns {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: 1fr;
        grid-column-gap: 30px;
        grid-row-gap: 30px;
        grid-auto-rows: auto;

        @include media-breakpoint-down(lg) {
          display: grid;
          grid-template-columns: 1fr;
          grid-template-rows: 1fr;
          grid-column-gap: 0;
          grid-row-gap: 30px;
        }

        app-classified-ad {
          width: 445px;

          @include media-breakpoint-down(lg) {
            width: 100%;
          }
        }
      }

      .results-one-column {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
        grid-column-gap: 0;
        grid-row-gap: 30px;
      }

      app-pager {
        display: flex;
        justify-content: center;
        margin: 50px 0;
      }
    }

    aside {
      margin-top: -30px;
    }

    &.no-aside {
      margin-top: 40px;
    }

    &.with-aside {
      &.no-margin {
        margin-top: 0;
      }
    }
  }
}
