<section class="flyers">
  <div class="wrapper no-aside">
    <div class="left-column">
      <div class="page-title">
        <div class="left">
          <a [routerLink]="['/', 'aprohirdetes']" class="page-title-text">APRÓHIRDETÉS</a>
        </div>
        <form [formGroup]="searchForm">
          <div class="right">
            <div class="search-input">
              <button (click)="onSearchButtonClick()" class="search-button"></button>
              <input formControlName="searchField" type="text" placeholder="Keresés" />
              <button (click)="onClearSearch()" class="clear-button"></button>
            </div>

            <div class="custom-select-box-flyer">
              <label class="custom-select-label-flyer">Rendezés</label>
              <select formControlName="dateOrderSelector" class="custom-select-flyer">
                <option value="newer">Legfrissebb elöl</option>
                <option value="older">Legrége<PERSON><PERSON> el<PERSON>l</option>
              </select>
              <i class="flyer-icon"></i>
            </div>
          </div>
        </form>
      </div>
      <div *ngIf="!searchValue" class="categories">
        <a class="tag" [routerLink]="['/', 'aprohirdetes']">Összes</a>
        <a class="tag" *ngFor="let category of adsCategorySlugs" [routerLink]="['/', 'aprohirdetes']" [queryParams]="{ category: category?.slug }"
          >{{ category?.title }}
        </a>
      </div>
    </div>

    <kesma-advertisement-adocean [style]="{ margin: '40px auto' }" *ngIf="adverts?.desktop?.leaderboard_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>

    <div class="search-details">
      <button (click)="onClearSearch()" *ngIf="isSearchPressed" class="clear-search"></button>
      <p class="details-text" *ngIf="isSearchPressed">
        {{ this.filteredAdsList?.length ? this.filteredAdsList.length : 0 }}
        találat „{{ searchValue }}” kifejezésre
      </p>
    </div>

    <div class="wrapper with-aside no-margin">
      <div class="left-column">
        <div *ngIf="normalAdsList?.length" class="results-two-columns">
          <app-classified-ad *ngFor="let adItem of normalAdsList" [adItem]="adItem"></app-classified-ad>
        </div>
        <div *ngIf="filteredAdsList?.length" class="results-one-column">
          <app-classified-ad *ngFor="let adItem of filteredAdsList" [adItem]="adItem"></app-classified-ad>
        </div>
        <app-pager [currentPage]="currentPage - START_FROM_ONE" [rowAllCount]="totalCount" (pageChange)="onSelectPage($event)" [rowOnPageCount]="10">
        </app-pager>
      </div>

      <aside>
        <app-sidebar></app-sidebar>
      </aside>
    </div>
  </div>
</section>
