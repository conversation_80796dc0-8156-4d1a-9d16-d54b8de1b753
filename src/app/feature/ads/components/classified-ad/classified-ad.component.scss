@use 'shared' as *;

.flyer-card {
  border: 1px solid $grey-19;
  border-radius: 6px;
  width: 100%;
  height: 100%;
  padding: 40px;

  @include media-breakpoint-down(sm) {
    width: 100%;
    max-width: none;
    padding: 20px 30px;
  }

  .lead {
    margin: 0 0 15px 0;
    font-size: 20px;
    line-height: 28px;
    font-weight: 600;
  }

  .flyer-card-content {
    .with-image {
      display: grid;
      grid-template-columns: 135px 2fr;
      grid-template-rows: 1fr;
      grid-column-gap: 0px;
      grid-row-gap: 0px;

      @include media-breakpoint-down(sm) {
        display: block;
      }
    }

    .image {
      display: block;
      height: 100px;
      width: 100px;
      border-radius: 50%;
      background-size: cover;
      background-position: center;

      @include media-breakpoint-down(sm) {
        float: left;
        margin-right: 20px;
      }
    }

    .details {
      display: flex;
      flex-direction: column;

      @include media-breakpoint-down(sm) {
        display: block;
      }

      .tag-holder {
        width: 100%;
        padding-bottom: 20px;

        .tag {
          display: inline-block;
          border-radius: 6px;
          color: $tag-text-color;
          background-color: $secondary-color;
          font-weight: 600;
          font-size: 12px;
          line-height: 15px;
          font-family: $font-inter;
          padding: 8px 14px;
          text-transform: uppercase;
        }
      }

      .content-text {
        font-weight: normal;
        font-size: 20px;
        line-height: 28px;
        display: block;
        margin-bottom: 53px;
        hyphens: auto;
        word-break: auto-phrase;

        @include media-breakpoint-down(lg) {
          font-size: 20px;
          line-height: 30px;
          margin-bottom: 14px;
        }
      }

      .publish-date {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        line-height: 19px;
        color: $grey-7;

        p {
          width: 100%;
          text-align: right;
        }
      }
    }
  }
}

.highlighted {
  background-color: $grey-25;
  border: 1px solid $secondary-color;
}
