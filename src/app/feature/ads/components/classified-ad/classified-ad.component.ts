import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, NgIf, Ng<PERSON>tyle } from '@angular/common';
import { FormatDatePipe, SafePipe } from '@trendency/kesma-core';
import { AdItem } from '../../ads.definitions';

@Component({
  selector: 'app-classified-ad',
  templateUrl: './classified-ad.component.html',
  styleUrls: ['./classified-ad.component.scss'],
  imports: [NgIf, NgClass, NgStyle, RouterLink, SafePipe, FormatDatePipe],
})
export class ClassifiedAdComponent {
  @Input() adItem: AdItem;

  get highlighted(): boolean {
    const isHighlighted = 1;
    return this.adItem.highlighted === isHighlighted;
  }
}
