<div class="flyer-card" [ngClass]="{ highlighted: highlighted }" *ngIf="adItem">
  <div class="flyer-card-content">
    <!-- The ad has an attached image -->
    <ng-container *ngIf="adItem.pictureFullSizeUrl">
      <div class="with-image">
        <div class="image" [ngStyle]="{ 'background-image': 'url(' + adItem.pictureFullSizeUrl + ')' }"></div>
        <div class="details">
          <div class="tag-holder">
            <a class="tag" [routerLink]="['/', 'aprohirdetes']" [queryParams]="{ category: adItem.category_slug }">
              {{ adItem.category_title || adItem.category_name || 'Apró' }}
            </a>
          </div>
          <div class="content-text" [innerHTML]="adItem.description | safe: 'html'"></div>
          <div class="publish-date">
            <p *ngIf="!highlighted">{{ adItem.start_date | formatDate: 'l-d' }}</p>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- The ad does not have an attached image -->
    <ng-container *ngIf="!adItem.pictureFullSizeUrl">
      <div class="details">
        <div class="tag-holder">
          <a class="tag" [routerLink]="['/', 'aprohirdetes']" [queryParams]="{ category: adItem.category_slug }">
            {{ adItem.category_title || adItem.category_name || 'Apró' }}
          </a>
        </div>
        <div class="content-text" [innerHTML]="adItem.description | safe: 'html'"></div>
        <div class="publish-date">
          <p *ngIf="!highlighted">{{ adItem.start_date | formatDate: 'l-d' }}</p>
        </div>
      </div>
    </ng-container>
  </div>
</div>
