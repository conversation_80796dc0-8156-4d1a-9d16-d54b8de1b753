import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { forkJoin, Subject, Subscription } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { AdItem, CategoryItem, SwitchMapResponse } from './ads.definitions';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, RedirectService } from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ClassifiedAdComponent } from './components/classified-ad/classified-ad.component';
import { NgFor, NgIf } from '@angular/common';
import { ApiService, defaultMetaInfo, PagerComponent, PortalConfigService } from '../../shared';

@Component({
  selector: 'app-ads',
  templateUrl: './ads.component.html',
  styleUrls: ['./ads.component.scss'],
  imports: [RouterLink, FormsModule, ReactiveFormsModule, NgIf, NgFor, AdvertisementAdoceanComponent, ClassifiedAdComponent, PagerComponent, SidebarComponent],
})
export class AdsComponent implements OnInit, OnDestroy {
  readonly START_FROM_ONE = 1;
  filteredAdsList: AdItem[];
  normalAdsList: AdItem[];
  subscription: Subscription;
  searchForm: UntypedFormGroup;
  searchValue: string;
  isSearchPressed = false;
  totalCount: number;
  currentPage = 1;
  adsCategorySlugs: CategoryItem[];
  adverts: AdvertisementsByMedium | undefined;
  private readonly destroy$ = new Subject<boolean>();
  private filterCategorySlug: string;

  constructor(
    private readonly router: Router,
    private readonly apiService: ApiService,
    private readonly route: ActivatedRoute,
    private readonly portalConfig: PortalConfigService,
    private readonly seo: SeoService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly redirectService: RedirectService
  ) {}

  ngOnInit(): void {
    this.searchForm = new UntypedFormGroup({
      dateOrderSelector: new UntypedFormControl('newer'),
      searchField: new UntypedFormControl(''),
    });

    this.subscription = this.searchForm?.controls['dateOrderSelector']?.valueChanges.pipe().subscribe((dateOrderSelector: string) => {
      this.filteredAdsList = this.sortAdsByDate(this.filteredAdsList, dateOrderSelector);
      this.normalAdsList = this.sortAdsByDate(this.normalAdsList, dateOrderSelector);
      this.changeRef.detectChanges();
    });

    this.route.queryParams
      .pipe(
        switchMap((data: Params) => {
          const firstPage = 1;
          this.filterCategorySlug = data?.['category'];
          this.searchValue = data?.['search'];
          this.isSearchPressed = !!this.searchValue;
          this.searchForm.controls['searchField'].setValue(this.searchValue);

          this.currentPage = data?.['page'] || firstPage;
          if (this.filterCategorySlug) {
            return forkJoin({ adsByCategory: this.apiService.adsByCategory(this.filterCategorySlug, this.currentPage) });
          }
          if (this.searchValue) {
            return forkJoin({
              searchAds: this.apiService.adsSearch(data?.['search'], '-'),
              validCategories: this.apiService.validAdsCategories(),
            });
          }
          return forkJoin({
            validAds: this.apiService.allValidAds(),
            validCategories: this.apiService.validAdsCategories(),
          });
        }),
        map((response: SwitchMapResponse) => {
          const order = this.searchForm.controls['dateOrderSelector'].value;
          const data = response?.adsByCategory?.data || response?.validAds || response?.searchAds;
          const sortedList = this.sortAdsByDate(data ?? [], order);
          this.adsCategorySlugs = response?.validCategories ?? [];

          this.seo.setMetaData({
            ...defaultMetaInfo(this.portalConfig),
            ogTitle: `${this.portalConfig?.portalName} - Apróhirdetések`,
          });

          if (response?.adsByCategory || response?.searchAds) {
            this.filteredAdsList = sortedList;
            this.totalCount = response?.adsByCategory?.totalCount ?? 0;
            this.normalAdsList = [];
          } else {
            this.normalAdsList = sortedList;
            this.filteredAdsList = [];
          }

          if (!this.normalAdsList?.length && !this.filteredAdsList?.length && this.currentPage > 1) {
            this.redirectService.redirectOldUrl(`aprohirdetes${this.filterCategorySlug ? `?category=${this.filterCategorySlug}` : ''}`, false, 302);
          }

          this.adStoreAdo.enableAds();
        }),
        tap(() => this.resetAds()),
        switchMap(() => this.adStoreAdo.advertisemenets$),
        map((ads): AdvertisementsByMedium => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.destroy$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.changeRef.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  onSelectPage(pageNumber: number): void {
    this.currentPage = pageNumber;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { category: this.filterCategorySlug, page: this.currentPage },
    });
  }

  onSearchButtonClick(): void {
    const searchValue = this.searchForm.controls['searchField']?.value;

    if (!searchValue) {
      this.isSearchPressed = false;
      this.router.navigate([], {
        relativeTo: this.route,
      });
      return;
    }
    this.searchValue = searchValue;
    this.isSearchPressed = true;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { search: searchValue },
    });
  }

  onClearSearch(): void {
    if (this.isSearchPressed) {
      this.router.navigate([], {
        relativeTo: this.route,
      });
      this.filterCategorySlug = '';
    }
    this.searchForm.controls['searchField'].setValue('');
    this.searchValue = '';
    this.isSearchPressed = false;
  }

  private sortAdsByDate(adList: AdItem[], order: string): AdItem[] {
    switch (order) {
      case 'newer': {
        const newer = adList?.sort((a: AdItem, b: AdItem) => new Date(b?.start_date).getTime() - new Date(a?.start_date).getTime());
        return this.moveHighlightedToFront(newer);
      }
      case 'older': {
        const older = adList?.sort((a: AdItem, b: AdItem) => new Date(a?.start_date).getTime() - new Date(b?.start_date).getTime());
        return this.moveHighlightedToFront(older);
      }
      default:
        return this.moveHighlightedToFront(adList);
    }
  }

  private moveHighlightedToFront(adItems: AdItem[]): AdItem[] {
    const isHighlighted = 1;
    const highlightedAds = adItems.filter((item) => item.highlighted === isHighlighted);
    const regularAds = adItems.filter((item) => item.highlighted !== isHighlighted);
    return highlightedAds.concat(regularAds);
  }
}
