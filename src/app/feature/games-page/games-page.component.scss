@use 'shared' as *;

$images-path: '/assets/images/games/';

:host {
  display: block;
  background: #0182a9;
}

// fő <PERSON>rk<PERSON>
.games-content {
  background: url($images-path + 'ojatekok2017-bg.jpg') 50% 0 no-repeat;
  background-size: contain;
  width: 100%;
  max-width: 1920px;
  margin: auto;
  min-height: 1300px; //To not shift the background image when loading the ads.
  padding-bottom: 50px; //Leave space for the Bors logo from the footer
  .wrapper {
    width: 1100px;
  }
}

.games-head {
  height: 143px;
}

.game-nav {
  transform: translate(5px, 41px);

  @include media-breakpoint-up(md) {
    transform: translate(5px, 26px);
  }

  .game-logo {
    display: block;
    width: 237px;
    height: 68px;
  }

  .game-kvizpart {
    background: #000;
    display: inline-block;
    font-family: $font-family;
    font-size: 16px;
    padding: 5px 10px;
    text-transform: uppercase;
    transform: translateY(10px);
    color: $primary-color;
  }
}

.related-game {
  border: 5px solid $primary-color;
  position: relative;

  ::ng-deep {
    .advertisement-block {
      display: block;
    }

    img {
      display: block;
      height: auto;
      width: 100%;
    }

    p,
    .tags {
      display: none;
    }

    .jatek-cim {
      background: #000;
      color: #fff;
      display: block;
      font-family: $font-family;
      font-size: 18px;
      font-weight: 700;
      padding: 15px;
      text-transform: uppercase;
      width: 100%;

      @include media-breakpoint-up(sm) {
        background: rgba(0, 0, 0, 0.7);
        bottom: 0;
        font-size: 20px;
        left: 0;
        position: absolute;
      }

      @include media-breakpoint-up(md) {
        font-size: 40px;
        padding: 30px;
      }
    }
  }
}

.games-lb {
  background: #fff;
}

.list-of-games {
  background: #fff;
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  padding: 10px;

  @include media-breakpoint-up(sm) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include media-breakpoint-up(md) {
    grid-template-columns: repeat(3, 1fr);
  }

  @include media-breakpoint-up(xl) {
    grid-template-columns: repeat(4, 1fr);
  }

  ::ng-deep {
    .empty-zone {
      display: none;
      visibility: hidden;
    }

    kesma-advertisement-adocean {
      background: #ebeaea;
      position: relative;

      section,
      .ad-wrapper,
      .advertisement-block {
        height: 100%;
      }
    }

    .jatek-elem {
      padding: 0 0 30px;
      height: 100%;
      position: relative;

      img {
        display: block;
        height: auto;
        width: 100%;
      }

      .jatek-cim {
        font-family: $font-family;
        font-weight: 700;
        color: #3f3f3f;
        display: block;
        font-size: 21px;
        padding: 6px 12px;
      }

      p {
        border-top: 1px dashed rgba(0, 0, 0, 0.25);
        color: #3f3f3f;
        font-size: 14px;
        line-height: 18px;
        padding: 6px 12px;
      }

      .tags,
      .game-play {
        font-family: $font-family;
        font-weight: 700;
        font-size: 14px;
      }

      .tags {
        bottom: 0;
        left: 0;
        position: absolute;

        span {
          display: none;

          &.game-type {
            background: #fe3c3c;
            color: #fff;
            display: inline-block;
            padding: 5px;
            text-transform: uppercase;

            &:empty {
              visibility: hidden;
            }
          }
        }
      }

      .game-play {
        background: #0182a9;
        bottom: 0;
        color: #fff;
        display: inline-block;
        padding: 5px;
        position: absolute;
        right: 0;
        text-transform: uppercase;
      }
    }
  }
}
