<section class="games-content">
  <div class="wrapper">
    <!-- A felső logó és kvízpart link -->
    <div class="games-head">
      <div class="game-nav">
        <a [href]="siteUrl + '/jatekok'" class="game-logo">
          <img [src]="'/assets/images/games/' + this.siteName + '-games-logo.svg'" alt="" loading="lazy" height="69" />
        </a>
      </div>
    </div>

    <!-- A kiemelt, nagy játék -->
    <div class="related-game">
      <kesma-advertisement-adocean [ad]="mainGame" [hasNoParentHeight]="true" class="jatek-elem"></kesma-advertisement-adocean>
    </div>

    <!-- Leaderboard -->
    <div class="games-lb">
      <div class="goaWrap">
        <kesma-advertisement-adocean [ad]="gameAdDesktop" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean [ad]="gameAdMobile" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
      </div>
    </div>

    <!-- Kis játékok listája -->
    <div class="list-of-games" *ngIf="canShowGames$ | async">
      <kesma-advertisement-adocean *ngFor="let game of games" class="game" [ad]="game" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
    </div>
  </div>
</section>
