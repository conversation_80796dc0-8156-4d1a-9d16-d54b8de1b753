import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Advertisement, AdvertisementAdoceanComponent } from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { defaultMetaInfo, PortalConfigService } from '../../shared';

@Component({
  selector: 'app-games-page',
  templateUrl: './games-page.component.html',
  styleUrls: ['./games-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AdvertisementAdoceanComponent, NgIf, NgFor, AsyncPipe],
})
export class GamesPageComponent implements OnInit, AfterViewInit {
  canShowGames$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  siteUrl: string;
  siteName: string;
  /*Ads*/
  readonly gameAdDesktop: Advertisement = {
    zonaId: 'adoceanhupoitmlgggn',
    medium: 'desktop',
    bannerName: 'games_page',
    pageType: 'games',
    masterId: 'QHkGiOyIklqUm5VBe3OU0BCPEAgPj0LaiPOwj3DirT3.E7',
    isAdultAd: false,
  };
  readonly gameAdMobile: Advertisement = {
    zonaId: 'adoceanhunflqbnfuzv',
    medium: 'mobile',
    bannerName: 'games_page',
    pageType: 'games',
    masterId: 'QHkGiOyIklqUm5VBe3OU0BCPEAgPj0LaiPOwj3DirT3.E7',
    isAdultAd: false,
  };
  gamesIds: string[] = [
    'adoceanhuybfrowkhpo',
    'adoceanhuvcmhcuemvj',
    'adoceanhusddofroqfe',
    'adoceanhupekejoifkd',
    'adoceanhumfrkmlsjjy',
    'adoceanhuzfirpimott',
    'adoceanhuwgphdwftoo',
    'adoceanhuthgogtphin',
    'adoceanhuqinekqjmdi',
    'adoceanhunjelnndryd',
    'adoceanhukklrqknfhc',
    'adoceanhuxkcieihknx',
    'adoceanhuuljohvqobs',
    'adoceanhurmqelsktwn',
    'adoceanhuonhlopeibm',
    'adoceanhuloorbnomlh',
    'adoceanhuyofifkirgc',
    'adoceanhuvpmoixrfeb',
    'adoceanhusaefmulkvw',
    'adoceanhupbllprfpfr',
    'adoceanhumccscpptam',
    'adoceanhuzcjigmjiul',
    'adoceanhuwdqojjdneg',
    'adoceanhutehfnwmrob',
  ];
  games: Advertisement[] = [];
  readonly mainGame: Advertisement = {
    zonaId: 'adoceanhuqfolqtggia',
    medium: 'all',
    bannerName: 'games_page',
    pageType: 'games',
    priority: 10,
    masterId: 'QHkGiOyIklqUm5VBe3OU0BCPEAgPj0LaiPOwj3DirT3.E7',
    isAdultAd: false,
  };

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly seo: SeoService
  ) {
    this.siteUrl = this.portalConfig?.siteUrl;
    this.siteName = this.portalConfig?.portalName?.toLowerCase();
  }

  ngOnInit(): void {
    const defMeta = defaultMetaInfo(this.portalConfig);
    this.seo.setMetaData({
      ...defMeta,
      title: `${defMeta.ogSiteName} - Játékok`,
      ogTitle: `${defMeta.ogSiteName} - Játékok`,
    });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.games = this.makeGamesAds();
      this.canShowGames$.next(true); //Force delay for the game cards to not prevent other advertisements from loading.
    });
  }

  private makeGamesAds(): Advertisement[] {
    return this.gamesIds.map((id) => {
      return {
        zonaId: id,
        medium: 'all',
        bannerName: 'games_page',
        pageType: 'games',
        priority: 1,
        isAdultAd: false,
        masterId: 'QHkGiOyIklqUm5VBe3OU0BCPEAgPj0LaiPOwj3DirT3.E7',
      };
    });
  }
}
