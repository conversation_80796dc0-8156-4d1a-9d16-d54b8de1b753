<section class="galleries">
  <div class="wrap">
    <h1><PERSON><PERSON><PERSON><PERSON><PERSON></h1>

    <div class="row-gallery">
      <div class="galleries-highlighted">
        <app-gallery-card
          *ngFor="let highlightedGallery of highlightedGalleries; let i = index"
          [data]="highlightedGallery"
          [mobileClass]="'big'"
          [isBigFont]="i === 0 ? true : false"
          [routerLink]="['/', 'galeria', highlightedGallery.slug, 1]"
        >
        </app-gallery-card>
      </div>

      <app-section-header sectionTitle="További galériák"></app-section-header>

      <div class="galleries-list">
        <app-gallery-card
          *ngFor="let restGallery of restGalleries"
          [data]="restGallery"
          [mobileClass]="'small'"
          [routerLink]="['/', 'galeria', restGallery.slug, 1]"
          [@fadeInAnimation]
          [isSmallFont]="true"
        >
        </app-gallery-card>
      </div>
    </div>

    <app-pager
      [rowAllCount]="totalGalleryCount - HIGHLIGHTED_GALLERY_COUNT"
      [rowOnPageCount]="REST_GALLERY_COUNT"
      [currentPage]="currentPageIndex"
      [allowAutoScrollToTop]="false"
      (pageChange)="onPageChange($event)"
    ></app-pager>

    <app-galleries-bottom
      [relatedArticles]="relatedArticlesLeft"
      [adverts]="adverts"
      ad="medium_rectangle_1_top"
      [relatedArticlesSecondary]="relatedArticlesRight"
    ></app-galleries-bottom>
  </div>
</section>
