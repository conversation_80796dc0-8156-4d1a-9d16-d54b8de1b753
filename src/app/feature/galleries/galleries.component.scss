@use 'shared' as *;

.galleries {
  padding: 50px 0;

  .wrap {
    width: $global-wrapper-width;
    margin: 0 auto;
    max-width: calc(100% - 30px);
    @include media-breakpoint-down(md) {
      width: 100vw;
      max-width: 100%;
    }
  }

  h1 {
    position: relative;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 30px;
    line-height: 30px;
    padding: 15px 20px;
    margin-bottom: 42px;

    @include article-before-line();

    @include media-breakpoint-down(md) {
      margin-left: 15px;
    }
  }

  .row-gallery {
    display: flex;
    flex-wrap: wrap;
  }

  .galleries-highlighted {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    margin: 0 -20px 10px;
    @include media-breakpoint-down(md) {
      justify-content: center;
      margin: 0 0 10px;
    }

    app-gallery-card {
      height: 521px;
      margin: 0 20px 36px;
      width: calc(33.3333% - 40px);
      max-width: 100%;

      @include media-breakpoint-down(md) {
        height: auto;
        width: 100%;
        margin: 0 0 36px;
      }

      &:first-child {
        @include media-breakpoint-up(lg) {
          width: calc(66.6666% - 40px);

          .gallery-card {
            padding: 0 80px 46px;
          }
        }
      }
    }
  }

  app-section-header {
    width: 100%;
  }

  .galleries-list {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    margin: 0 -20px 10px;
    // min-width: 100%;
    min-width: calc(100% + 40px);
    @include media-breakpoint-down(md) {
      justify-content: center;
      width: 100%;
    }
    @include media-breakpoint-down(sm) {
      margin: 0px;
      justify-content: flex-start;
    }

    app-gallery-card {
      height: 372px;
      margin: 0 20px 36px;
      width: calc(25% - 40px);
      max-width: 100%;
      display: block;

      @include media-breakpoint-down(lg) {
        height: auto;
        width: calc(33.3333% - 40px);
        .gallery-card {
          padding-top: 30px;
        }
      }

      @include media-breakpoint-down(sm) {
        width: 100%;
        margin: 0 0 25px 0;
      }
    }
  }

  app-pager {
    display: flex;
    justify-content: center;
    margin-bottom: $block-bottom-margin;

    @include media-breakpoint-down(md) {
      margin-bottom: $block-bottom-margin-mobile;
    }
  }
}
