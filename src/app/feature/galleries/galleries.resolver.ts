import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService } from '../article/article.service';
import { ALL_GALLERY_COUNT } from './galleries.constants';
import { GalleryApiService } from '../../shared';
import { GalleriesPageData } from './galleries.definitions';

const MAX_RELATED_ARTICLE_LEFT = 4;
const MAX_RELATED_ARTICLE_RIGHT = 6;

@Injectable()
export class GalleriesResolver {
  constructor(
    private readonly galleryApiService: GalleryApiService,
    private readonly router: Router,
    private readonly articleService: ArticleService
  ) {}

  resolve(): Observable<GalleriesPageData> {
    return forkJoin({
      galleryResult: this.galleryApiService.getGalleries(0, ALL_GALLERY_COUNT),
      relatedArticles: this.articleService.getLatestArticles(MAX_RELATED_ARTICLE_LEFT + MAX_RELATED_ARTICLE_RIGHT),
    }).pipe(
      map(({ galleryResult, relatedArticles: { data: articles } }) => ({
        galleries: galleryResult.galleries ?? [],
        totalGalleryCount: galleryResult.totalCount,
        relatedArticlesLeft: (articles ?? []).slice(0, MAX_RELATED_ARTICLE_LEFT),
        relatedArticlesRight: (articles ?? []).slice(MAX_RELATED_ARTICLE_LEFT),
      })),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
