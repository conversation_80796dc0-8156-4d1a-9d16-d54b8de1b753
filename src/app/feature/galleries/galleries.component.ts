import { animate, style, transition, trigger } from '@angular/animations';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  GalleryCard,
  mapGalleryToGalleryCard,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { ALL_GALLERY_COUNT, HIGHLIGHTED_GALLERY_COUNT, REST_GALLERY_COUNT } from './galleries.constants';
import { SeoService } from '@trendency/kesma-core';
import { NgFor } from '@angular/common';
import {
  defaultMetaInfo,
  GalleriesBottomComponent,
  GalleryApiService,
  GalleryCardComponent,
  List,
  PagerComponent,
  PortalConfigService,
  SectionHeaderComponent,
} from '../../shared';


@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrls: ['./galleries.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [trigger('fadeInAnimation', [transition(':enter', [style({ opacity: 0 }), animate('.300s ease-out', style({ opacity: 1 }))])])],
  imports: [NgFor, GalleryCardComponent, RouterLink, SectionHeaderComponent, PagerComponent, GalleriesBottomComponent],
})
export class GalleriesComponent implements OnInit, OnDestroy {
  readonly REST_GALLERY_COUNT = REST_GALLERY_COUNT;
  readonly HIGHLIGHTED_GALLERY_COUNT = HIGHLIGHTED_GALLERY_COUNT;

  highlightedGalleries: List<GalleryCard> = [];
  restGalleries: List<GalleryCard> = [];
  relatedArticlesLeft: ArticleCard[] = [];
  relatedArticlesRight: ArticleCard[] = [];
  currentPageIndex = 0;
  totalGalleryCount: number;
  adPageType = 'gallery';

  adverts: AdvertisementsByMedium;
  private readonly unsubscribe$ = new Subject<boolean>();

  constructor(
    private readonly galleryApiService: GalleryApiService,
    private readonly route: ActivatedRoute,
    private readonly portalConfig: PortalConfigService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly seo: SeoService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        switchMap(({ pageData: { galleries, totalGalleryCount, relatedArticlesLeft, relatedArticlesRight } }): Observable<Advertisement[]> => {
          this.highlightedGalleries = galleries.map(mapGalleryToGalleryCard).slice(0, HIGHLIGHTED_GALLERY_COUNT);
          this.restGalleries = galleries.map(mapGalleryToGalleryCard).slice(HIGHLIGHTED_GALLERY_COUNT, ALL_GALLERY_COUNT);
          this.totalGalleryCount = totalGalleryCount;
          this.relatedArticlesLeft = relatedArticlesLeft;
          this.relatedArticlesRight = relatedArticlesRight;

          this.seo.setMetaData({
            ...defaultMetaInfo(this.portalConfig),
            ogTitle: `${this.portalConfig?.portalName} - Galériák`,
          });

          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads, this.adPageType)),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  onPageChange(pageNumber: number): void {
    this.currentPageIndex = pageNumber - 1;
    this.fetchRestGalleries(this.currentPageIndex);
  }

  private fetchRestGalleries(pageIndex: number): void {
    const from = HIGHLIGHTED_GALLERY_COUNT + REST_GALLERY_COUNT * pageIndex;
    const count = REST_GALLERY_COUNT;

    this.galleryApiService
      .getGalleries(from, count)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(({ galleries, totalCount }) => {
        this.restGalleries = galleries.map(mapGalleryToGalleryCard);
        this.totalGalleryCount = totalCount;
        this.changeRef.detectChanges();
      });
  }
}
