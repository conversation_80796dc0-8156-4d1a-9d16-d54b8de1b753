<kesma-layout
  [breakingNews]="breakingNews"
  [blockTitleRef]="blockTitles"
  [configuration]="configuration"
  [contentComponentsRef]="contentComponents"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [layoutType]="layoutType"
  [structure]="structure"
  [adPageType]="adPageType"
  [editorFrameSize]="editorFrameSize"
>
</kesma-layout>

<ng-template #blockTitles let-layoutElement="layoutElement">
  @if (layoutType !== layoutPageTypes.SIDEBAR) {
    <app-block-title-row [data]="layoutElement.blockTitle"></app-block-title-row>
  } @else {
    <app-block-title-sidebar [data]="layoutElement.blockTitle"></app-block-title-sidebar>
  }
</ng-template>

<ng-template #contentComponents let-index="index" let-layoutElement="layoutElement" let-desktopWidth="desktopWidth">
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article && layoutElement.config">
    <app-article-card
      *ngIf="layoutElement.extractorData?.[index] as data"
      [article]="data"
      [isSidebar]="layoutType === layoutPageTypes.SIDEBAR"
      [styleID]="layoutElement.styleId"
      [widthDesktop]="desktopWidth"
    >
    </app-article-card>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Note">
    <app-note-card *ngIf="layoutElement.extractorData?.[index] as data" [data]="data"></app-note-card>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Gallery && index === 0">
    <app-gallery-block *ngIf="layoutElement.extractorData as data" [data]="data"></app-gallery-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Tabs">
    <app-tabs-block *ngIf="layoutElement.extractorData as data" [data]="data"></app-tabs-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
    <app-voting-box *ngIf="layoutElement.extractorData?.data as data" [voting]="data" type="layout"></app-voting-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Dossier && layoutElement.config">
    @if (layoutElement.extractorData?.[0]?.sponsorship) {
      <app-dossier-sponsoration-header [data]="layoutElement.extractorData?.[0]"></app-dossier-sponsoration-header>
    }
    <app-dossier-card
      *ngIf="layoutElement.extractorData as data"
      [dossiers]="data"
      [preferMainArticleThumbnail]="true"
      [styleID]="layoutElement.styleId"
      [widthDesktop]="desktopWidth"
    >
    </app-dossier-card>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Breaking && layoutElement.config">
    <app-breaking-block *ngIf="layoutElement.extractorData?.[index] as data" [data]="data"></app-breaking-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PrBlock && layoutElement.config">
    <app-article-card
      *ngIf="layoutElement.extractorData?.[index] as data"
      [article]="data"
      [isSidebar]="layoutType === layoutPageTypes.SIDEBAR"
      [styleID]="layoutElement.styleId"
    >
    </app-article-card>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Astrology">
    <app-astrology-box></app-astrology-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <app-real-estate-bazaar-block></app-real-estate-bazaar-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed && layoutElement.config">
    <app-html-embed [data]="layoutElement?.config?.htmlContent"></app-html-embed>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PROGRAM">
    <app-program-block *ngIf="layoutElement.extractorData?.[index] as data" [programs]="data"></app-program-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Image">
    <app-image-box *ngIf="layoutElement.extractorData as data" [layoutImageData]="data"></app-image-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.AGROKEP_LIST">
    <app-agro-list-card></app-agro-list-card>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.TENYEK_BOX">
    <app-tenyek-box></app-tenyek-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT && index === 0">
    <app-podcast-block *ngIf="layoutElement.extractorData as data" [buttonUrl]="layoutElement.config.btnUrl" [data]="data"></app-podcast-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
    <app-external-branding-box
      [brand]="layoutElement?.brand"
      [isSidebar]="layoutType === layoutPageTypes.SIDEBAR || desktopWidth < 5"
    ></app-external-branding-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Wysiwyg">
    <kesma-wysiwyg-box *ngIf="layoutElement.extractorData as data" [htmlArray]="data"></kesma-wysiwyg-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad && isMobile && layoutElement?.ad?.medium === 'mobile'">
    <kesma-advertisement-adocean
      *ngIf="layoutElement.ad as ad"
      [ad]="ad"
      [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
    >
    </kesma-advertisement-adocean>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Ad && !isMobile && layoutElement?.ad?.medium === 'desktop'">
    <kesma-advertisement-adocean
      *ngIf="layoutElement.ad as ad"
      [ad]="ad"
      [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
    >
    </kesma-advertisement-adocean>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.JOB_LISTINGS">
    <app-job-listings-adapter></app-job-listings-adapter>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX">
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.SECRET_DAYS_CALENDAR">
    <app-secret-days-calendar-adapter
      *ngIf="layoutElement.config.selectedCalendar"
      [id]="layoutElement.config.selectedCalendar.id"
    ></app-secret-days-calendar-adapter>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>
</ng-template>
