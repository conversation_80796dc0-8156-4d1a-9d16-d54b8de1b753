import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges, TemplateRef } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  BlockWrapperTemplateData,
  BreakingNews,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutElement,
  LayoutElementColumn,
  LayoutElementContent,
  LayoutElementContentAd,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutElementType,
  LayoutPageType,
  PAGE_TYPES,
  provideLayoutDataExtractors,
  SecondaryFilterAdvertType,
  SponsoredBoxComponent,
  WysiwygBoxComponent,
} from '@trendency/kesma-ui';
import { Advertisement } from '@trendency/kesma-ui/lib/definitions/advertisement.definitions';
import { NgIf } from '@angular/common';
import {
  AgroListCardComponent,
  ArticleCardComponent,
  AstrologyBoxComponent,
  BlockTitleRowComponent,
  BlockTitleSidebarComponent,
  BreakingBlockComponent,
  DossierCardComponent,
  ExternalBrandingBoxComponent,
  GalleryBlockComponent,
  HtmlEmbedComponent,
  ImageBoxComponent,
  JobListingsAdapterComponent,
  NoteCardComponent,
  PodcastBlockComponent,
  ProgramBlockComponent,
  RealEstateBazaarBlockComponent,
  TabsBlockComponent,
  TenyekBoxComponent,
  VotingBoxComponent,
  SecretDaysCalendarAdapterComponent,
} from '../../../../shared';
import { MEGYEI_EXTRACTORS_CONFIG } from '../../extractors/extractor.config';

import { DossierSponsorationHeaderComponent } from '../../../../shared/components/dossier-sponsoration-header/dossier-sponsoration-header.component';
import { VariableSponsoredDidYouKnowWrapperComponent } from '../../../../shared/components/variable-sponsored-did-you-know-wrapper/variable-sponsored-did-you-know-wrapper.component';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    KesmaLayoutComponent,
    BlockTitleRowComponent,
    BlockTitleSidebarComponent,
    NgIf,
    ArticleCardComponent,
    NoteCardComponent,
    GalleryBlockComponent,
    TabsBlockComponent,
    VotingBoxComponent,
    DossierCardComponent,
    BreakingBlockComponent,
    AstrologyBoxComponent,
    RealEstateBazaarBlockComponent,
    HtmlEmbedComponent,
    ProgramBlockComponent,
    ImageBoxComponent,
    AgroListCardComponent,
    TenyekBoxComponent,
    PodcastBlockComponent,
    ExternalBrandingBoxComponent,
    WysiwygBoxComponent,
    AdvertisementAdoceanComponent,
    JobListingsAdapterComponent,
    SponsoredBoxComponent,
    DossierSponsorationHeaderComponent,
    SecretDaysCalendarAdapterComponent,
    VariableSponsoredDidYouKnowWrapperComponent,
  ],
  providers: [provideLayoutDataExtractors(MEGYEI_EXTRACTORS_CONFIG, true)],
})
export class LayoutComponent implements OnInit, OnChanges {
  readonly layoutPageTypes = LayoutPageType;
  @Input() adPageType: string = PAGE_TYPES.all_articles_and_sub_pages;
  @Input() hasFallbackAd = true;
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() hasAdBackground = false;
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';
  dataMapped: boolean;
  adZones: any[];
  isMobile: boolean = typeof window !== 'undefined' ? window?.innerWidth <= 768 : false;

  LayoutElementContentType = LayoutElementContentType;

  constructor(
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (this.structure && this.configuration) {
      this.mapNode(this.structure);
      this.dataMapped = true;
    }
    this.adStoreAdo.advertisemenets$.subscribe((ads) => {
      this.adZones = ads;
      this.initADZones(this.structure);
      this.changeRef.detectChanges();
    });
  }

  initADZones(elements: LayoutElement[]): void {
    if (elements) {
      elements.forEach((el) => {
        // recursions on row & column children
        if (el.type === LayoutElementType.Row || el.type === LayoutElementType.Column) {
          this.initADZones((el as LayoutElementRow | LayoutElementColumn).elements);
        } else if ((el as LayoutElementContent).contentType === LayoutElementContentType.Ad) {
          const ad = this.getAd(el as LayoutElementContentAd);
          (el as LayoutElementContentAd).ad = ad;
        }
      });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes['structure'].firstChange) {
      this.initADZones(this.structure);
    }

    if (changes['structure']?.currentValue && changes['configuration']?.currentValue) {
      this.dataMapped = false;
      this.mapNode(this.structure);
      this.dataMapped = true;
    }
  }

  mapNode(elements: LayoutElement[]): void {
    elements.forEach((el) => {
      // recursions on row & column children
      if (el.type === LayoutElementType.Row || el.type === LayoutElementType.Column) {
        this.mapNode((el as LayoutElementRow | LayoutElementColumn).elements);
      } else {
        const config = this.getConfig(el as LayoutElementContent);
        (el as LayoutElementContent).config = config;
      }
    });
  }

  getConfig(element: LayoutElementContent): LayoutElementContentConfiguration | undefined {
    return this.configuration.find((c) => c?.layoutElementId === element?.id);
  }

  getAd(element: LayoutElementContentAd): Advertisement | undefined {
    const zoneIds = this.adStoreAdo.separateAdsByMedium(this.adZones, this.adPageType, [element?.bannerName], SecondaryFilterAdvertType.REPLACEABLE);

    if (element?.medium === 'desktop' && zoneIds?.desktop) {
      return zoneIds?.desktop[element?.bannerName];
    }
    if (element?.medium === 'mobile' && zoneIds?.mobile) {
      return zoneIds?.mobile[element?.bannerName];
    }
    return undefined;
  }
}
