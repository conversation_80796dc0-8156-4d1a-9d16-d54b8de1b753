import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { forkJoin, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { ArticleCard, Layout, LayoutContent, LayoutElementRow, LayoutPageType, PAGE_TYPES } from '@trendency/kesma-ui';
import { LayoutComponent } from '../layout/layout.component';
import { NgIf } from '@angular/common';
import { ApiService, mapSidebarData } from '../../../../shared';
import { EmbeddingService } from '@trendency/kesma-core';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, LayoutComponent],
})
export class SidebarComponent implements OnInit, OnChanges, AfterViewInit {
  readonly layoutPageType = LayoutPageType.SIDEBAR;
  @Input() adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  @Input() hasFallbackAd = true;
  @Input() categorySlug: string;
  @Input() articleId: string;
  @Input() articleSlug: string;
  @Input() columnSlug: string;
  @Input() excludedIds: string[] = [];
  layoutApiData: Layout;
  layoutReady = false;

  constructor(
    private readonly api: ApiService,
    private readonly embedding: EmbeddingService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.populateSidebar();
  }

  get structure(): LayoutElementRow[] {
    return this.layoutApiData.struct as LayoutElementRow[];
  }

  populateSidebar(): void {
    const allExcludedId = [...(this.articleId ? [this.articleId] : []), ...this.excludedIds];
    this.api
      .getSidebar(this.categorySlug, this.articleSlug)
      .pipe(
        mergeMap((layoutApiResponse) =>
          forkJoin([
            of(layoutApiResponse),
            this.api.getSidebarArticleRecommendations(this.getArticleCount(layoutApiResponse?.data?.content), this.categorySlug, allExcludedId),
            this.api.getSidebarOpinionRecommendations(this.getOpinionCount(layoutApiResponse?.data?.content), this.categorySlug, allExcludedId),
          ])
        )
      )
      .subscribe(([{ data: layoutApiData }, { data: recommendedArticles }, { data: recommendedOpinions }]) => {
        this.layoutApiData = mapSidebarData(layoutApiData);

        this.fillLayoutContent(recommendedArticles, recommendedOpinions);
        this.layoutReady = true;
        this.embedding.loadEmbedMedia();
        this.changeRef.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // need sidebar refetch to avoid showing the same article in sidebar that is currently displayed on detail page
    if (
      (changes['articleId']?.currentValue && !changes['articleId']?.firstChange) ||
      (changes['excludedIds']?.currentValue && !changes['excludedIds']?.firstChange)
    ) {
      this.populateSidebar();
    }
  }

  getArticleCount(content: LayoutContent[] | undefined): number {
    if (!content) {
      return 0;
    }
    return content.map(({ selectedArticles }) => selectedArticles).reduce((acc, val) => acc.concat(val), []).length;
  }

  getOpinionCount(content: LayoutContent[] | undefined): number {
    if (!content) {
      return 0;
    }
    return content.map(({ selectedOpinions }) => selectedOpinions).reduce((acc, val) => acc.concat(val), []).length;
  }

  fillLayoutContent(articles: ArticleCard[], opinions: ArticleCard[]): void {
    let articleCursor = 0;
    let opinionCursor = 0;
    this.layoutApiData.content.forEach(({ selectedArticles, selectedOpinions }) => {
      if (selectedArticles?.length) {
        for (let i = 0; i < selectedArticles.length; i++) {
          // only overwrite null values
          if (!selectedArticles[i] && articles[articleCursor]) {
            selectedArticles[i] = {
              id: articles[articleCursor]?.id ?? '',
              // TODO: mismatching types????
              data: articles[articleCursor] as any,
            };
            articleCursor++;
          }
        }
      } else if (selectedOpinions?.length) {
        for (let i = 0; i < selectedOpinions.length; i++) {
          // only overwrite null values
          if (selectedOpinions[i]) {
            selectedOpinions[i] = {
              id: opinions[opinionCursor]?.id ?? '',
              // TODO: mismatching types????
              data: opinions[opinionCursor] as any,
            };
            opinionCursor++;
          }
        }
      }
    });
  }
}
