import { Injectable } from '@angular/core';
import { DataExtractorFunction, LayoutDataExtractorService, LayoutElementContent } from '@trendency/kesma-ui';
import { BackendProgramBlock } from '../../../shared';
import { LayoutElementContentConfigurationProgramBlock } from '../layout.definitions';

@Injectable()
export class ProgramExtractor implements LayoutDataExtractorService<BackendProgramBlock[] | undefined> {
  extractData: DataExtractorFunction<BackendProgramBlock[] | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationProgramBlock;

    if (!conf || !conf.selectedPrograms?.length) {
      return;
    }

    return {
      data: conf.selectedPrograms.map(({ data }) => data),
      meta: {
        extractedBy: ProgramExtractor.name,
      },
    };
  };
}
