@use 'shared' as *;

section.grief {
  .wrapper {
    .left-column {
      .page-title {
        font-size: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 5px 0 5px 20px;
        margin-bottom: 50px;
        margin-top: -5px;
        @include article-before-line();

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }

        @include media-breakpoint-down(sm) {
          margin-top: 30px;
          margin-bottom: 14px;
          flex-wrap: wrap;
          padding-left: 0;
          padding-top: 0;

          &:before {
            height: 60px;
          }
        }

        .left {
          @include media-breakpoint-down(sm) {
            width: 100%;
          }

          .page-title-text {
            font-weight: 500;
            font-size: 30px;
            line-height: 30px;
            padding-right: 5px;

            &,
            &:visited,
            &:active,
            &:link {
              color: $base-text-color;
            }

            @include media-breakpoint-down(sm) {
              font-size: 20px;
              line-height: 30px;
              height: 60px;
              display: flex;
              align-items: center;
              padding: 0 0 0 20px;
            }
          }
        }

        .right {
          display: flex;
          justify-content: flex-end;

          @include media-breakpoint-down(sm) {
            justify-content: flex-start;
            width: 100%;
            flex-wrap: wrap;
            .search-input {
              margin: 10px 0;
              width: 100%;
            }
          }
        }
      }

      .search-details {
        display: flex;
        align-items: center;
        margin-bottom: 40px;

        .clear-search {
          width: 50px;
          height: 50px;
          background: $grey-22;
          border: 1px solid $grey-16;
          box-sizing: border-box;
          border-radius: 6px;
          margin-right: 17px;
          @include icon('icons/arrow-left.svg');
          background-size: 40%;
        }

        .details-text {
          font-weight: 500;
          font-size: 14px;
          line-height: 21px;
          color: $grey-7;
        }
      }

      .results {
        display: flex;
        flex-wrap: wrap;
        margin-top: 96px;
        justify-content: space-between;

        .grief-card {
          border: 1px solid $grey-25;
          border-radius: 6px;
          width: calc(50% - 20px);
          margin-bottom: 40px;
          padding: 20px 40px;
          @include media-breakpoint-down(sm) {
            width: 100%;
            padding: 20px 30px;
          }

          &.full-width {
            width: 100%;
          }

          .lead {
            margin: 10px 0 15px 0;
            font-weight: 600;
          }

          .grief-card-content {
            display: block;

            .image {
              display: block;
              float: left;
              height: 150px;
              width: 120px;
              margin: 0 25px 10px 0;
              background-size: cover;
              background-position: center;
            }

            .content-text {
              font-family: $font-zilla;
              font-weight: normal;
              font-size: 20px;
              line-height: 28px;
              display: block;
              margin-bottom: 13px;
              @include media-breakpoint-down(sm) {
                font-size: 20px;
                line-height: 30px;
                margin-bottom: 14px;
              }
            }
          }

          .informations {
            font-weight: normal;
            font-size: 14px;
            line-height: 19px;
            display: block;
          }

          .date {
            margin-top: 18px;
            font-size: 14px;
            line-height: 19px;
            display: block;
            text-align: right;
            color: $grey-7;
          }
        }

        .highlighted {
          border: 5px solid black;
        }
      }

      app-pager {
        display: flex;
        justify-content: center;
        margin-bottom: 50px;
      }
    }

    aside {
      margin-top: -30px;
    }
  }
}
