import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, RedirectService } from '@trendency/kesma-ui';
import { forkJoin, Observable, Subject, Subscription } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { GriefItem, GriefResponse } from './grief.definitions';
import { FormatDatePipe, SafePipe, SeoService } from '@trendency/kesma-core';
import { NgClass, NgFor, NgIf, NgStyle } from '@angular/common';
import { ApiService, ArticleCardTypes, defaultMetaInfo, PagerComponent, PortalConfigService } from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-grief',
  templateUrl: './grief.component.html',
  styleUrls: ['./grief.component.scss'],
  imports: [
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    NgIf,
    AdvertisementAdoceanComponent,
    NgFor,
    NgClass,
    NgStyle,
    PagerComponent,
    SidebarComponent,
    SafePipe,
    FormatDatePipe,
  ],
})
export class GriefComponent implements OnInit, OnDestroy {
  readonly articleCardType = ArticleCardTypes;
  readonly placeholderList = [];
  griefList: GriefItem[];
  subscription: Subscription;
  searchForm: UntypedFormGroup;
  searchValue: string;
  isSearchPressed = false;
  totalCount: number;
  currentPage = 1;
  readonly START_FROM_ONE = 1;
  adPageType = 'grief';
  adverts: AdvertisementsByMedium | undefined;
  private readonly destroy$ = new Subject<boolean>();
  private readonly categorySlug = 'gyasz';
  isHighlighted = 1;

  constructor(
    private readonly apiService: ApiService,
    private readonly portalConfig: PortalConfigService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly seo: SeoService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly redirectService: RedirectService
  ) {}

  ngOnInit(): void {
    this.searchForm = new UntypedFormGroup({
      dateOrderSelector: new UntypedFormControl('newer'),
      searchField: new UntypedFormControl(''),
    });

    this.subscription = this.searchForm?.controls['dateOrderSelector']?.valueChanges.pipe().subscribe((dateOrderSelector: string) => {
      this.griefList = this.sortAdsByDate(this.griefList, dateOrderSelector);
      this.changeRef.detectChanges();
    });

    this.route.queryParams
      .pipe(
        switchMap((data) => {
          this.searchValue = data?.['search'];
          this.isSearchPressed = !!this.searchValue;
          this.searchForm.controls['searchField'].setValue(this.searchValue);
          if (this.searchValue) {
            return forkJoin({ data: this.apiService.griefSearch(this.searchValue, this.categorySlug) });
          }
          const firstPage = 1;
          this.currentPage = data?.['page'] || firstPage;
          return this.apiService.getGriefList(this.currentPage);
        }),
        switchMap((response: GriefResponse): Observable<Advertisement[]> => {
          if (this.currentPage > 1 && this.redirectService.shouldBeRedirect(this.currentPage, response?.data ?? [])) {
            this.redirectService.redirectOldUrl(`gyasz`, false, 302);
          }
          const order = this.searchForm.controls['dateOrderSelector'].value;
          const sortedList = this.sortAdsByDate(response?.data ?? [], order);
          this.griefList = sortedList;
          this.totalCount = parseInt(response?.totalCount ?? '0', 10);

          return this.adStoreAdo.advertisemenets$;
        }),
        tap(() => this.resetAds()),
        map((ads: Advertisement[]): AdvertisementsByMedium => this.adStoreAdo.separateAdsByMedium(ads, this.adPageType)),
        takeUntil(this.destroy$)
      )
      .subscribe((ads: AdvertisementsByMedium): void => {
        this.adverts = ads;

        this.seo.setMetaData({
          ...defaultMetaInfo(this.portalConfig),
          ogTitle: `${this.portalConfig?.portalName} - Gyász`,
        });
        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  onSelectPage(pageNumber: number): void {
    this.currentPage = pageNumber;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: this.currentPage },
    });
  }

  onSearchButtonClick(): void {
    const searchValue = this.searchForm.controls['searchField']?.value;

    if (!searchValue) {
      this.isSearchPressed = false;
      this.router.navigate([], {
        relativeTo: this.route,
      });
      return;
    }
    this.searchValue = searchValue;
    this.isSearchPressed = true;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { search: searchValue },
    });
  }

  onClearSearch(): void {
    if (this.isSearchPressed) {
      this.router.navigate([], {
        relativeTo: this.route,
      });
    }
    this.searchForm.controls['searchField'].setValue('');
    this.searchValue = '';
    this.isSearchPressed = false;
  }

  resetAds(): void {
    this.adverts = undefined;
    this.changeRef.detectChanges();
  }

  private sortAdsByDate(griefList: GriefItem[], order: string): GriefItem[] {
    switch (order) {
      case 'newer': {
        const newer = griefList?.sort((a: GriefItem, b: GriefItem) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        return newer;
      }
      case 'older': {
        const older = griefList?.sort((a: GriefItem, b: GriefItem) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        return older;
      }
      default:
        return griefList;
    }
  }
}
