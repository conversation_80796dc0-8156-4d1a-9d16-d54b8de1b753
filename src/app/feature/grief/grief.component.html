<section class="grief">
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="page-title">
        <div class="left">
          <a [routerLink]="['/', 'gyasz']" class="page-title-text">GYÁSZHÍREK</a>
        </div>
        <form [formGroup]="searchForm">
          <div class="right">
            <div class="search-input">
              <button (click)="onSearchButtonClick()" class="search-button"></button>
              <input formControlName="searchField" type="text" placeholder="Keresés név szerint" />
              <button (click)="onClearSearch()" class="clear-button"></button>
            </div>

            <div class="custom-select-box-flyer">
              <label class="custom-select-label-flyer">Rendezés</label>
              <select formControlName="dateOrderSelector" class="custom-select-flyer">
                <option value="newer">Leg<PERSON><PERSON><PERSON><PERSON> elöl</option>
                <option value="older"><PERSON>g<PERSON><PERSON><PERSON><PERSON></option>
              </select>
              <i class="flyer-icon"></i>
            </div>
          </div>
        </form>
      </div>

      <div class="search-details">
        <button (click)="onClearSearch()" *ngIf="isSearchPressed" class="clear-search"></button>
        <p class="details-text" *ngIf="isSearchPressed">{{ this.griefList?.length ? this.griefList.length : 0 }} találat „{{ searchValue }}” kifejezésre</p>
      </div>

      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>

      <div class="results">
        <!-- half width list for normal view -->
        <ng-container *ngIf="!searchValue; else searchResult">
          <div class="grief-card" [ngClass]="{ highlighted: card.highlighted === isHighlighted }" *ngFor="let card of griefList">
            <p class="lead" *ngIf="card?.lead">{{ card?.lead }}</p>
            <div class="grief-card-content">
              <div *ngIf="card?.pictureFullSizeUrl" class="image" [ngStyle]="{ 'background-image': 'url(' + card.pictureFullSizeUrl + ')' }"></div>
              <p class="content-text" [innerHTML]="card?.description | safe: 'html'"></p>
            </div>
            <div class="informations">
              <p class="date">{{ card?.created_at ?? '' | formatDate: 'l-d' }}</p>
            </div>
          </div>
        </ng-container>

        <!-- full width list for filterd view -->
        <ng-template #searchResult>
          <div class="grief-card full-width" *ngFor="let card of griefList">
            <p class="content-text" [innerHTML]="card?.description | safe: 'html'"></p>
            <div class="informations">
              <!-- {{ card?.search_index }} -->
              <p class="date">{{ card?.created_at ?? '' | formatDate: 'l-d' }}</p>
            </div>
          </div>
        </ng-template>
      </div>

      <app-pager
        *ngIf="!searchValue"
        [currentPage]="currentPage - START_FROM_ONE"
        [rowAllCount]="totalCount"
        [rowOnPageCount]="10"
        (pageChange)="onSelectPage($event)"
      >
      </app-pager>

      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
    </div>
    <aside>
      <app-sidebar [hasFallbackAd]="false" [adPageType]="adPageType"></app-sidebar>
    </aside>
  </div>
</section>
