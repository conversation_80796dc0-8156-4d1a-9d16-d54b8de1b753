export type GriefItem = Readonly<{
  id: string;
  category_id: string;
  category_name: string;
  picture_id: string;
  title: string;
  lead: string;
  description: string;
  ad_city: string;
  source_type: string;
  source_name: string;
  source_id: string;
  external_type: string;
  external_id: string;
  active: string;
  highlighted: number;
  start_date: string;
  end_date: string;
  created_at: string;
  search_index: string;
  is_grief: string;
  pictureFullSizeUrl: string;
}>;

export type GriefResponse = Readonly<{
  totalCount?: string;
  totalPage?: string;
  data?: GriefItem[];
}>;

export type SearchForm = Readonly<{
  dateOrderSelector: string;
  nameField: string;
}>;
