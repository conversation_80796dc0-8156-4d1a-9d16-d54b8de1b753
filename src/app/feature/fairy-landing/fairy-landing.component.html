<section class="fairy-landing">
  <div class="wrapper">
    <div class="page-title">
      <span class="main-title">{{ indexData?.title || 'Tünd<PERSON>rszépek' }}</span>
    </div>
    <div class="fairy-landing-top">
      <div class="left">
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"> </kesma-advertisement-adocean>

        <div class="content" [innerHTML]="indexData?.lead | safe: 'html'"></div>
      </div>

      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>

      <div class="right">
        <div class="info-box">
          <p class="title">értékelés leadás</p>
          <div class="info-w-icon">
            <i class="icon icon-calendar-heart"></i>
            <p>Március 22-ig lehet <PERSON>eket leadni</p>
          </div>
        </div>
        <div class="info-box">
          <p class="title">Ajándéksorsolás</p>
          <div class="info-w-icon">
            <i class="icon icon-gift"></i>
            <p>Az értékelések leadói közötti első ajándéksorsolás pedig március 23-án lesz.</p>
          </div>
        </div>
      </div>
    </div>

    <ng-container *ngIf="isOverview; else countyView">
      <app-section-header
        sectionTitle="Döntősök"
        [link]="['/', 'tunderszepek', 'megye', 'dontosok']"
        linkTitle="Tovább az összes döntősre"
      ></app-section-header>
      <div class="fairy-list">
        <div class="col-12 col-md-6 col-lg-3" *ngFor="let participant of indexData.finalists" [@fadeInAnimation]>
          <app-fairy-card [styleID]="fairyCardType.SMALL_CARD_NO_VOTE" [data]="participant"></app-fairy-card>
        </div>
      </div>

      <ng-container *ngFor="let countyData of indexData.counties">
        <app-section-header
          [sectionTitle]="countyData.county"
          [link]="['/', 'tunderszepek', 'megye', countyData.data[0].portal]"
          [linkTitle]="'Tovább az összes ' + countyData.county + ' megyei döntősre'"
        ></app-section-header>
        <div class="fairy-list">
          <div class="col-12 col-md-6 col-lg-3" *ngFor="let participant of countyData.data" [@fadeInAnimation]>
            <app-fairy-card [styleID]="fairyCardType.SMALL_CARD_NO_VOTE" [data]="participant"></app-fairy-card>
          </div>
        </div>
      </ng-container>
    </ng-container>

    <ng-template #countyView>
      <app-section-header sectionTitle="Versenyzők"></app-section-header>
      <div class="fairy-list">
        <div class="col-12 col-md-6 col-lg-4" *ngFor="let participant of mainParticipants">
          <app-fairy-card [styleID]="fairyCardType.BIG_CARD_WITH_VOTE" [data]="participant"></app-fairy-card>
        </div>
      </div>

      <app-section-header sectionTitle="További versenyzők"></app-section-header>
      <div class="fairy-list">
        <div class="col-12 col-md-6 col-lg-3" *ngFor="let participant of displayedOtherParticipants" [@fadeInAnimation]>
          <app-fairy-card [styleID]="fairyCardType.SMALL_CARD_NO_VOTE" [data]="participant"></app-fairy-card>
        </div>
      </div>
    </ng-template>

    <app-pager
      [currentPage]="currentPage"
      [rowAllCount]="itemCount"
      [rowOnPageCount]="maxPerPage"
      [allowAutoScrollToTop]="false"
      (pageChange)="onPageChange($event)"
    ></app-pager>

    <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>

    <app-galleries-bottom
      [previousCycles]="previousCycles"
      [relatedArticles]="relatedArticles"
      [adverts]="adverts"
      ad="medium_rectangle_4_bottom"
    ></app-galleries-bottom>
  </div>
</section>
