@use 'shared' as *;

.fairy-landing {
  padding-top: 50px;
  padding-bottom: 50px;
  width: 100%;
  @include media-breakpoint-down(md) {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .wrapper {
    .page-title {
      font-weight: 500;
      font-size: 30px;
      line-height: 30px;
      display: flex;
      align-items: center;
      position: relative;
      padding: 20px;
      text-transform: uppercase;
      margin-bottom: 30px;
      @include article-before-line();

      &:before {
        background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
      }
    }

    .fairy-landing-top {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 30px;

      .left {
        width: calc(100% - 440px - 50px);
        @include media-breakpoint-down(md) {
          width: 100%;
          display: flex;
          justify-content: center;
        }

        .lead,
        .content p:first-of-type {
          font-style: normal;
          font-weight: 300;
          font-size: 30px;
          line-height: 50px;
          @include media-breakpoint-down(md) {
            font-size: 20px;
            line-height: 30px;
            margin-bottom: 20px;
          }
        }

        .content {
          p {
            font-weight: normal;
            font-size: 16px;
            line-height: 26px;
            margin-bottom: 20px;
          }
        }
      }

      .right {
        width: 440px;
        @include media-breakpoint-down(md) {
          width: 100%;
        }

        .info-box {
          border: 1px solid $grey-16;
          border-radius: 6px;
          margin-bottom: 15px;
          padding: 40px;
          @include media-breakpoint-down(sm) {
            padding: 15px;
          }

          .title {
            font-weight: 500;
            font-size: 16px;
            line-height: 140%;
            text-transform: uppercase;
            display: block;
          }

          .info-w-icon {
            padding-left: 80px;
            margin-top: 12px;
            position: relative;

            .icon {
              width: 48px;
              height: 48px;
              position: absolute;
              top: 0;
              left: 0;

              &.icon-calendar-heart {
                @include icon('icons/calendar-heart.png');
              }

              &.icon-gift {
                @include icon('icons/gift-box.png');
              }
            }

            p {
              font-size: 20px;
              line-height: 30px;
            }
          }
        }
      }
    }

    .fairy-list {
      display: flex;
      margin-left: -15px;
      margin-right: -15px;
      width: calc(100% + 30px);
      flex-wrap: wrap;
    }

    app-pager {
      display: flex;
      justify-content: center;
      margin-bottom: $block-bottom-margin;

      @include media-breakpoint-down(md) {
        margin-bottom: $block-bottom-margin-mobile;
      }
    }

    app-galleries-bottom {
      .article-card {
        &.style-6 {
          @include media-breakpoint-down(sm) {
            .article-block {
              margin-left: 0;

              .article-bottom {
                padding-right: 15px;
              }
            }
          }
        }
      }
    }
  }
}
