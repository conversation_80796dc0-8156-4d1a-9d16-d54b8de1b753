import { omit } from 'lodash-es';
import {
  BackendFairyGalleryItem,
  BackendFairyPreviewData,
  BackendFairySimpleData,
  BackendFinalistData,
  BackendMainFairyData,
  FairyGalleryItem,
  FairyPreviewData,
  FairyPreviewInterface,
  FinalistData,
  MainFairyData,
} from '../../shared';
import { backendDateToDate, strBoolToBool } from '@trendency/kesma-core';

const IMAGE_HOST = 'https://zoe-static.mediaworks.hu/';

const toAbsoluteImgUrl = (relativeUrl: string): string | undefined => relativeUrl && `${IMAGE_HOST}/${relativeUrl}`;

export const backendFairySimpleDataToFairySimpleData = (data: BackendFairySimpleData | BackendFinalistData): FinalistData => {
  let isPickedByJury = false;
  const finalist = Object.getOwnPropertyNames(data).includes('zsuri_altal_valasztott') ? (data as BackendFinalistData) : undefined;
  if (finalist) {
    isPickedByJury = finalist.zsuri_altal_valasztott === '1';
  }
  return {
    portal: data.country,
    thumbnail: toAbsoluteImgUrl(data.thumbnail_src),
    city: data.city,
    slug: data.slug,
    name: data.name,
    age: parseInt(data.age, 10),
    zsuri_altal_valasztott: isPickedByJury,
  } as FairyPreviewInterface;
};

export const backendFairyGalleryItemToFairyGalleryItem = (item: BackendFairyGalleryItem): FairyGalleryItem =>
  item &&
  ({
    ...item,
    id: parseInt(item.id, 10),
    lany_id: parseInt(item.lany_id, 10),
    sorrend: parseInt(item.sorrend, 10),
    created_time: backendDateToDate(item.created_time, true),
    last_updated_time: backendDateToDate(item.last_updated_time, true),
    src: toAbsoluteImgUrl(item.src),
    thumbnail_src: toAbsoluteImgUrl(item.thumbnail_src),
    thumbnail2_src: toAbsoluteImgUrl(item.thumbnail2_src),
    ...['cimlapra_mehet_e', 'nagy_ajanlokep', 'kis_ajanlokep', 'thumbnail', 'facebook_kep', 'thumbnail2'].reduce(
      (result, property: string) => ({ ...result, [property]: strBoolToBool((item as Record<string, any>)[property]) }),
      {} as Partial<FairyGalleryItem>
    ),
  } as any as FairyGalleryItem);

export const backendMainFairyDataToFairyData = (item: BackendMainFairyData): MainFairyData =>
  item && {
    ...omit(item, ['country']),
    portal: item.country,
    age: parseInt(item.age, 10),
    vote_value: parseFloat(item.vote_value),
    zsuri_altal_valasztott: strBoolToBool(item.zsuri_altal_valasztott),
    galeria: (item.galeria || []).map(backendFairyGalleryItemToFairyGalleryItem),
  };

export const backendFairyPreviewDataToFairyreviewData = (item: BackendFairyPreviewData): FairyPreviewData =>
  item && {
    ...omit(item, ['country', 'city']),
    portal: item.country,
    county: item.city,
    age: parseInt(item.age, 10),
    vote_value: parseFloat(item.vote_value),
    zsuri_altal_valasztott: strBoolToBool(item.zsuri_altal_valasztott),
    kis_ajanlokep: backendFairyGalleryItemToFairyGalleryItem(item.kis_ajanlokep),
    thumbnail: toAbsoluteImgUrl(item.kis_ajanlokep.thumbnail_src) ?? '',
  };
