import { animate, style, transition, trigger } from '@angular/animations';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard } from '@trendency/kesma-ui';
import { sortBy } from 'lodash-es';
import { Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { IMetaData, SafePipe, SeoService } from '@trendency/kesma-core';
import { NgFor, NgIf } from '@angular/common';
import {
  defaultMetaInfo,
  FairyArchiveData,
  FairyCardComponent,
  FairyCardTypes,
  FairyCycle,
  FinalistData,
  GalleriesBottomComponent,
  PagerComponent,
  PortalConfigService,
  SectionHeaderComponent,
} from '../../shared';

const TOP_PARTICIPANT_COUNT = 6;
const MAX_PARTICIPANT_PER_PAGE = 8;

@Component({
  selector: 'app-fairy-landing',
  templateUrl: './fairy-landing.component.html',
  styleUrls: ['./fairy-landing.component.scss'],
  encapsulation: ViewEncapsulation.None,
  animations: [trigger('fadeInAnimation', [transition(':enter', [style({ opacity: 0 }), animate('.300s ease-out', style({ opacity: 1 }))])])],
  imports: [NgIf, AdvertisementAdoceanComponent, SectionHeaderComponent, NgFor, FairyCardComponent, PagerComponent, GalleriesBottomComponent, SafePipe],
})
export class FairyLandingComponent implements OnInit, OnDestroy {
  readonly fairyCardType = FairyCardTypes;
  adverts: AdvertisementsByMedium;
  indexData: FairyArchiveData;
  relatedArticles: ArticleCard[] = [];
  mainParticipants: FinalistData[] = [];
  otherParticipants: FinalistData[] = [];
  displayedOtherParticipants: FinalistData[];
  currentPage = 0;
  itemCount = 0;
  maxPerPage = MAX_PARTICIPANT_PER_PAGE;
  isOverview = true;
  previousCycles: FairyCycle[];
  private readonly unsubscribe$ = new Subject<boolean>();
  private sortedParticipants: FinalistData[] = [];

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly portalConfig: PortalConfigService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        switchMap(({ indexData: { indexData, relatedArticles, previousCycles, isOverview } }) => {
          this.setData(indexData, previousCycles, isOverview);
          this.relatedArticles = relatedArticles;
          this.setMetaData();

          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  onPageChange(activePage: number): void {
    this.pageChange(activePage - 1);
  }

  private setData(data: FairyArchiveData, previousCycles: FairyCycle[], isOverview: boolean): void {
    this.indexData = data;
    this.previousCycles = previousCycles;
    this.isOverview = isOverview;
    if (!isOverview) {
      const participants = (data.finalists?.length ? data.finalists : data.counties?.[0]?.data) || [];
      this.sortedParticipants = sortBy(participants, 'vote_value').reverse();
      this.mainParticipants = this.sortedParticipants.slice(0, TOP_PARTICIPANT_COUNT);
      const topIds = this.mainParticipants.map(({ slug }) => slug);
      // let the original sorting take place -> remove the top ones
      this.otherParticipants = participants.filter(({ slug }) => !topIds.includes(slug));
      this.itemCount = this.otherParticipants.length;
      this.pageChange(0);
    }
  }

  private setMetaData(): void {
    const { title, lead } = this.indexData || {};
    if (!this.indexData) {
      return;
    }

    const metaData: IMetaData = {
      ...defaultMetaInfo(this.portalConfig),
      title: `${this.portalConfig?.portalName} - ${title}`,
      description: lead || this.portalConfig.portalSubtitle,
      ogTitle: title,
      ogType: 'article',
    };

    this.seo.setMetaData(metaData);
  }

  private pageChange(currentPage: number): void {
    this.currentPage = currentPage;
    const itemIndex = this.currentPage * this.maxPerPage;
    this.displayedOtherParticipants = this.otherParticipants.slice(itemIndex, itemIndex + this.maxPerPage);
  }
}
