import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService } from '../article/article.service';
import { FairyService } from './fairy.service';
import { FairyArchiveData } from '../../shared';
import { ApiResponseMetaList, ApiResult, ArticleCard } from '@trendency/kesma-ui';

const COLUMN_SLUG = 'tunderszepek';
const MAX_RELATED_ARTICLE = 6;

@Injectable()
export class FairyLandingResolver {
  constructor(
    private readonly fairyService: FairyService,
    private readonly articleService: ArticleService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<{ indexData: FairyArchiveData; relatedArticles: ArticleCard[] }> {
    return forkJoin({
      indexData: this.fairyService.getArchive(route.params['county'] === 'dontosok', route.params['county']),
      previousCycles: this.fairyService.getIndex(),
      relatedArticles: this.articleService.getSidebarArticleRecommendations(MAX_RELATED_ARTICLE, COLUMN_SLUG).pipe(
        catchError(() => {
          return of({
            meta: { dataCount: 0 } as ApiResponseMetaList,
            data: [],
          } as ApiResult<ArticleCard[], ApiResponseMetaList>);
        })
      ),
    }).pipe(
      catchError((err) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        console.error(err);
        return throwError(err);
      }),
      map(({ indexData, relatedArticles, previousCycles }) => ({
        indexData: {
          ...indexData,
          ...(previousCycles as any),
        },
        relatedArticles: relatedArticles?.data || [],
        previousCycles: previousCycles.previous_cycles,
        isOverview: !route.params['county'],
      }))
    );
  }
}
