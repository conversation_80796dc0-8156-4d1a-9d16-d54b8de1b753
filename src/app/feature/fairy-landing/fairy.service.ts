import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BackendFairyArchiveData, BackendFairyIndexResult, FairyArchiveData, FairyArchiveFilter, FairyEmbedResult, FairyIndexResult } from '../../shared';
import { backendFairyPreviewDataToFairyreviewData, backendFairySimpleDataToFairySimpleData, backendMainFairyDataToFairyData } from './fairy.utils';
import { ReqService } from '@trendency/kesma-core';

@Injectable({
  providedIn: 'root',
})
export class FairyService {
  constructor(private readonly reqService: ReqService) {}

  getIndex(cycleSlug?: string, personSlug?: string): Observable<FairyIndexResult> {
    return this.reqService.get<BackendFairyIndexResult>(`/tunderszepek${cycleSlug ? `/${cycleSlug}` : ''}${personSlug ? `/${personSlug}` : ''}`).pipe(
      map((res) =>
        res
          ? {
              ...res,
              main_girl_data: backendMainFairyDataToFairyData(res.main_girl_data),
              all_girl_data: (res.all_girl_data || []).map(backendFairyPreviewDataToFairyreviewData),
              previous_cycles: res.previus_cycles,
            }
          : ({
              title: 'Tündérszépek - hiba',
              lead: 'Hiba történt',
            } as FairyIndexResult)
      )
    );
  }

  getEmbed(): Observable<FairyEmbedResult> {
    return this.reqService.get<FairyEmbedResult>('/tunderszepek-embed');
  }

  getArchive(onlyFinalists: boolean, portal?: string, personSlug?: string, year?: number): Observable<FairyArchiveData> {
    const params: FairyArchiveFilter = {};
    if (onlyFinalists) {
      params.is_finalists = '1';
    }
    if (portal && portal !== 'dontosok') {
      params.country_slug = portal;
    }
    if (year) {
      params.year = year.toString();
    }
    if (personSlug) {
      params.person_slug = personSlug;
    }
    return this.reqService
      .get<BackendFairyArchiveData>('/tunderszepek-archive', {
        params,
      })
      .pipe(
        map(({ fo_lany_data, lead, title, finalists, countries }: BackendFairyArchiveData): FairyArchiveData => {
          const countyName = countries?.[0]?.country ?? 'Nincs adat';
          return {
            slug: fo_lany_data?.slug ?? '',
            title,
            lead,
            countyName,
            counties: (countries ?? []).map(({ country, data }) => ({
              county: country,
              data: data.map(backendFairySimpleDataToFairySimpleData),
            })),
            finalists: (finalists ?? []).map(backendFairySimpleDataToFairySimpleData),
            main_girl_data: fo_lany_data && {
              slug: fo_lany_data.slug,
              name: fo_lany_data.name,
              description: fo_lany_data.description,
              age: parseInt(fo_lany_data.age, 10),
              county: fo_lany_data.country,
              city: fo_lany_data.city,
              galeria: (fo_lany_data.galeria ?? []).map(({ src }) => ({
                name: fo_lany_data.name,
                src,
                thumbnail_src: src,
                thumbnail2_src: src,
                facebook_kep: false,
                kis_ajanlokep: false,
              })),
            },
          };
        })
      );
  }

  getPersonDetails = (personSlug: string, cycleSlug = 'current'): Observable<FairyIndexResult> => this.getIndex(cycleSlug, personSlug);

  getArchivePersonDetails = (personSlug: string, portal?: string, year?: number): Observable<FairyArchiveData> =>
    this.getArchive(false, portal, personSlug, year);
}
