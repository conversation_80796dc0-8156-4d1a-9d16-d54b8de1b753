import { DOCUMENT } from '@angular/common';
import { AfterViewInit, Component, inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, BreakingNews, LayoutApiData, PAGE_TYPES } from '@trendency/kesma-ui';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { defaultMetaInfo, InitResolverData, JsonLDService, mapLayoutData, PortalConfigService } from '../../shared';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  imports: [LayoutComponent],
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  layoutApiData: LayoutApiData;
  adPageType = PAGE_TYPES.main_page;
  breakingNews: BreakingNews;

  private readonly document: Document = inject(DOCUMENT);
  private links: HTMLCollectionOf<HTMLAnchorElement>;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly utils: UtilService,
    private readonly schema: JsonLDService,
    private readonly portalConfig: PortalConfigService,
    private readonly analytics: AnalyticsService
  ) {}

  ngOnInit(): void {
    const {
      data: {
        init: { breakingNews },
      },
      layoutData,
    } = this.route.snapshot.data as { data: InitResolverData; layoutData: LayoutApiData };

    this.layoutApiData = mapLayoutData(layoutData);

    this.breakingNews = breakingNews;
    this.applySchemaOrg();
    this.seo.setMetaData(defaultMetaInfo(this.portalConfig), { skipSeoMetaCheck: true });
    this.seo.updateCanonicalUrl('');
  }

  ngAfterViewInit(): void {
    if (!this.utils.isBrowser()) {
      return;
    }

    queueMicrotask((): void => {
      this.links = this.document.getElementsByTagName('a');
      Array.from(this.links ?? []).forEach((element) => {
        element.addEventListener('click', this.handleAnchorClickEvent);
        element.addEventListener('auxclick', this.handleAnchorClickEvent);
      });
    });
  }

  ngOnDestroy(): void {
    if (!this.utils.isBrowser()) {
      return;
    }

    Array.from(this.links ?? []).forEach((element): void => {
      element.removeEventListener('click', this.handleAnchorClickEvent);
      element.removeEventListener('auxclick', this.handleAnchorClickEvent);
    });
  }

  handleAnchorClickEvent = (ev: MouseEvent): void => {
    const link = (ev.composedPath().find((elem: EventTarget) => (elem as Element).nodeName === 'A') as HTMLAnchorElement)?.href;
    if (link) {
      this.analytics.sendMainPageClick(link, document.referrer);
    }
  };

  private applySchemaOrg(): void {
    this.schema.removeStructuredData();
    this.schema.insertSchema();
  }
}
