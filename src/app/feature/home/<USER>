import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../../shared';
import { LayoutApiData } from '@trendency/kesma-ui';

@Injectable()
export class HomeResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(): Observable<LayoutApiData> {
    return this.apiService.getHomePage().pipe(
      catchError((err) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map(({ data }) => data)
    );
  }
}
