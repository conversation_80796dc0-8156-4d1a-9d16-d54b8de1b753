import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-advertisement-page',
  templateUrl: './advertisement-page.component.html',
  styleUrls: ['./advertisement-page.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [NgFor, NgIf, AdvertisementAdoceanComponent, AsyncPipe],
})
export class AdvertisementPageComponent implements OnInit {
  advertisementList$: Observable<Advertisement[]>;

  constructor(private readonly adStore: AdvertisementAdoceanStoreService) {}

  ngOnInit(): void {
    this.advertisementList$ = this.adStore.advertisemenets$;
  }
}
