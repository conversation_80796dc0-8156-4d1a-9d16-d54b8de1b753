import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { combineLatest, forkJoin, Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  RedirectService,
} from '@trendency/kesma-ui';
import { ArticleSearchResult } from '../article/article.definitions';
import { ArticleService } from '../article/article.service';
import { PublishDatePipe, SeoService } from '@trendency/kesma-core';
import { NgFor, NgIf } from '@angular/common';
import {
  ArticleCardComponent,
  ArticleCardTypes,
  DEFAULT_PUBLISH_DATE_FORMAT,
  defaultMetaInfo,
  mapSearchResultToArticleCard,
  PagerComponent,
  PortalConfigService,
  SectionHeaderComponent,
} from '../../shared';

const MAX_RESULTS_PER_PAGE = 8;

const MAX_RELATED_ARTICLE_LEFT = 4;
const MAX_RELATED_ARTICLE_RIGHT = 6;

export type NotesData = Readonly<{
  articles: ApiResult<ArticleSearchResult[], ApiResponseMetaList>;
  recommendation: ApiResult<ArticleCard[], ApiResponseMetaList>;
}>;

@Component({
  selector: 'app-notes',
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.scss'],
  imports: [NgFor, RouterLink, PagerComponent, SectionHeaderComponent, ArticleCardComponent, NgIf, AdvertisementAdoceanComponent, PublishDatePipe],
})
export class NotesComponent implements OnInit, OnDestroy {
  readonly articleCardType = ArticleCardTypes;

  maxCount = 0;
  maxPerPage = MAX_RESULTS_PER_PAGE;
  currentPage = 0;
  publicAuthor: string;
  articleList: ArticleCard[];
  recommendationTop: ArticleCard[];
  recommendationBottom: ArticleCard[];
  adverts: AdvertisementsByMedium;
  private readonly destroy$ = new Subject<boolean>();

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly seo: SeoService,
    private readonly articleService: ArticleService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly redirectService: RedirectService
  ) {}

  ngOnInit(): void {
    combineLatest([this.route.queryParams, this.route.params, this.adStoreAdo.advertisemenets$])
      .pipe(
        map(([queryParams, params, ads]): [Params, Params, AdvertisementsByMedium] => {
          return [queryParams, params, this.adStoreAdo.separateAdsByMedium(ads)];
        }),
        switchMap(([queryParams, params, ads]): Observable<NotesData> => {
          const { author } = params;
          const { page } = queryParams;

          this.adverts = ads;
          this.publicAuthor = author;
          this.currentPage = (page ?? 1) - 1;

          return forkJoin({
            articles: this.articleService.searchArticleByKeyword(
              {
                author,
                exclude_articles: '1',
              },
              this.currentPage,
              this.maxPerPage
            ),
            recommendation: this.articleService.getLatestArticles(MAX_RELATED_ARTICLE_LEFT + MAX_RELATED_ARTICLE_RIGHT),
          }).pipe(
            tap(({ articles }) => {
              if (this.redirectService.shouldBeRedirect(this.currentPage, articles?.data)) {
                this.redirectService.redirectOldUrl(`jegyzetek/${author}`, false, 302);
              }
            })
          );
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(({ articles, recommendation }): void => {
        this.maxCount = articles?.meta.limitable?.rowAllCount ?? 0;
        this.articleList = articles?.data.map(mapSearchResultToArticleCard);
        const recommendations = recommendation?.data;
        this.recommendationTop = (recommendations ?? [])?.slice(0, MAX_RELATED_ARTICLE_LEFT);
        this.recommendationBottom = (recommendations ?? [])?.slice(MAX_RELATED_ARTICLE_LEFT);

        this.seo.setMetaData({
          ...defaultMetaInfo(this.portalConfig),
          ogTitle: `${this.portalConfig?.portalName} - Jegyzetek`,
        });
        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  onPageChange(newPage: number): void {
    this.currentPage = newPage - 1;
    this.router.navigate([], { queryParams: { page: newPage } });
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
