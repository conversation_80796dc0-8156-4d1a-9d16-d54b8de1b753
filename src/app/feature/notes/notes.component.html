<section class="notes">
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="page-title">{{ publicAuthor }}</div>
      <div class="notes">
        <ng-container *ngFor="let note of articleList">
          <a [routerLink]="['/', note?.category?.slug, note?.publishYear, note?.publishMonth, note?.slug]" class="note-card">
            <div class="top">
              <div class="tags">{{ note?.tag ? note?.tag : 'Jegyzet' }}</div>
              <div class="date">{{ note?.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</div>
            </div>
            <h3 class="title">{{ note?.title }}</h3>
            <p class="lead">{{ note?.lead }}</p>
          </a>
        </ng-container>
      </div>
      <app-pager [currentPage]="currentPage" [rowAllCount]="maxCount" [rowOnPageCount]="maxPerPage" (pageChange)="onPageChange($event)"></app-pager>
    </div>
    <aside>
      <app-section-header [sectionTitle]="'Kapcsolódó tartalmak'"></app-section-header>
      <div class="cards-container">
        <ng-container *ngFor="let card of recommendationTop">
          <app-article-card [styleID]="articleCardType.STYLE6" [article]="card"></app-article-card>
        </ng-container>
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.box_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>

        <ng-container *ngFor="let card of recommendationBottom">
          <app-article-card [styleID]="articleCardType.STYLE3" [article]="card"></app-article-card>
        </ng-container>
      </div>
    </aside>
  </div>
</section>
