@use 'shared' as *;

section.notes {
  .wrapper {
    .left-column {
      padding-bottom: 50px;

      .page-title {
        font-weight: 500;
        font-size: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 20px;
        text-transform: uppercase;
        @include article-before-line();

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }
      }

      .notes {
        .note-card {
          display: block;
          margin-top: 50px;
          margin-bottom: 50px;
          position: relative;
          @include media-breakpoint-down(md) {
            padding-bottom: 29px;
          }

          .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 10px;

            .tags {
              font-weight: normal;
              font-size: 16px;
              line-height: 30px;
            }

            .date {
              font-weight: normal;
              font-size: 14px;
              line-height: 19px;
              color: $grey-7;
              @include media-breakpoint-down(md) {
                position: absolute;
                left: 0;
                bottom: 0;
              }
            }
          }

          .title {
            font-family: $font-zilla;
            font-style: italic;
            font-weight: 500;
            font-size: 30px;
            line-height: 40px;
            margin-bottom: 7px;
            @include media-breakpoint-down(md) {
              font-size: 20px;
              line-height: 30px;
            }
          }

          .lead {
            font-weight: normal;
            font-size: 20px;
            line-height: 30px;
            @include media-breakpoint-down(md) {
              font-size: 16px;
              line-height: 22px;
            }
          }
        }
      }
    }

    app-pager {
      display: flex;
      justify-content: center;
    }
  }
}
