<section class="foundation-content">
  <div class="wrapper foundation-content-wrapper">
    <div class="title-container has-title">
      <h1 class="title foundation-content-title">
        <span class="keyword">Tartalmaink</span>
      </h1>
    </div>

    <ng-container *ngIf="searchResponse$ | async as response; else loading">
      <div class="foundation-content-results-meta">
        <div class="foundation-content-results-counter">
          <strong>{{ response.meta.limitable.rowAllCount }} db</strong> cikk öss<PERSON>en
        </div>
      </div>

      <div class="foundation-content-list">
        <ng-container *ngFor="let article of articles$ | async; let i = index; trackBy: trackByFn">
          <app-article-card [article]="article" [styleID]="ArticleCardTypes.STYLE8"></app-article-card>

          <div *ngIf="i === 3 && adverts?.mobile?.mobilrectangle_1 as ad" class="mobile">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 3 && adverts?.desktop?.roadblock_1 as ad" class="desktop">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 7 && adverts?.mobile?.mobilrectangle_2 as ad" class="mobile">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 7 && adverts?.desktop?.roadblock_2 as ad" class="desktop">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 11 && adverts?.mobile?.mobilrectangle_3 as ad" class="mobile">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 11 && adverts?.desktop?.roadblock_3 as ad" class="desktop">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 15 && adverts?.mobile?.mobilrectangle_4 as ad" class="mobile">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>

          <div *ngIf="i === 15 && adverts?.desktop?.roadblock_4 as ad" class="desktop">
            <kesma-advertisement-adocean
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                paddingTop: 'var(--ad-padding-top)',
                paddingBottom: 'var(--ad-padding-bottom)',
                background: 'var(--kui-gray-300)',
              }"
            ></kesma-advertisement-adocean>
          </div>
        </ng-container>

        <app-pager
          (pageChange)="onSelectPage($event)"
          [currentPage]="response.meta.limitable.pageCurrent"
          [rowAllCount]="response.meta.limitable.rowAllCount"
          [rowOnPageCount]="response.meta.limitable.rowOnPageCount"
        ></app-pager>
      </div>
    </ng-container>

    <ng-template #loading>
      <div class="no-results-text">
        <p>Betöltés...</p>
      </div>
    </ng-template>
  </div>
</section>
