import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiListResult,
  ArticleCard,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { Observable, Subject, takeUntil } from 'rxjs';
import { map } from 'rxjs/operators';
import { ArticleSearchResult } from '../article/article.definitions';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { ArticleCardComponent, ArticleCardTypes, defaultMetaInfo, mapSearchResultToArticleCard, PagerComponent, PortalConfigService } from '../../shared';

@Component({
  selector: 'app-foundation-content',
  templateUrl: './foundation-content.component.html',
  styleUrls: ['./foundation-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, ArticleCardComponent, AdvertisementAdoceanComponent, PagerComponent, AsyncPipe],
})
export class FoundationContentComponent implements OnInit, OnDestroy {
  searchResponse$: Observable<ApiListResult<ArticleSearchResult>> = this.route.data.pipe(map((res) => res['searchResponse']));
  articles$: Observable<ArticleCard[]> = this.searchResponse$.pipe(map((res) => res.data.map(mapSearchResultToArticleCard)));

  adverts: AdvertisementsByMedium;

  ArticleCardTypes = ArticleCardTypes;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly portalConfig: PortalConfigService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.setMetaData();
    });

    this.adStore.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads, 'listing')))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.cd.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSelectPage(page: number): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page },
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(this.seo.currentUrl, this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical, { addHostUrl: false, skipSeoMetaCheck: true });
    }
    const title = `${this.portalConfig.portalName} - Alapkőtartalom`;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
