@use 'shared' as *;
@use '../../shared/components/search-page-base/search-page-base' as *;

:host {
  display: block;
  margin: 30px 0;
}

.foundation-content {
  .title-container {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    position: relative;
    padding: 20px 0;
    min-height: 70px;
    max-width: 100%;
    @include article-before-line();

    &.has-title {
      padding: 20px;
    }
  }

  &-wrapper {
    margin-top: 40px;
  }

  &-title {
    @include title();
  }

  &-results-meta {
    margin-bottom: 50px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 20px;
    }
  }

  &-results-counter {
    font-size: 14px;
    line-height: 19.07px;
    margin: 20px 0 10px;
  }
}
