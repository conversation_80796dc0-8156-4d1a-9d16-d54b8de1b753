import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiListResult, RedirectService } from '@trendency/kesma-ui';
import { ApiService } from '../../shared';
import { ArticleSearchResult } from '../article/article.definitions';

@Injectable()
export class FoundationContentResolver {
  constructor(
    private readonly api: ApiService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ApiListResult<ArticleSearchResult>> {
    const pageIndex: number = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;

    return this.api
      .getSearch({ isFoundationContent: 'true' }, route.queryParams['page'] ? route.queryParams['page'] - 1 : 0, route.queryParams['perPage'])
      .pipe(
        map((res) => {
          if (this.redirectService.shouldBeRedirect(pageIndex, res?.data)) {
            this.redirectService.redirectOldUrl(`alapko-tartalom`, false, 302);
          }
          return res;
        }),
        catchError((err) => {
          this.router.navigate(['/404'], { skipLocationChange: true }).then();
          return throwError(() => err);
        })
      );
  }
}
