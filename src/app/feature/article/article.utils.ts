import { Article, ArticleBody, ArticleBodyType, ArticleCard, BackendArticle, backendDateToDate, backendVotingDataToVotingData } from '@trendency/kesma-ui';
import { format } from 'date-fns';
import { ArticleSearchResult, BackendArticleSearchResult } from './article.definitions';
import { EmbeddedArticleRecommendation } from '@trendency/kesma-ui/lib/definitions/article-card.definitions';

export const backendArticlesSearchResultsToArticleSearchResArticles = (article: BackendArticleSearchResult): ArticleSearchResult => {
  const [year, month] = article.publishDate.split('-');
  return {
    ...article,
    length: parseInt(article.length, 10),
    year,
    month,
  };
};

export const backendArticlesToArticles = (article: BackendArticle): Article => {
  const { lastUpdated, publishDate: pubDate, dossier } = article;
  let publishDate: Date | undefined;
  let publishYear: number;
  let publishMonth: number;
  if (pubDate) {
    publishDate = backendDateToDate(typeof pubDate === 'string' ? pubDate : pubDate.date) ?? undefined;
    publishYear = publishDate?.getUTCFullYear() ?? 0;
    publishMonth = publishDate?.getUTCMonth() ?? 0;
  } else {
    publishDate = undefined;
    publishYear = 0;
    publishMonth = 0;
  }
  const last: Date | undefined = lastUpdated ? (backendDateToDate(lastUpdated) ?? undefined) : undefined;

  const body: ArticleBody[] = article.body.map((element) => {
    if (element.type === ArticleBodyType.Voting) {
      const votingValue = backendVotingDataToVotingData(element.details[0]?.value);
      const detail = { ...element.details[0], value: votingValue };
      return { ...element, details: [detail] };
    }
    return element;
  });

  return {
    ...article,
    dossier: dossier?.[0],
    body,
    lastUpdated: last,
    publishDate,
    year: publishYear,
    month: publishMonth,
    preTitle: article?.preTitle,
    tag: article.tags && article.tags[0],
    columnSlug: article.primaryColumn?.slug,
    columnTitle: article.primaryColumn?.title,
  };
};

export const dontMissThisArticleToArticleCard = ({ columnSlug, publishDate, slug, title }: EmbeddedArticleRecommendation): ArticleCard => {
  let publishDateFromBackend: Date | undefined;
  let publishYear: string | number;
  let publishMonth: string | number;
  if (publishDate) {
    publishDateFromBackend = backendDateToDate(typeof publishDate === 'string' ? publishDate : publishDate) ?? undefined;
    publishYear = publishDateFromBackend?.getUTCFullYear() ?? 0;
    publishMonth = format(publishDateFromBackend ?? 0, 'MM');
  } else {
    publishDateFromBackend = undefined;
    publishYear = 0;
    publishMonth = 0;
  }

  return {
    slug,
    title,
    publishDate: publishDateFromBackend,
    columnSlug,
    publishYear,
    publishMonth,
    category: {
      slug: columnSlug,
    },
    label: undefined,
  };
};
