import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import {
  AllColumnsResponse,
  ApiResponseMetaList,
  ApiResult,
  Article,
  ArticleCard,
  ArticleCategoryParams,
  BackendArticle,
  BackendRecommendationsData,
  BackendRecommendedArticle,
  backendRecommendedArticleToArticleCard,
  buildPhpArrayParam,
  externalRecommendationToArticleCard,
  RecommendationsData,
  RegionParams,
  SearchQuery,
  Tag,
  TagNameFilterResponse,
} from '@trendency/kesma-ui';
import { backendArticlesSearchResultsToArticleSearchResArticles, backendArticlesToArticles } from './article.utils';
import { ArticleSearchResult, BackendArticleSearchResult } from './article.definitions';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(private readonly reqService: ReqService) {}

  searchArticleByKeyword(
    searchQuery: SearchQuery,
    page = 0,
    itemsPerPage = 20,
    orderByAsc = true
  ): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>('/content-page/search', {
        params: {
          ...searchQuery,
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
          'date_order[0]': orderByAsc ? 'asc' : 'desc',
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  searchArticleByTags(tags: string[], page = 0, itemsPerPage = 10, orderByAsc = true): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>(`/content-page/search`, {
        params: {
          ...buildPhpArrayParam(tags, 'tags'),
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
          order: orderByAsc ? 'asc' : 'desc',
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    const timestamp = Math.floor(new Date().getTime());
    return this.reqService.get(`content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}&t=${timestamp.toString()}`, {
      params: {
        previewType,
      },
    });
  }

  getArticleRedirect(requestUrl: string): Observable<{ url: string }> {
    return this.reqService.get(`/portal/redirection?url=${requestUrl}`);
  }

  getArticle(category: string, year: string, month: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticle>>(`/content-page/article/${category}/${year}/${`${month}`.padStart(2, '0')}/${articleSlug}`).pipe(
      map(({ data, meta }: ApiResult<BackendArticle>) => ({
        data: backendArticlesToArticles(data),
        meta,
      }))
    );
  }

  getArticleRecommendations(articleSlug: string): Observable<ApiResult<RecommendationsData>> {
    return this.reqService
      .get<ApiResult<BackendRecommendationsData>>(`/content-page/article/${articleSlug}/recommendation?fields[]=foundationTagSlug&fields[]=foundationTagTitle`)
      .pipe(
        map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
          data: {
            ...data,
            highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
            lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
            externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
          },
          meta,
        }))
      );
  }

  getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds || [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService.get<ApiResult<BackendRecommendedArticle[], ApiResponseMetaList>>('/content-page/articles-by-column', { params }).pipe(
      map(({ data, meta }: ApiResult<BackendRecommendedArticle[], ApiResponseMetaList>) => ({
        data: (data || []).map(backendRecommendedArticleToArticleCard),
        meta,
      }))
    );
  }

  getRegionArticles(
    regionSlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds?: string[]
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: RegionParams = {
      regionSlug,
      rowCount_limit: itemsPerPage?.toString(),
      page_limit: page?.toString(),
      'excludedArticleIds[]': excludedIds || [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;

    return this.reqService.get<ApiResult<BackendRecommendedArticle[], ApiResponseMetaList>>('/content-page/articles-by-region', { params }).pipe(
      map(({ data, meta }: ApiResult<BackendRecommendedArticle[], ApiResponseMetaList>) => ({
        data: (data || []).map(backendRecommendedArticleToArticleCard),
        meta,
      }))
    );
  }

  getSidebarArticleRecommendations = (
    count: number,
    categorySlug: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> => this.getCategoryArticles(categorySlug, count, undefined, undefined, undefined, excludedIds);

  getAllColumns(itemsPerPage = 99999999): Observable<AllColumnsResponse> {
    return this.reqService.get<AllColumnsResponse>('/source/content-group/columns', {
      params: {
        rowCount_limit: itemsPerPage.toString(),
      },
    });
  }

  getTagBySlug(tagSlug: string): Observable<Tag> {
    return this.reqService
      .get<ApiResult<TagNameFilterResponse>>(`content-group/tags/${tagSlug}`)
      .pipe(map(({ data }: ApiResult<TagNameFilterResponse>) => data));
  }

  getLatestArticles(rowCountLimit = 10, pageLimit?: number): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService
      .get<ApiResult<BackendRecommendedArticle[], ApiResponseMetaList>>('/content-page/articles-by-any', {
        params: {
          rowCount_limit: rowCountLimit?.toString() ?? '10',
          page_limit: pageLimit?.toString() ?? '1',
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          meta,
          data: data.map(backendRecommendedArticleToArticleCard),
        }))
      );
  }
}
