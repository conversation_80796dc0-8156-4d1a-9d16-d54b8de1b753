import { DOCUMENT } from '@angular/common';
import { inject, Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { ArticleService } from './article.service';
import type { Response } from 'express';
import { environment } from '../../../environments/environment';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { ApiService } from '../../shared';
import { ApiResponseMetaList, ApiResult, ArticleSearchResult } from '@trendency/kesma-ui';

@Injectable()
export class ArticleResolverService<TArticleResolverData> {
  private readonly document: Document = inject(DOCUMENT);

  constructor(
    private readonly articleService: ArticleService,
    private readonly router: Router,
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly apiService: ApiService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<TArticleResolverData> {
    const foundationTagSlug = route.params['slug'];

    const previewHash = route.params['previewHash'];
    let { year, month } = route.params;
    const categorySlug = route.params['categorySlug'];
    const articleSlug = route.params['previewHash'] ? 'cikk-elolnezet' : route.params['articleSlug'];
    const previewType = route.params['previewType'] ? route.params['previewType'] : 'accepted';
    const isYear = !isNaN(year);
    year = isYear && year ? year : '';
    month = isYear && month ? month : '';
    const isMonth = !isNaN(month);
    const URL = foundationTagSlug ? `${foundationTagSlug}` : `${categorySlug}/${year}/${month}/${articleSlug}`;
    let request$: Observable<any>;

    if (previewHash) {
      request$ = forkJoin({
        article: this.articleService.getArticlePreview(articleSlug, previewHash, previewType),
        recommendations: of({}),
      });
    } else if ((!isYear || !isMonth) && !foundationTagSlug) {
      request$ = forkJoin({
        article: this.redirectOldArticleUrls(),
        recommendations: of({}),
        articleSlug: of(articleSlug),
        categorySlug: of(categorySlug),
        url: of(URL),
      });
    } else {
      request$ = (foundationTagSlug ? this.apiService.getArticlesByFoundationTag(foundationTagSlug) : of({})).pipe(
        switchMap((searchResult: ApiResult<ArticleSearchResult[], ApiResponseMetaList> | any) => {
          let fCategorySlug, fYear, fMonth, fArticleSlug, firstArticleWithFoundationTag;
          if (foundationTagSlug) {
            firstArticleWithFoundationTag = searchResult?.data?.[0];

            if (firstArticleWithFoundationTag) {
              fCategorySlug = firstArticleWithFoundationTag?.columnSlug;
              fYear = firstArticleWithFoundationTag.publishDate.split('-')[0];
              fMonth = firstArticleWithFoundationTag.publishDate.split('-')[1];
              fArticleSlug = firstArticleWithFoundationTag?.slug;
            }
          }

          if (!(foundationTagSlug && firstArticleWithFoundationTag) && !categorySlug) {
            return of(null);
          }

          return forkJoin({
            article:
              foundationTagSlug && firstArticleWithFoundationTag
                ? this.articleService.getArticle(fCategorySlug ?? '', fYear ?? '', fMonth ?? '', fArticleSlug ?? '')
                : this.articleService.getArticle(categorySlug, year, month, articleSlug),
            url: of(URL),
            recommendations:
              firstArticleWithFoundationTag || route.params['categorySlug']
                ? this.articleService.getArticleRecommendations(foundationTagSlug ? searchResult?.data?.[0]?.slug : articleSlug).pipe(
                    catchError((_) => {
                      return of([]);
                    })
                  )
                : of(null),
            foundationTagSlug: of(foundationTagSlug),
            foundationTagTitle: of(foundationTagSlug ? searchResult?.data?.[0]?.foundationTagTitle : undefined),
            slug: of(foundationTagSlug ? searchResult?.data?.[0]?.slug : articleSlug),
            articleSlug: of(foundationTagSlug ? searchResult?.data?.[0]?.slug : articleSlug),
            categorySlug: of(foundationTagSlug ? searchResult?.data?.[0]?.columnSlug : categorySlug),
            year: of(foundationTagSlug ? fYear : year),
            month: of(foundationTagSlug ? fMonth : month),
          });
        })
      );
    }

    return request$.pipe(
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });

        return throwError(error);
      })
    );
  }

  private redirectOldArticleUrls(): Observable<any> {
    const currentUrl = this.seoService.currentUrl;
    return this.articleService.getArticleRedirect(encodeURIComponent(currentUrl)).pipe(
      map(({ url }) => {
        if (url && this.utilsService.isBrowser()) {
          this.document.location = url;
        } else if (url && this.response) {
          this.response.status(301);
          // port is missing from the response and `process.env.PORT` reflects
          // the devserver's port when running w/ local devserver -> replacing w/ the predefined
          if (url.match(/^https?:\/\/localhost\//)) {
            url = url.replace(/^https?:\/\/localhost/, environment.siteUrl ?? '');
          }
          this.response.setHeader('location', url);
        } else {
          this.router.navigate(['/', '404'], {
            skipLocationChange: true,
          });
          return throwError(null);
        }
        return of({});
      })
    );
  }
}
