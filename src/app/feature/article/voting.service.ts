import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import { ApiResult, BackendVoteData, backendVotingDataToVotingData, VoteData } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class VotingService {
  constructor(private readonly reqService: ReqService) {}

  vote(voteId: string, token: string): Observable<ApiResult<VoteData>> {
    return this.reqService
      .get<ApiResult<BackendVoteData>>(`/voting/vote/${voteId}?`, {
        params: {
          recaptcha: token,
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          meta,
          data: backendVotingDataToVotingData(data),
        }))
      );
  }

  getCurrentVotingStatus(votingId: string, token: string): Observable<ApiResult<VoteData>> {
    return this.reqService
      .get<ApiResult<BackendVoteData>>(`/voting/${votingId}`, {
        params: {
          recaptcha: token,
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          meta,
          data: backendVotingDataToVotingData(data),
        }))
      );
  }
}
