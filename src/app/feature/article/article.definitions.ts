import { Api<PERSON><PERSON><PERSON>, Article, ArticleCard, ContentType, FocusPointUrlWithAspectRatio, Tag } from '@trendency/kesma-ui';

export type SearchQuery = Readonly<{
  global_filter?: string;
  author?: string;
  from_date?: string;
  to_date?: string;
  column?: string;
  exclude_articles?: string;
  isFoundationContent?: string;
}>;

export type ArticleSearchResult = Readonly<{
  id: string;
  contentType: string;
  title: string;
  lead: string;
  length: number;
  publishDate: string;
  slug: string;
  columnSlug: string;
  columnTitle: string;
  author: string;
  image?: string;
  thumbnail?: string;
  tag?: Tag;
  tags?: Tag[];
  preTitle: string;
  year: string;
  month: string;
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  thumbnailFocusedImages?: FocusPointUrlWithAspectRatio;
}>;

export type BackendArticleSearchResult = Readonly<{
  id: string;
  contentType: ContentType;
  title: string;
  lead: string;
  length: string;
  publishDate: string;
  slug: string;
  columnSlug: string;
  columnTitle: string;
  author: string;
  image?: string;
  thumbnail?: string;
  tag?: Tag;
  tags?: Tag[];
  preTitle: string;
}>;

export type GameDetails = Readonly<{
  description: string;
  id?: string;
  url?: string;
  game?: Game;
}>;

export type Game = Readonly<{
  backgroundColor: string;
  logo: string;
  title: string;
  blocks?: GameDetails[];
}>;

export type RecommendationsData = Readonly<{
  highPriorityArticles: ArticleCard[];
  lowPriorityArticles: ArticleCard[];
  videos: ArticleCard[];
  externalRecommendation: ArticleCard[];
}>;

export type ArticleResolverData = Readonly<{
  article: ApiResult<Article>;
  recommendations: ApiResult<RecommendationsData>;
  year?: number;
  month?: number;
  articleSlug: string;
  categorySlug: string;
  url?: string;
  foundationTagSlug?: string;
  foundationTagTitle?: string;
}>;
