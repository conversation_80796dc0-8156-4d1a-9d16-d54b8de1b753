import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgStyle, NgS<PERSON>, Ng<PERSON>witchCase, NgTemplateOutlet } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {
  ArticleSchema,
  EmbeddingService,
  FormatDatePipe,
  ImageLazyLoadDirective,
  IMetaData,
  PublishDatePipe,
  SeoService,
  StorageService,
  UtilService,
} from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AdvertisementVariablesByMediums,
  ALL_BANNER_LIST,
  AnalyticsService,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  AutoArticleBodyAdService,
  ElectionsBoxStyle,
  FocusPointDirective,
  GalleryData,
  GalleryDetails,
  GalleryElementData,
  GameDetails,
  getArticleAuthors,
  MinuteToMinuteState,
  NativtereloComponent,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
  SecondaryFilterAdvertType,
  SponsoredTag,
  Tag,
} from '@trendency/kesma-ui';
import { forkJoin, Subject } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import {
  ArticleCardComponent,
  ArticleCardTypes,
  ArticleGameComponent,
  ArticleSubscriptionBoxComponent,
  ArticleVideoComponent,
  DEFAULT_PUBLISH_DATE_FORMAT,
  DontMissThisArticleToArticleCardPipe,
  DossierRecommendationComponent,
  ElectionsBoxComponent,
  ElectionsService,
  FoundationRecommendationComponent,
  GalleryApiService,
  GalleryCardComponent,
  JsonLDService,
  MegyeiUtils,
  MinuteByMinuteComponent,
  NewsletterModalComponent,
  PersonalizedRecommendationService,
  PortalConfigService,
  QuizComponent,
  ScrollPositionService,
  SectionHeaderComponent,
  SocialRowComponent,
  SponsoredTagBoxComponent,
  TagRowComponent,
  VotingBoxComponent,
  WysiwygBoxComponent,
} from '../../shared';
import { GalleryScreenService } from '../gallery-screen/gallery-screen.service';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleResolverData } from './article.definitions';
import { AdultComponent } from './components/adult/adult.component';
import { NoteComponent } from './components/note/note.component';

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrls: ['./article.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ScrollPositionService, AutoArticleBodyAdService],
  imports: [
    NgIf,
    AdultComponent,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    WysiwygBoxComponent,
    AdvertisementAdoceanComponent,
    DossierRecommendationComponent,
    QuizComponent,
    VotingBoxComponent,
    ArticleVideoComponent,
    ArticleGameComponent,
    ArticleCardComponent,
    GalleryCardComponent,
    RouterLink,
    NgClass,
    TagRowComponent,
    NgStyle,
    SocialRowComponent,
    FocusPointDirective,
    NgTemplateOutlet,
    MinuteByMinuteComponent,
    NativtereloComponent,
    ElectionsBoxComponent,
    ArticleSubscriptionBoxComponent,
    FoundationRecommendationComponent,
    SectionHeaderComponent,
    SidebarComponent,
    NewsletterModalComponent,
    NoteComponent,
    PublishDatePipe,
    DontMissThisArticleToArticleCardPipe,
    ImageLazyLoadDirective,
    SponsoredTagBoxComponent,
  ],
})
export class ArticleComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('dataTrigger') readonly dataTrigger: ElementRef;
  @ViewChild('externalRecommendationsBlock', { static: false }) readonly externalRecommendationsBlock: ElementRef<HTMLDivElement>;

  readonly articleCardType = ArticleCardTypes;
  readonly ArticleBodyType = ArticleBodyType;
  readonly MinuteToMinuteState = MinuteToMinuteState;
  isUserAdultChoiceDefault = false;
  dontMissThisArticleDefault = null;
  article: Article;
  articleSlug = '';
  categorySlug = '';
  lowPriorityArticles: ArticleCard[] = [];
  highPriorityArticles: ArticleCard[] = [];
  externalRecommendation: ArticleCard[] = [];
  videos: ArticleCard[] = [];
  galleries: Record<string, GalleryDetails> = {};
  isUserAdultChoice: boolean;
  metaData: IMetaData | null = null;
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  mobile_pr_cikkfix?: Advertisement[];
  isTestingEnv = false;
  isDesktopAd: boolean = this.utilsService.isBrowser() && window?.innerWidth > 768;
  isMobileAd: boolean = this.utilsService.isBrowser() && window?.innerWidth <= 768;
  isErdon = this.portalConfig.portalName === 'ERDON';
  afterDot = this.isErdon ? '.ro' : '.hu';
  platformName = this.portalConfig.portalName.charAt(0) + this.portalConfig.portalName.substring(1).toLowerCase();
  tereloUrl: string =
    `https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=${this.portalConfig.portalName.toLowerCase()}${
      this.afterDot
    }&traffickingPlatforms=${this.platformName}%20Nat%C3%ADv` + `&domain=${this.portalConfig.portalName}`;
  notColumnFilteredAds?: AdvertisementsByMedium;
  url: string | undefined;

  sponsor_line: AdvertisementVariablesByMediums | undefined;
  adverts: AdvertisementsByMedium | undefined;
  gameDetails: GameDetails | undefined;
  dontMissThisArticle: { columnSlug: string; publishDate: string; slug: string; title: string } | undefined;
  isArticleLoaded = false;
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  tags: Tag[] = [];
  embedPrAdvert?: SafeHtml;
  ElectionsBoxStyle = ElectionsBoxStyle;
  private cannonicalUrl: string;
  private readonly unsubscribe$ = new Subject<boolean>();
  isExceptionAdvertEnabled: boolean;
  sponsoredTag?: SponsoredTag;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly galleryService: GalleryApiService,
    private readonly seo: SeoService,
    public portalConfig: PortalConfigService,
    private readonly embedding: EmbeddingService,
    private readonly utilsService: UtilService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly schema: JsonLDService,
    private readonly storage: StorageService,
    private readonly analyticsService: AnalyticsService,
    private readonly formatDate: FormatDatePipe,
    private readonly galleryScreenService: GalleryScreenService,
    private readonly scrollPositionService: ScrollPositionService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly sanitizer: DomSanitizer,
    private readonly personalizedRecommendationService: PersonalizedRecommendationService,
    private readonly autoArticleBodyAd: AutoArticleBodyAdService,
    public readonly electionsService: ElectionsService,
    private readonly megyeiUtils: MegyeiUtils
  ) {}

  get galleriesData(): Record<string, GalleryData> {
    return this.galleries as unknown as Record<string, GalleryData>;
  }

  ngOnInit(): void {
    this.megyeiUtils.createScriptWithSrc('https://telegram.org/js/telegram-widget.js?21', false);

    this.route.data
      .pipe(
        map((res) => {
          const { article, recommendations, articleSlug, categorySlug, url, foundationTagSlug, foundationTagTitle } = res[
            'articlePageData'
          ] as ArticleResolverData;

          let body = article?.data?.body;
          this.autoArticleBodyAd.init(body);
          body = this.autoArticleBodyAd.autoAd() as any;

          this.foundationTagSlug = foundationTagSlug;
          this.foundationTagTitle = foundationTagTitle;
          this.article = { ...article?.data, body: this.#prepareArticleBody(body) };
          this.tags = this.article?.tags;

          this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');

          this.gameDetails = this.article?.games ? this.article?.games?.[0]?.blocks?.[0] : undefined;
          this.articleSlug = articleSlug;

          this.categorySlug = categorySlug;
          this.adPageType = `column_${this.article.primaryColumn?.parent?.slug || this.categorySlug}`;

          this.url = url;
          this.cannonicalUrl = this.article.seo?.seoCanonicalUrl || this.article?.canonicalUrl || (url ? `${this.seo.hostUrl}/${url}` : this.seo.hostUrl);
          this.seo.updateCanonicalUrl(this.cannonicalUrl ?? '', {
            addHostUrl: false,
            skipSeoMetaCheck: true,
          });
          const isWithoutAds = this.article?.withoutAds;
          this.sponsoredTag = article.meta?.['sponsoredTag'];

          this.isUserAdultChoice = this.storage.getSessionStorageData('isAdultChoice', this.isUserAdultChoiceDefault) ?? false;
          this.dontMissThisArticle = this.storage.getLocalStorageData('dontMissThisArticle', this.dontMissThisArticleDefault) ?? undefined;

          const { lowPriorityArticles, highPriorityArticles, videos } = recommendations?.data ?? {
            externalRecommendation: [],
            videos: [],
            lowPriorityArticles: [],
            highPriorityArticles: [],
          };
          this.lowPriorityArticles = lowPriorityArticles;
          this.highPriorityArticles = highPriorityArticles;
          this.getPersonalizedRecommendations();
          this.videos = videos;
          // this.setMetaData();
          this.loadEmbeddedGalleries();

          // this.gtagExtraDimension(this.article?.technicalMetaTag);

          this.adStoreAdo.setArticleParentCategory(this.adPageType);

          this.setAdvertisementMeta(this.article.tags);

          if (this.utilsService.isBrowser()) {
            setTimeout(() => {
              this.analyticsService.sendPageView({
                pageCategory: this.categorySlug,
                customDim2: this.article?.topicLevel1,
                customDim1: this.article?.aniCode,
                title: this.article.title,
                articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
                publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
                lastUpdatedDate: this.formatDate.transform(
                  this.article.lastUpdated ? this.article.lastUpdated : (this.article.publishDate as Date),
                  'dateTime'
                ),
              });
            }, 0);
          }

          this.setMetaData();
          this.applySchemaOrg(this.article);
          this.isArticleLoaded = true;

          this.isExceptionAdvertEnabled = article?.data.isExceptionAdvertEnabled;

          return isWithoutAds;
        }),
        tap(() => this.resetAds()),
        tap((withoutAds) => (withoutAds ? this.adStoreAdo.disableAds() : this.adStoreAdo.enableAds())),
        switchMap(() => this.adStoreAdo.advertisemenets$),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((adverts): void => {
        const ads = this.adStoreAdo.separateAdsByMedium(adverts ?? [], this.adPageType, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE);
        const notColumnFilteredAds = this.adStoreAdo.separateAdsByMedium(adverts ?? []);

        this.notColumnFilteredAds = notColumnFilteredAds;

        this.mobile_pr_cikkfix = [
          notColumnFilteredAds?.mobile?.prcikkfix_1,
          notColumnFilteredAds?.mobile?.prcikkfix_2,
          notColumnFilteredAds?.mobile?.prcikkfix_3,
          notColumnFilteredAds?.mobile?.prcikkfix_4,
          notColumnFilteredAds?.mobile?.prcikkfix_5,
        ];

        this.adverts = ads;
        this.sponsor_line = { desktop: ads?.desktop?.szponzorcsik, mobile: ads?.mobile?.szponzorcsik };

        this.adStoreAdo.onArticleLoaded();
        this.changeRef.detectChanges();
      });

    setTimeout(() => {
      this.scrollPositionService.setupScrollPositionListener();
    }, 0);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.adStoreAdo.advertMeta$.next({ keys: '', vars: '' });
    this.adStoreAdo.setArticleParentCategory('');
    this.adStoreAdo.isArticleLoaded$.next(false);
    this.adStoreAdo.enableAds(); // Ha letiltottuk a hirdetéseket egy cikkben és más oldalra navigálunk, újra kell engedélyezni
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
    this.triggerDataLayer();
    this.scrollPositionService.scrollToLastPosition();
  }

  setSourceRoute(): void {
    this.galleryScreenService.setSourceRoute(this.url ?? '');
  }

  handleUserChoice(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
    this.triggerDataLayer();
  }

  resetAds(): void {
    this.adverts = undefined;
    this.sponsor_line = undefined;
    this.mobile_pr_cikkfix = [];
    this.changeRef.detectChanges();
  }

  getPersonalizedRecommendations(): void {
    if (this.utilsService.isBrowser()) {
      this.personalizedRecommendationService
        .getPersonalizedRecommendations()
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe((data: ArticleCard[]): void => {
          this.externalRecommendation = data;
          this.changeRef.detectChanges();
        });
    }
  }

  private observeArticleEnd(): void {
    const dataTriggerElement = this.dataTrigger?.nativeElement;
    const observer = new IntersectionObserver((entries) => {
      console.log('ENTRIES:', entries);
      entries.forEach(({ isIntersecting }) => {
        console.log('INTERSECTING:', isIntersecting);

        if (isIntersecting && dataTriggerElement) {
          this.sendEcommerceEvent();
          observer.unobserve(dataTriggerElement);
        }
      });
    });
    if (dataTriggerElement) {
      observer.observe(dataTriggerElement);
    }
  }

  private sendEcommerceEvent(): void {
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.article.id}`,
      title: this.article.title,
      articleSlug: this.route.snapshot.params['articleSlug'] ? this.route.snapshot.params['articleSlug'] : 'cikk-elonezet',
      category: this.article?.primaryColumn?.title,
      articleSource: this.article.articleSource ? this.article.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform(this.article.lastUpdated ? this.article.lastUpdated : (this.article.publishDate as Date), 'dateTime'),
    });
  }

  private applySchemaOrg(articleResponse: Article): void {
    const { title, publishDate, lastUpdated, lead, thumbnail, avatar } = articleResponse || {};
    const seoTitle = articleResponse?.seo?.seoTitle || '';

    const newsArticle: ArticleSchema = {
      '@type': 'NewsArticle',
      headline: seoTitle && seoTitle.length > 0 ? seoTitle : title,
      alternativeHeadline: seoTitle && seoTitle.length > 0 ? seoTitle : title,
      image: thumbnail,
      url: this.url ?? '',
      mainEntityOfPage: `${this.seo.hostUrl}/${this.url}`,
      description: lead,
      dateModified: lastUpdated,
      author: getArticleAuthors(this.article, this.seo.hostUrl, { hasAuthorPageSlug: true }),
      datePublished: publishDate,
      publisher: {
        '@type': 'Organization',
        name: 'Mediaworks Hungary Zrt.',
        logo: { '@type': 'ImageObject', height: '100', width: '100', url: avatar },
      },
    };

    this.schema.removeStructuredData();
    this.schema.insertSchema(newsArticle);
  }

  private loadEmbeddedGalleries(): void {
    const gallerySubs = (this.article.body as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0]?.value?.slug));
    forkJoin(gallerySubs)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries[gallery.id] = gallery;
        });
        this.changeRef.detectChanges();
      });
  }

  private setMetaData(): void {
    const { lead, thumbnail, publicAuthor, publishDate } = this.article || {};
    if (!this.article) {
      return;
    }
    const title = this.article.seo?.seoTitle || this.article.title;
    const metaData: IMetaData & { index: string } = {
      title: title,
      description: this.article.seo?.seoDescription || lead || this.portalConfig.portalSubtitle,
      ogTitle: this.article.title,
      ogImage: thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
      index: this.article.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
    };

    this.seo.setMetaData(metaData);
    this.metaData = metaData;
  }

  private triggerDataLayer(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
          if ('IntersectionObserver' in window) {
            this.observeExternalRecommendations();
            this.observeArticleEnd();
          }
        });
      }, 1000);
    }
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({ ...detail, ...this.#prepareArticleBodyDetail(detail, bodyPart.type) })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: { mobile: `mobilinterrupter_${advertIndex}`, desktop: `desktopinterrupter_${advertIndex++}` },
      }),
    }));
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = { ...detail, value: previewBackendArticleToArticleCard(detail.value || {}) };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }

  private observeExternalRecommendations(): void {
    if (!this.externalRecommendationsBlock?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting && this.externalRecommendation) {
          this.personalizedRecommendationService.sendPersonalizedRecommendationAv(this.externalRecommendation).subscribe();
          observer.unobserve(this.externalRecommendationsBlock.nativeElement);
        }
      });
    });
    observer.observe(this.externalRecommendationsBlock.nativeElement);
  }

  private setAdvertisementMeta(tags: Tag[]): void {
    if (!tags?.length) {
      return;
    }
    const currentValue = this.adStoreAdo.advertMeta$.getValue();
    const keys = tags.map((tag) => tag.slug).toString();
    const vars = currentValue.vars;

    this.adStoreAdo.advertMeta$.next({ keys, vars });
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
