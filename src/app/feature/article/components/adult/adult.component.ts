import { Location } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IMetaData, SeoService, StorageService } from '@trendency/kesma-core';

@Component({
  selector: 'app-adult',
  templateUrl: './adult.component.html',
  styleUrls: ['./adult.component.scss'],
})
export class AdultComponent implements OnInit {
  @Output() isUserAdult = new EventEmitter<boolean>();
  @Input() metaData: IMetaData;

  constructor(
    private readonly seo: SeoService,
    private readonly storage: StorageService,
    private readonly location: Location
  ) {}

  ngOnInit(): void {
    this.seo.setMetaData({
      ...this.metaData,
    });
  }

  handleUserChoice(isAdult: boolean): void {
    this.isUserAdult.emit(isAdult);
    this.storage.setSessionStorageData('isAdultChoice', isAdult);

    if (!isAdult) {
      this.location.back();
    }
  }
}
