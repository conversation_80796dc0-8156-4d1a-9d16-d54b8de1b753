@use 'shared' as *;

.adult {
  background: $black;
  color: $white;
  padding: 95px 0;
  overflow: hidden;
  @include media-breakpoint-down(sm) {
    padding: 30px 0;
  }

  .wrapper {
    width: 680px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .left {
      width: 200px;
      @include media-breakpoint-down(sm) {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
      }

      .warning-circle {
        font-weight: 600;
        font-size: 50px;
        line-height: 100%;
        font-family: $font-inter;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 120px;
        border: 10px solid $red;
        border-radius: 50%;
      }
    }

    .right {
      width: calc(100% - 200px);
      @include media-breakpoint-down(sm) {
        width: 100%;
      }

      .title {
        font-weight: 500;
        font-size: 30px;
        line-height: 45px;
        margin-bottom: 15px;
        @include media-breakpoint-down(sm) {
          font-weight: 500;
          font-size: 20px;
          line-height: 30px;
        }
      }

      .lead {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 15px;
      }

      .text {
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: $grey-16;
      }
    }

    .bottom {
      width: calc(100% + 40px);
      margin: 30px -20px 0;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;

      .button {
        width: 320px;
        max-width: 100%;
        padding: 12px;
        background: $grey-7;
        color: $black;
        border-radius: 6px;
        margin: 7.5px 20px;
        text-align: center;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        cursor: pointer;

        &.light {
          background: $grey-22;
        }
      }
    }
  }
}
