import { ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  Article,
  ArticleBodyType,
  ArticleCard,
  GalleryData,
  GalleryDetails,
  MinuteToMinuteState,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { NgFor, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import {
  ArticleCardComponent,
  ArticleCardTypes,
  DEFAULT_PUBLISH_DATE_FORMAT,
  DossierRecommendationComponent,
  GalleriesBottomComponent,
  GalleryCardComponent,
  PreviewAsArticleCardPipe,
  QuizComponent,
  SocialRowComponent,
  TagRowComponent,
  VotingBoxComponent,
  WysiwygBoxComponent,
} from '../../../../shared';
import { PublishDatePipe } from '@trendency/kesma-core';
import { ArticleService } from '../../article.service';
import { GalleryScreenService } from '../../../gallery-screen/gallery-screen.service';


@Component({
  selector: 'app-note',
  templateUrl: './note.component.html',
  styleUrls: ['./note.component.scss'],
  imports: [
    NgFor,
    NgSwitch,
    NgSwitchCase,
    WysiwygBoxComponent,
    DossierRecommendationComponent,
    QuizComponent,
    VotingBoxComponent,
    ArticleCardComponent,
    NgIf,
    GalleryCardComponent,
    RouterLink,
    SocialRowComponent,
    NgTemplateOutlet,
    TagRowComponent,
    GalleriesBottomComponent,
    PublishDatePipe,
    PreviewAsArticleCardPipe,
  ],
})
export class NoteComponent implements OnInit, OnDestroy {
  @Input() article: Article;
  @Input() galleries: Record<string, GalleryDetails> = {};
  @Input() highPriorityArticles: ArticleCard[];
  @Input() lowPriorityArticles: ArticleCard[];
  @Input() articleUrl: string;
  adverts: AdvertisementsByMedium | undefined;
  recommendedArticles: ArticleCard[];
  recommendedByAuthor: any;
  readonly articleCardType = ArticleCardTypes;
  readonly ArticleBodyType = ArticleBodyType;
  readonly MinuteToMinuteState = MinuteToMinuteState;
  private readonly destroy$ = new Subject<boolean>();

  constructor(
    private readonly articleService: ArticleService,
    private readonly route: ActivatedRoute,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly galleryScreenService: GalleryScreenService,
    private readonly changeRef: ChangeDetectorRef,

  ) {}

  get galleriesData(): Record<string, GalleryData> {
    return this.galleries as unknown as Record<string, GalleryData>;
  }

  ngOnInit(): void {
    this.recommendedArticles = [...this.highPriorityArticles, ...this.lowPriorityArticles].slice(0, 6);

    this.articleService
      .searchArticleByKeyword({ author: this.article?.publicAuthor, exclude_articles: '1' }, 0, 6)
      .pipe(takeUntil(this.destroy$))
      .subscribe(({ data }): void => {
        const articleList = data;
        this.recommendedByAuthor = articleList as any;
        this.changeRef.detectChanges();
      });

    this.route.data
      .pipe(
        tap(() => this.resetAds()),
        switchMap((): Observable<Advertisement[]> => {
          return this.adStoreAdo.advertisemenets$;
        }),
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.destroy$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.changeRef.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  setSourceRoute(): void {
    this.galleryScreenService.setSourceRoute(this.articleUrl);
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
