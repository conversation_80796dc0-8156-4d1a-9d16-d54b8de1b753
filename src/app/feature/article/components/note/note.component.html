<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <app-wysiwyg-box [html]="wysiwygDetail?.value"></app-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossier">
        <app-dossier-recommendation [subsequentDossier]="element?.details[0]?.value"></app-dossier-recommendation>
      </ng-container>
      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <app-quiz [quiz]="element?.details[0]?.value"></app-quiz>
      </ng-container>

      <div class="block-voting" *ngSwitchCase="ArticleBodyType.Voting">
        <app-voting-box [voting]="element?.details[0]?.value" type="article"></app-voting-box>
      </div>

      <div class="block-recommendation" *ngSwitchCase="ArticleBodyType.Article">
        <app-article-card [styleID]="articleCardType.STYLE7" [article]="element?.details[0]?.value | previewAsArticleCard" [isTagVisible]="false">
        </app-article-card>
      </div>
      <div class="block-gallery" *ngSwitchCase="ArticleBodyType.Gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id]">
          <app-gallery-card
            [data]="galleries[element?.details[0]?.value?.id]"
            [isInsideAdultArticleBody]="article?.isAdultsOnly"
            [routerLink]="['/', 'galeria', galleries[element?.details[0]?.value?.id].slug, 1]"
            [mobileClass]="'big'"
            [isEmbedded]="true"
            (click)="setSourceRoute()"
          >
          </app-gallery-card>
        </ng-container>
      </div>
    </ng-container>
  </ng-container>
</ng-template>

<section class="note">
  <div class="wrapper">
    <div class="note-header">
      <div class="note-date-row">
        <label>Jegyzet</label>
        <time>{{ article?.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</time>
      </div>
      <div class="note-title-row">
        <h1 class="note-title">{{ article?.title }}</h1>
        <div class="note-title-decor"></div>
      </div>
      <a [routerLink]="['/', 'jegyzetek', article?.publicAuthor?.toLowerCase()]" class="note-credit">{{ article?.publicAuthor }}</a>
      <app-social-row></app-social-row>
      <div class="note">
        <!-- body -->
        <ng-container [ngTemplateOutlet]="bodyContent" [ngTemplateOutletContext]="{ body: article.body }"></ng-container>
      </div>
      <app-tag-row [tags]="article.tags"></app-tag-row>
      <div class="block-show-comments">
        <button class="btn grey show-comments-btn">Hozzászólások</button>
      </div>
    </div>
  </div>
  <div *ngIf="recommendedByAuthor" class="bottom-part">
    <app-galleries-bottom
      [leftBlockTitle]="'További jegyzetek a szerzőtől'"
      [relatedArticles]="recommendedByAuthor"
      [adverts]="adverts"
      ad="medium_rectangle_1_top"
      [relatedArticlesSecondary]="recommendedArticles"
    ></app-galleries-bottom>
  </div>
</section>
