@use 'shared' as *;

.note {
  .wrapper {
    width: 920px;
    max-width: 100%;
    padding-top: 50px;
    padding-bottom: 30px;
    @include media-breakpoint-down(md) {
      padding: 20px;
    }

    .note-header {
      .note-date-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 20px;
        line-height: 30px;
        margin-bottom: 20px;
        position: relative;
        z-index: 2;
      }

      .note-title-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
        @include media-breakpoint-down(md) {
          align-items: flex-start;
        }

        .note-title {
          font-style: italic;
          font-family: Zilla Slab;
          font-weight: 500;
          font-size: 60px;
          line-height: 100%;
          max-width: 811px;
          @include media-breakpoint-down(md) {
            font-size: 30px;
          }
        }

        .note-title-decor {
          width: 109px;
          height: 70px;
          @include icon('icons/note-decor.svg');
          @include media-breakpoint-down(md) {
            margin-top: -50px;
            margin-right: -5px;
            position: absolute;
            right: 20px;
            z-index: -1;
          }
        }
      }

      .note-credit {
        display: block;
        margin-bottom: 30px;
        font-size: 20px;
        line-height: 30px;
      }

      .social-row {
        margin-bottom: 30px;
      }
    }

    .note {
      line-height: 34px;
      margin-bottom: 30px;

      p {
        margin-bottom: 40px;
        font-weight: 300;
        font-size: 16px;
        line-height: 34px;
        @include media-breakpoint-down(md) {
          font-weight: normal;
          font-size: 20px;
          line-height: 40px;
          margin-bottom: 30px;
        }
      }
    }

    .block-show-comments {
      display: block;
      margin-top: 50px;
      margin-bottom: $block-bottom-margin;

      @include media-breakpoint-down(md) {
        margin-bottom: $block-bottom-margin-mobile;
      }

      .show-comments-btn {
        display: block;
        width: 100%;
        height: 50px;
      }
    }
  }
}

.bottom-part {
  width: $global-wrapper-width;
  margin: 0 auto;
  max-width: calc(100% - 30px);
}
