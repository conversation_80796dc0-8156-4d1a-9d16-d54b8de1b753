@use 'shared' as *;

section.article {
  .wrapper {
    > .left-column {
      .top-section {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .short-lead {
          font-size: 20px;
          line-height: 21px;
          position: relative;

          &,
          &:active,
          &:visited,
          &:focus,
          &:link {
            color: $base-text-color;
          }

          &.dossier-title {
            padding-left: 41px;

            &,
            &:active,
            &:visited,
            &:focus,
            &:link {
              color: $base-text-color;
            }

            &:before {
              content: ' ';
              display: inline-block;
              @include icon('icons/dossier.svg');
              width: 27px;
              height: 21px;
              margin-right: 14px;
              position: absolute;
              left: 0;
            }
          }
        }

        .time {
          color: $grey-7;
          font-size: 14px;
          line-height: 21px;
        }
      }

      h1 {
        font-weight: 500;
        font-size: 60px;
        line-height: 70px;
        margin-bottom: 30px;
        @include media-breakpoint-down(md) {
          font-size: 30px;
          line-height: 40px;
        }
      }

      .lead {
        font-weight: normal;
        font-size: 30px;
        line-height: 50px;
        margin-bottom: $block-bottom-margin;
        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
          font-size: 20px;
          line-height: 30px;
        }
      }

      .article-embed-pr-advert {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 10px auto;
      }

      app-social-row {
        margin-bottom: calc(#{$block-bottom-margin} - 15px);

        @include media-breakpoint-down(md) {
          margin-bottom: calc(#{$block-bottom-margin-mobile} - 15px);
        }
      }

      .ad-spacer {
        height: 30px;
      }

      .info-line {
        margin-bottom: 30px;
        border-top: 1px solid $white-6;
        border-bottom: 1px solid $white-6;
        display: flex;
        flex-wrap: wrap;
        padding: 6px;
        justify-content: space-between;

        .left {
          text-transform: uppercase;
          color: $black;
          font-size: 16px;
          font-weight: 500;
          display: flex;
          height: 30px;
          align-items: center;

          @include media-breakpoint-down(sm) {
            font-size: 12px;
          }

          .author-pic {
            width: 30px;
            height: 30px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            margin-right: 10px;
          }
        }
      }

      .block-voting {
        width: 680px;
        max-width: 100%;
        margin: 0 auto $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin: 0 auto $block-bottom-margin-mobile;
        }
      }

      .block-recommendation {
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }

        @include media-breakpoint-down(sm) {
          width: calc(100% + 30px);
          margin-left: -15px;
          margin-right: -15px;

          app-article-card {
            .article-block[class] {
              border-radius: 0;
              border-left-width: 0;
              border-right-width: 0;
              padding-left: 0;
              padding-right: 15px;
            }

            .style-7 {
              .article-block {
                padding-left: 15px;
              }
            }
          }
        }
      }

      .block-big-image {
        margin-bottom: $block-bottom-margin;
        margin-top: 35px;

        @include media-breakpoint-down(md) {
          width: calc(100% + 30px);
          margin: 20px -15px $block-bottom-margin-mobile;
        }

        .big-image {
          width: 100%;
          object-fit: cover;
        }
      }

      app-quiz,
      app-dossier-recommendation,
      app-tag-row {
        display: block;
        margin-bottom: $block-bottom-margin;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }
      }

      .minute-by-minute-list {
        app-minute-by-minute {
          display: block;
          margin-bottom: 30px;
        }

        .minute-by-minute-live-header {
          font-weight: 600;
          font-size: 24px;
          line-height: 40px;
          margin-bottom: 30px;
        }
      }

      app-section-header {
        .section-header {
          margin-bottom: 0;
        }
      }

      .cards-container {
        padding-top: 30px;
        display: flex;
        margin-bottom: $block-bottom-margin;
        flex-wrap: wrap;
        width: calc(100% + 30px);
        margin-left: -15px;
        margin-right: -15px;

        @include media-breakpoint-down(md) {
          margin-bottom: $block-bottom-margin-mobile;
        }

        .full-row {
          width: 100%;
        }
      }

      .mindmegette {
        display: block;
        margin: 0 auto 30px;
        border: none;
        overflow: hidden;
        background: #ffffff;
        width: 640px;
        height: 360px;

        @include media-breakpoint-down(sm) {
          width: 300px;
          height: 250px;
        }
      }

      .block-gallery {
        @include media-breakpoint-down(sm) {
          margin: 0 -15px;
        }
      }
    }

    > aside {
      .cards-container {
        display: block;
      }
    }
  }

  .raw-html-embed {
    margin: auto;
    width: 100%;
    text-align: center;

    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
    display: block;

    > * {
      margin: 0 auto;
    }

    .flourish-embed {
      width: 100%;
    }
  }

  .table {
    max-width: 100%;
    overflow: auto;
  }

  .thumbnail-info {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 40px;
    position: relative;
    justify-content: center;
    background-color: $grey-22;
    @include article-before-line();
    padding-left: 20px;
    font-family: $font-family;
    letter-spacing: 0em;
    text-align: left;
    padding-top: 5px;
    padding-bottom: 5px;

    &:before {
      background-color: $primary-color;
    }

    .description {
      margin-right: auto;
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      @include media-breakpoint-down(sm) {
        font-size: 14px;
      }
    }

    .source {
      margin-right: auto;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      @include media-breakpoint-down(sm) {
        font-size: 12px;
      }
    }

    .photographer {
      margin-right: auto;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      @include media-breakpoint-down(sm) {
        font-size: 12px;
      }
    }
  }

  .desktop {
    @include media-breakpoint-up(md) {
      display: flex;
    }
  }

  .mobile {
    @include media-breakpoint-down(md) {
      display: flex;
      position: relative;
      width: 100vw;
      margin-left: -15px;
      margin-top: -30px;
    }
  }

  .body-image {
    display: flex;
    position: relative;
    width: 100%;
  }
}

.desktop-erdon-ad {
  @include media-breakpoint-down(md) {
    display: none;
  }
}

.mobile-erdon-ad {
  @include media-breakpoint-up(md) {
    display: none;
  }
}

app-elections-box.article-elections-diverter {
  margin: 30px 0;
}
