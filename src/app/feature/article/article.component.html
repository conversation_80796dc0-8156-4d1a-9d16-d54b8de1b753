<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly; else adultGate" class="article">
  <app-adult (isUserAdult)="handleUserChoice($event)" [metaData]="metaData"></app-adult>
</ng-container>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <app-wysiwyg-box [html]="wysiwygDetail?.value"></app-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Advert">
        <kesma-advertisement-adocean *ngIf="notColumnFilteredAds?.mobile?.[element.adverts.mobile] as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="notColumnFilteredAds?.desktop?.[element.adverts.desktop] as ad" [ad]="ad"></kesma-advertisement-adocean>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossier">
        <app-dossier-recommendation [excludedArticleId]="article.id" [subsequentDossier]="element?.details[0]?.value"></app-dossier-recommendation>
      </ng-container>
      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <app-quiz [quiz]="element?.details[0]?.value"></app-quiz>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.Voting" class="block-voting">
        <app-voting-box [voting]="element?.details[0]?.value" type="article"></app-voting-box>
      </div>

      <div *ngSwitchCase="ArticleBodyType.MediaVideo" class="block-voting">
        <app-article-video [componentObject]="element?.details[0]?.value"></app-article-video>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Game" class="">
        <app-article-game [gameDetails]="element?.details[0]?.value"></app-article-game>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Article" class="block-recommendation">
        <app-article-card [article]="element?.details[0]?.value" [isTagVisible]="false" [styleID]="articleCardType.STYLE7"></app-article-card>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Gallery" class="block-gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id]">
          <app-gallery-card
            (click)="setSourceRoute()"
            [data]="galleries[element?.details[0]?.value?.id]"
            [isEmbedded]="true"
            [isInsideAdultArticleBody]="article?.isAdultsOnly"
            [mobileClass]="'big'"
            [routerLink]="['/', 'galeria', galleries[element?.details[0]?.value?.id]?.slug, 1]"
          >
          </app-gallery-card>
        </ng-container>
      </div>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #adultGate>
  <section *ngIf="!article?.isNotebook; else note" class="article">
    <div *ngIf="article" [ngClass]="{ 'with-aside': !foundationTagSlug && !article.isAlternativeView }" class="wrapper">
      <article class="left-column">
        <div class="top-section">
          <ng-container *ngIf="article.dossier?.slug; else normalHeader">
            <a [routerLink]="['/', 'dosszie', article.dossier?.slug]" class="short-lead dossier-title">{{ article.dossier?.title }}</a>
          </ng-container>
          <ng-template #normalHeader>
            <div *ngIf="article?.preTitle; else column" class="short-lead normal-header">{{ article.preTitle }}</div>

            <ng-template #column>
              <a [routerLink]="['/', 'rovat', article.primaryColumn?.slug]" class="short-lead column-header">{{ article.primaryColumn.title }}</a>
            </ng-template>
          </ng-template>
          <p class="time">{{ article.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</p>
        </div>

        <h1>{{ article.title }}</h1>

        <app-tag-row [foundationTagSlug]="foundationTagSlug" [foundationTagTitle]="foundationTagTitle" [tags]="article.tags"></app-tag-row>

        <kesma-advertisement-adocean *ngIf="sponsor_line?.desktop as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="sponsor_line?.mobile as ad" [ad]="ad"></kesma-advertisement-adocean>

        <p class="lead">
          {{ article.lead }}
        </p>

        <!-- ROADBLOCK 1 -->
        <div [ngStyle]="{ 'margin-top': '30px' }">
          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.roadblock_1 as ad"
            [ad]="ad"
            [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
          ></kesma-advertisement-adocean>
          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobiltop as ad" [ad]="ad"></kesma-advertisement-adocean>
          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
            [ad]="ad"
            [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
          ></kesma-advertisement-adocean>
        </div>
        <!-- ROADBLOCK 1 -->

        <div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="article-embed-pr-advert"></div>

        <!-- Detail Desktop Middle 1 Revive Adserver iFrame Tag -->
        <iframe
          *ngIf="isDesktopAd && portalConfig?.portalName === 'ERDON'"
          allow="autoplay"
          class="desktop-erdon-ad"
          frameborder="0"
          height="250"
          id="adadc646"
          name="adadc646"
          scrolling="no"
          src="https://wpmania.ro/ads/www/delivery/afr.php?zoneid=19&amp;cb=INSERT_RANDOM_NUMBER_HERE"
          width="920"
          ><a href="https://wpmania.ro/ads/www/delivery/ck.php?n=aadd18ba&amp;cb=INSERT_RANDOM_NUMBER_HERE" target="_blank"
            ><img alt="" border="0" src="https://wpmania.ro/ads/www/delivery/avw.php?zoneid=19&amp;cb=INSERT_RANDOM_NUMBER_HERE&amp;n=aadd18ba" /></a
        ></iframe>

        <!-- Detail Mobil Content 1 Revive Adserver iFrame Tag -->
        <iframe
          *ngIf="isMobileAd && portalConfig?.portalName === 'ERDON'"
          allow="autoplay"
          class="mobile-erdon-ad"
          frameborder="0"
          height="250"
          id="a978562a"
          name="a978562a"
          scrolling="no"
          src="https://wpmania.ro/ads/www/delivery/afr.php?zoneid=21&amp;cb=INSERT_RANDOM_NUMBER_HERE"
          width="300"
          ><a href="https://wpmania.ro/ads/www/delivery/ck.php?n=af6469a4&amp;cb=INSERT_RANDOM_NUMBER_HERE" target="_blank"
            ><img alt="" border="0" src="https://wpmania.ro/ads/www/delivery/avw.php?zoneid=21&amp;cb=INSERT_RANDOM_NUMBER_HERE&amp;n=af6469a4" /></a
        ></iframe>

        <app-social-row></app-social-row>

        <ng-container *ngIf="!article.thumbnail || article.hideThumbnailFromBody">
          <div class="ad-spacer"></div>
        </ng-container>

        <div *ngIf="article?.publicAuthor" class="info-line">
          <div class="left">
            <div
              [ngClass]="{ 'author-pic': article?.avatar }"
              [ngStyle]="article?.avatar && { 'background-image': 'url(' + article?.avatar + ')' }"
              [title]="article?.publicAuthor"
              trImageLazyLoad
            ></div>
            @if (article?.publicAuthorSlug) {
              <a [routerLink]="['/', 'szerzo', article?.publicAuthorSlug]">
                {{ article.publicAuthor }}
              </a>
            } @else {
              {{ article.publicAuthor }}
            }
          </div>
        </div>

        @if (!(articleSlug === 'uj-praktiker-aruhaz-nyilt-veszpremben') || !isErdon) {
          <app-article-card
            *ngIf="dontMissThisArticle && dontMissThisArticle !== null"
            [article]="dontMissThisArticle | dontMissThisArticleToArticleCard"
            [isTagVisible]="false"
            [styleID]="articleCardType.ArticleBodyRecommendation"
            class="dont-miss"
          />
        }
        <app-article-game *ngIf="gameDetails" [gameDetails]="gameDetails"></app-article-game>

        <div *ngIf="article.thumbnail && !article.hideThumbnailFromBody" class="block-big-image">
          <img
            [data]="article?.thumbnailFocusedImages"
            [displayedAspectRatio]="{ desktop: '16:9' }"
            [displayedUrl]="article?.thumbnail"
            [alt]="article?.title"
            class="big-image"
            loading="eager"
            withFocusPoint
          />
          <div *ngIf="article?.thumbnailInfo?.caption || article?.thumbnailInfo?.source || article?.thumbnailInfo?.photographer" class="thumbnail-info">
            <p *ngIf="article?.thumbnailInfo?.caption" class="description">{{ article?.thumbnailInfo?.caption }}</p>
            <p *ngIf="article?.thumbnailInfo?.source" class="source">Forrás: {{ article?.thumbnailInfo?.source }}</p>
            <p *ngIf="article?.thumbnailInfo?.photographer" class="photographer">Fotó: {{ article?.thumbnailInfo?.photographer }}</p>
          </div>
        </div>

        <!-- body -->
        <ng-container [ngTemplateOutletContext]="{ body: article.body }" [ngTemplateOutlet]="bodyContent"></ng-container>

        <div *ngIf="article?.minuteToMinute !== MinuteToMinuteState.NOT" class="minute-by-minute-list">
          <h3 *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING" class="minute-by-minute-live-header">
            Élő közvetítésünket lejjebb görgetve olvashatják.
          </h3>
          <app-minute-by-minute *ngFor="let block of article.minuteToMinuteBlocks" [data]="block"></app-minute-by-minute>
        </div>

        <!-- dosszie cikk eseten a dosszie elemei -->
        <ng-container *ngIf="article.dossier?.slug && !sponsoredTag">
          <app-dossier-recommendation [dossier]="article.dossier" [excludedArticleId]="article.id" class="dossier-article"></app-dossier-recommendation>
        </ng-container>

        <!-- Detail Desktop Middle 2 Revive Adserver iFrame Tag -->
        <iframe
          *ngIf="isDesktopAd && portalConfig?.portalName === 'ERDON'"
          allow="autoplay"
          class="desktop-erdon-ad"
          frameborder="0"
          height="250"
          id="ac9a663d"
          name="ac9a663d"
          scrolling="no"
          src="https://wpmania.ro/ads/www/delivery/afr.php?zoneid=20&amp;cb=INSERT_RANDOM_NUMBER_HERE"
          width="920"
          ><a href="https://wpmania.ro/ads/www/delivery/ck.php?n=aa23d731&amp;cb=INSERT_RANDOM_NUMBER_HERE" target="_blank"
            ><img alt="" border="0" src="https://wpmania.ro/ads/www/delivery/avw.php?zoneid=20&amp;cb=INSERT_RANDOM_NUMBER_HERE&amp;n=aa23d731" /></a
        ></iframe>

        <!-- Detail Mobil Content 2 Revive Adserver iFrame Tag -->
        <iframe
          *ngIf="isMobileAd && portalConfig?.portalName === 'ERDON'"
          allow="autoplay"
          class="mobile-erdon-ad"
          frameborder="0"
          height="250"
          id="a598a850"
          name="a598a850"
          scrolling="no"
          src="https://wpmania.ro/ads/www/delivery/afr.php?zoneid=22&amp;cb=INSERT_RANDOM_NUMBER_HERE"
          width="300"
          ><a href="https://wpmania.ro/ads/www/delivery/ck.php?n=a9a8f71d&amp;cb=INSERT_RANDOM_NUMBER_HERE" target="_blank"
            ><img alt="" border="0" src="https://wpmania.ro/ads/www/delivery/avw.php?zoneid=22&amp;cb=INSERT_RANDOM_NUMBER_HERE&amp;n=a9a8f71d" /></a
        ></iframe>

        <div #dataTrigger *ngIf="article"></div>
        <ng-container *ngIf="portalConfig.portalName !== 'KPI'">
          <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>
        </ng-container>

        <!-- Cimke szponzorációs cikkek-->
        @if (sponsoredTag) {
          <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="article?.slug || ''" />
        }

        <!-- külső cikk ajánló blokk -->
        <ng-container *ngIf="externalRecommendation.length && !isErdon">
          <div #externalRecommendationsBlock class="cards-container">
            <ng-container *ngFor="let article of externalRecommendation; let i = index">
              <div class="col-12 {{ foundationTagSlug ? 'col-md-4' : 'col-md-6' }}">
                <app-article-card
                  [article]="article"
                  [isSubTitleVisible]="true"
                  [isTagVisible]="false"
                  [styleID]="articleCardType.STYLE6"
                  class="external-recommendation-article"
                ></app-article-card>
              </div>

              <div *ngIf="i === 5" class="full-row">
                <kesma-advertisement-adocean
                  *ngIf="notColumnFilteredAds?.mobile?.mobilrectangle_ottboxextra as ad"
                  [ad]="ad"
                  [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
                ></kesma-advertisement-adocean>
                <kesma-advertisement-adocean
                  *ngIf="notColumnFilteredAds?.desktop?.roadblock_ottboxextra as ad"
                  [ad]="ad"
                  [style]="{ margin: 'var(--ad-margin)', background: 'var(--ad-bg)' }"
                ></kesma-advertisement-adocean>
              </div>
            </ng-container>
          </div>
        </ng-container>

        <ng-container *ngIf="electionsService.isElections2024Enabled()">
          <app-elections-box
            [desktopWidth]="foundationTagSlug || article.isAlternativeView ? 12 : 9"
            [link]="electionsService.getElections2024Link()"
            [styleID]="ElectionsBoxStyle.DIVERTER"
            class="article-elections-diverter"
          ></app-elections-box>
        </ng-container>

        <!-- ROADBLOCK 2 -->
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <!-- ROADBLOCK 2 -->

        <!-- ROADBLOCK 3 -->
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <!-- ROADBLOCK 3 -->

        <app-article-subscription-box></app-article-subscription-box>

        <!-- ROADBLOCK 4 -->
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_4 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <!-- ROADBLOCK 4 -->

        <app-foundation-recommendation
          *ngIf="foundationTagSlug && articleSlug"
          [articleSlug]="articleSlug"
          [foundationTagSlug]="foundationTagSlug"
          [tags]="tags"
        ></app-foundation-recommendation>

        <!-- ROADBLOCK 5 -->
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_5 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <!-- ROADBLOCK 5 -->

        <!-- cikk ajánló blokk -->
        <ng-container *ngIf="lowPriorityArticles.length && !foundationTagSlug">
          <app-section-header [link]="['/', 'rovat', article.primaryColumn?.slug]" linkTitle="További hírek a témában" sectionTitle="Rovatunkból ajánljuk">
          </app-section-header>

          <div class="cards-container">
            <div *ngFor="let article of lowPriorityArticles" class="col-12 col-md-6">
              <app-article-card [article]="article" [isTagVisible]="false" [isTargetBlank]="true" [styleID]="articleCardType.STYLE5"></app-article-card>
            </div>
          </div>
        </ng-container>

        <!-- ROADBLOCK 6 -->
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_6_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <!-- ROADBLOCK 6 -->

        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilbottom as ad" [ad]="ad"></kesma-advertisement-adocean>
      </article>
      <ng-container *ngIf="!isTestingEnv && !foundationTagSlug">
        <aside *ngIf="article.isAlternativeView === false && isArticleLoaded">
          <app-sidebar [adPageType]="adPageType" [articleId]="article.id" [articleSlug]="articleSlug" [categorySlug]="categorySlug"></app-sidebar>
        </aside>
      </ng-container>
    </div>

    <div *ngIf="mobile_pr_cikkfix?.length" [style]="{ display: 'flex', width: '100%', 'justify-content': 'center', 'flex-direction': 'column' }">
      <ng-container *ngFor="let pr_cik_fix of mobile_pr_cikkfix">
        <kesma-advertisement-adocean *ngIf="pr_cik_fix as ad" [ad]="ad" [style]="{ 'margin-bottom': '20px' }"></kesma-advertisement-adocean>
      </ng-container>
    </div>
  </section>

  <app-newsletter-modal></app-newsletter-modal>

  <ng-template #note>
    <app-note
      [articleUrl]="url"
      [article]="article"
      [galleries]="galleries"
      [highPriorityArticles]="highPriorityArticles"
      [lowPriorityArticles]="lowPriorityArticles"
    >
    </app-note>
  </ng-template>
</ng-template>
