import { Injectable } from '@angular/core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService, BackendPortalConfigService } from '../services';
import { InitResolverData, InitResponse, MenuTreeResponse } from '../definitions';

@Injectable()
export class InitResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly backendPortalConfigService: BackendPortalConfigService
  ) {}

  resolve(): Observable<InitResolverData> {
    return forkJoin({
      init: this.apiService.init().pipe(
        map(({ data }) => {
          this.backendPortalConfigService.setConfig(data.portalConfigs);
          return data;
        }),
        catchError(() => of({} as InitResponse))
      ),
      menu: this.apiService.getMenu().pipe(
        map(({ data }) => data),
        catchError(() => of({} as MenuTreeResponse))
      ),
    });
  }
}
