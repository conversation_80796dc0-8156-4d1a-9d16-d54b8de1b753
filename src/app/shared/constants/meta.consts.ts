import { PortalConfig } from 'src/environments/environment.definitions';
import { IMetaData } from '@trendency/kesma-core';

export const defaultMetaInfo = (portalConfig: PortalConfig): IMetaData & { labelDescriptionMetaSuffix?: string } => ({
  title: `${portalConfig?.portalName} - ${portalConfig.portalSubtitle}`,
  ogTitle: `${portalConfig?.portalName} - ${portalConfig.portalSubtitle}`,
  ogLocale: 'hu_hu',
  ogDescription: portalConfig.portalSubtitle,
  ogSiteName: portalConfig?.portalName,
  twitterSiteName: portalConfig?.twitterSiteName,
  ogType: 'website',
});
