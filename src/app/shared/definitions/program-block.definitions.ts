export type ProgramBlock = Readonly<{
  address: string;
  imageUrl: string;
  isActive: boolean;
  isRecommended: boolean;
  latitude: string;
  lead: string;
  longitude: string;
  phoneNumber: string;
  recommendedFrom: string;
  recommendedTill: string;
  slug: string;
  title: string;
  url: string;
  date?: string;
  programDates?: { startDate: string; endDate: string }[];
}>;
export type BackendProgramBlock = Readonly<{
  address?: string;
  created_at: string;
  dates?: {
    id: string;
    end_date: string;
    start_date: string;
  }[];
  full_size_url?: string;
  id: string;
  is_recommended: string;
  latitude?: string;
  lead?: string;
  locations?: {
    id: string;
    is_active: string;
    slug: string;
    title: string;
  }[];
  longitude?: string;
  phone_number?: string;
  recommended_from?: string;
  recommended_till?: string;
  slug: string;
  thumbnail_url?: string;
  title: string;
  tags?: {
    description: null;
    header_color: string;
    id: string;
    slug: string;
    title: string;
    title_color: string;
  }[];
  url?: string;
}>;
