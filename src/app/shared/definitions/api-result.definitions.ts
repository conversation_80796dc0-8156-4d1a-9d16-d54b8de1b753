export type ApiResponseType = 'list' | 'item';

export type ApiResponseMeta = Readonly<{
  responseType: ApiResponseType;
  dataCount: number;
  requestUrl: string;
  [property: string]: string | number;
}>;

export type ApiResult<T, TMeta = ApiResponseMeta> = Readonly<{
  data: T;
  meta: TMeta;
}>;

export type ApiResponseMetaList = ApiResponseMeta &
  Readonly<{
    responseType: 'list';
    filterable: Record<string, FilterableMeta>;
    orderable: Record<string, OrderableMeta>;
    limitable: LimitableMeta;
  }>;

export type LimitableMeta = Readonly<{
  pageCurrent?: number;
  pageMax?: number;
  rowAllCount?: number;
  rowFrom?: number;
  rowOnPageCount?: number;
}>;

export type FilterableMeta = Readonly<{
  requestQueryKey: string;
  sourceUrl: string;
  selectKeyProperty: string;
  selectDisplayProperty: string;
}>;

export type OrderableMeta = Readonly<Record<string, string | number>>;

export type BackendDataMetaResult<TData = unknown, TMeta = unknown> = Readonly<{
  data: TData;
  meta: TMeta;
}>;
