import { SafeResourceUrl } from '@angular/platform-browser';
import { BreakingNews, PortalConfigData } from '@trendency/kesma-ui';

export type MediaStreamApi = Readonly<{
  hirTv: HirTv;
  karcFm: KarcFm;
  retroRadio?: KarcFm;
}>;

export type KarcFm = Readonly<{
  stream: string;
}>;

export type HirTv = Readonly<{
  subTitle: string;
  stream: string;
  title: string;
}>;

export enum RelatedType {
  COLUMN = 'Column',
  DOSSIER = 'Dossier',
  REGION = 'Region',
  GALLERY_COLLECTION = 'GalleryCollection',
  WEATHER_PAGE = 'WeatherPage',
  ADS_PAGE = 'AdsPage',
  GRIEF_PAGE = 'GriefPage',
  TUNDERSZEPEK_PAGE = 'TunderszepekPage',
  CUSTOM_URL = 'CustomUrl',
  STATIC_PAGE = 'StaticPage',
  DROPDOWN = 'Dropdown',
  ARTICLE = 'Article',
  HOME_PAGE = 'HomePage',
  SEARCH_PAGE = 'SearchPage',
  CUSTOM_BUILT_PAGE = 'CustomBuiltPage',
  NOTE = 'NotebookArticle',
  PROGRAM_RECOMMENDATION = 'ProgramRecommendation',
  PROGRAM_RECOMMENDATION_COLLECTION = 'ProgramRecommendationCollection',
}

export type RelatedArticle = Readonly<{
  id: string;
  title: string;
  slug: string;
  language?: string;
  columnSlug?: string;
  publishDate: { date: string; timezone_type: number; timezone: string };
}>;

export type MenuChild = Readonly<{
  id: string;
  title: string;
  isActive: boolean;
  sort: number;
  targetBlank: boolean;
  relatedType?: RelatedType;
  relatedKey?: string;
  related?: RelatedArticle;
  customUrl?: string;
  children: MenuChild[];
}>;

export type MainMenuChild = MenuChild &
  Readonly<{
    columns?: MenuChild[][];
  }>;

export type MenuTreeResponse = Readonly<{
  footer_0: MenuChild[];
  footer_1: MenuChild[];
  header: MenuChild[];
  header_0: MenuChild[];
  header_1: MenuChild[];
  secondary_header: MenuChild[];
}>;

export type InitResponse = Readonly<{
  dossiers: InitResponseDossier[];
  publications: InitResponsePublication[];
  breakingNews: BreakingNews;
  nameDays?: string;
  idojaras?: InitWeather;
  menuSettings: MediaStreamApi;
  portalConfigs: PortalConfigData;
  apiVersion: string;
  dontMissThisArticle?: {
    columnSlug: string;
    publishDate: string;
    slug: string;
    title: string;
  };
}>;

export type InitResponseDossier = Readonly<{
  title: string;
  slug: string;
}>;

export type InitResponsePublication = Readonly<{
  title: string;
  price: string;
  url: string;
  thumbnail?: string;
  thumbnailCreatedAt?: string;
}>;

export type InitWeather = Readonly<{
  city: string;
  sky_view: string;
  temperature: string;
  icon2: string;
}>;

export type InitResolverData = Readonly<{
  init: InitResponse;
  menu: MenuTreeResponse;
}>;

export interface ThirdPartyAdUrls {
  desktop?: {
    frameUrl?: SafeResourceUrl;
    linkUrl?: SafeResourceUrl;
    imgUrl?: SafeResourceUrl;
  };
  mobile?: {
    frameUrl?: SafeResourceUrl;
    linkUrl?: SafeResourceUrl;
    imgUrl?: SafeResourceUrl;
  };
}
