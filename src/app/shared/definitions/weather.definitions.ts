export type WeatherData = Readonly<{
  text: WeatherDailyShort[];
  current: CityWeatherCurrent[];
  daily: Record<WeatherCity, WeatherDaily>;
  forecast: Record<WeatherCity, WeatherForecast[]>;
  uv: WeatherUv[];
}>;

export type WeatherDailyShort = Readonly<{
  day: string;
  text: string;
}>;

export type CityWeatherCurrent = Readonly<{
  city: string;
  country: string;
  icon: string;
  icon2: string;
  skyView: string;
  temperature: string;
  minTemperature: string;
  maxTemperature: string;
  rain: string;
  wind: string;
  windDirectionDegrees: string;
  windDirection: string;
  airPressure: string;
  humidity: string;
  sunrise: string;
  sunset: string;
}>;

export type WeatherDaily = Readonly<{
  city: string;
  time: string;
  icon: string;
  skyView: string;
  temperature: string;
  rain: string;
  wind: string;
}>;

export type WeatherCity =
  | 'Békéscsaba'
  | 'Budapest'
  | 'Debrecen'
  | 'Eger'
  | 'Győr'
  | 'Kaposvár'
  | 'Kecskemét'
  | 'Miskolc'
  | 'Nyíregyháza'
  | 'Pécs'
  | 'Salgótarján'
  | 'Szeged'
  | 'Székesfehérvár'
  | 'Szekszárd'
  | 'Szolnok'
  | 'Szombathely'
  | 'Tatabánya'
  | 'Veszprém'
  | 'Zalaegerszeg';

export type WeatherForecast = Readonly<{
  city: string;
  country: string;
  date: Date;
  day: string;
  icon: string;
  icon2: string;
  skyView: string;
  description: string;
  minTemperature: number;
  maxTemperature: number;
  rain: number;
  wind: number;
}>;

export type WeatherUv = Readonly<{
  city: string;
  index: string;
  text: string;
}>;

export type MapCityData = Readonly<{
  city: string;
  weather: CityWeatherCurrent;
  class: string;
}>;
