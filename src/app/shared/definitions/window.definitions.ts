import { GTMEcommerceEvent } from '@trendency/kesma-ui/lib/definitions/gtm-ecommerce-event';
import { SlaveConfig } from '@trendency/kesma-ui';

export type ExtendedWindow = Partial<IZoneFlags> &
  Partial<{
    twttr: {
      widgets: {
        load: () => void;
      };
    };
    instgrm: {
      Embeds: {
        process: () => void;
      };
    };
    FB: {
      init: (params: Record<string, unknown>) => void;
      XFBML: {
        parse: () => void;
      };
    };
    FlourishLoaded: boolean;
    Woo: unknown;
    ado: {
      slave: (slaveId: string, config?: SlaveConfig) => void;
      placement: (config: { server: string; id: string }) => void;
      master: (config: { id: string; server: string; keys?: string; vars?: string }) => void;
      refresh: (masterId: string) => void;
      [key: string]: ((...args: any[]) => void) | undefined;
    };
    adoQueueFn: (fn: string, args?: unknown[]) => void;
    adOceanInited: boolean;
    googletag: {
      destroySlots: () => void;
    };
    pp_gemius_identifier: string; // added in index.html via embedded js
    dataLayer: GTMEcommerceEvent[]; // added in index.html via embedded js
  }>;

declare global {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface,@typescript-eslint/naming-convention -- This is a declaration merging
  interface Window extends ExtendedWindow {}
}

export interface IZoneFlags {
  /** Disable the monkey patch of the on_property such as onclick, onerror, oncancel, etc. */
  __zone_symbol__BLACK_LISTED_EVENTS: string[];
  /** stack frames will have the Zone's name information, (By default, Error patch will not be loaded by zone.js) */
  __Zone_disable_Error: boolean;
  /** Function.toString will be patched to return native version of toString */
  __Zone_disable_toString: boolean;
  /** Promise.then will be patched as Zone aware MicroTask */
  __Zone_disable_ZoneAwarePromise: boolean;
  /** Bluebird will use Zone.scheduleMicroTask as async scheduler. (By default, bluebird patch will not be loaded by zone.js) */
  __Zone_disable_bluebird: boolean;
  /** target.onProp will become zone aware target.addEventListener(prop) */
  __Zone_disable_on_property: boolean;
  /** setTimeout/setInterval/setImmediate will be patched as Zone MacroTask */
  __Zone_disable_timers: boolean;
  /** requestAnimationFrame will be patched as Zone MacroTask */
  __Zone_disable_requestAnimationFrame: boolean;
  /** alert/prompt/confirm will be patched as Zone.run */
  __Zone_disable_blocking: boolean;
  /** target.addEventListener will be patched as Zone aware EventTask */
  __Zone_disable_EventTarget: boolean;
  /** MutationObserver will be patched as Zone aware operation */
  __Zone_disable_MutationObserver: boolean;
  /** Intersection will be patched as Zone aware operation */
  __Zone_disable_IntersectionObserver: boolean;
  /** FileReader will be patched as Zone aware operation */
  __Zone_disable_FileReader: boolean;
  /** HTMLCanvasElement.toBlob will be patched as Zone aware operation */
  __Zone_disable_canvas: boolean;
  /** in IE, browser tool will not use zone patched eventListener */
  __Zone_disable_IE_check: boolean;
  /** in webdriver, enable check event listener is cross context */
  __Zone_enable_cross_context_check: boolean;
  /** XMLHttpRequest will be patched as Zone aware MacroTask */
  __Zone_disable_XHR: boolean;
  /** navigator.geolocation's prototype will be patched as Zone.run */
  __Zone_disable_geolocation: boolean;
  /** PromiseRejectEvent will fire when ZoneAwarePromise has unhandled error */
  __Zone_disable_PromiseRejectionEvent: boolean;
  /** mediaQuery addListener API will be patched as Zone aware EventTask. (By default, mediaQuery patch will not be loaded by zone.js)  */
  __Zone_disable_mediaQuery: boolean;
  /** notification onProperties API will be patched as Zone aware EventTask. (By default, notification patch will not be loaded by zone.js)  */
  __Zone_disable_notification: boolean;
  /** MessagePort onProperties APIs will be patched as Zone aware EventTask. (By default, MessagePort patch will not be loaded by zone.js)  */
  __Zone_disable_MessagePort: boolean;
  /** Node.js patch timer */
  __Zone_disable_node_timers: boolean;
  /** Node.js patch fs function as macroTask */
  __Zone_disable_fs: boolean;
  /** Node.js patch EventEmitter as Zone aware EventTask */
  __Zone_disable_EventEmitter: boolean;
  /** Node.js patch process.nextTick as microTask */
  __Zone_disable_nextTick: boolean;
  /** Node.JS handle unhandledPromiseRejection from ZoneAwarePromise */
  __Zone_disable_handleUnhandledPromiseRejection: boolean;
  /** Node.js patch crypto function as macroTask */
  __Zone_disable_crypto: boolean;
  /** Jasmine APIs patch */
  __Zone_disable_jasmine: boolean;
  /** Mocha APIs patch */
  __Zone_disable_mocha: boolean;
}
