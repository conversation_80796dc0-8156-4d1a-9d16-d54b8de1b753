import { ArticleCard, Meta } from '@trendency/kesma-ui';

export type ProgramSelects = Readonly<{
  id?: string;
  title: string;
  slug: string;
}>;
export type ProgramTypes = Readonly<{
  id: string;
  isActive: string;
  isHighlighted: string;
  slug: string;
  title: string;
}>;

export type ProgramRecommendations = Readonly<{
  programRecommendations: ProgramRecommendationItem[];
}>;

export type ProgramRecommendationItem = Readonly<{
  address: string;
  endDate: string;
  id: string;
  isActive: string;
  lead: string;
  listedLocations: string[];
  locations: { slug: string; title: string }[];
  phoneNumber: string;
  types: { slug: string; title: string }[];
  tags: { slug: string; title: string }[];
  slug: string;
  startDate: string;
  title: string;
  categorySlug: string;
  image: string;
}>;

export type ProgramDetail = Readonly<{
  address: string;
  body: {
    details: {
      key: string;
      type: string;
      value: string;
    }[];
    id: string;
    subComponents: string[];
    type: string;
  }[];
  externalURL: string;
  dates: ProgramDate[];
  id: string;
  isActive: boolean;
  lead: string;
  listedLocations: string[];
  programLocations: ProgramSelects[];
  programTypes: ProgramSelects[];
  phoneNumber: string;
  tags: ProgramSelects[];
  slug: string;

  title: string;
  longitude: string;
  latitude: string;
}>;

export type ProgramDetailApiData = Readonly<{
  programDetail: ProgramDetail;
  recommendedArticles: ArticleCard;
}>;
export type ProgramDate = Readonly<{
  startDate: string;
  endDate: string;
}>;

export type ProgramQueryParams = Readonly<{
  global_filter?: string;
  'program_type[]'?: string[];
  date_from?: string;
  date_until?: string;
  rowCount_limit?: string;
  page_limit?: string;
}>;

export interface ProgramDetailResult {
  data: ProgramDetail;
  meta: Meta;
}
