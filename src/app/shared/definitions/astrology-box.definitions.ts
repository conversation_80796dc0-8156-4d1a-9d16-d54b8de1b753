import { AstrologyCard } from './astrology-card.definitions';

export type AstrologyBox = Readonly<{
  data: AstrologyCard[];
  slug: string;
}>;

export type BackendAstrologyResponse = Readonly<{
  jegy_linkek: BackendAstrologyInfo;
  jegy_szovegek: BackendAstrologyInfo;
}>;

export type BackendAstrologyInfo = Readonly<{
  bak: string;
  bika: string;
  halak: string;
  ikrek: string;
  kos: string;
  merleg: string;
  nyilas: string;
  oroszlan: string;
  rak: string;
  skorpio: string;
  szuz: string;
  vizonto: string;
}>;
