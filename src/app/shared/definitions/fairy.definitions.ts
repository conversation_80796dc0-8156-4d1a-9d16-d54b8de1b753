export interface FairyArchiveFilter extends Record<string, string | undefined> {
  /** Only finalists */
  is_finalists?: string;
  /** Portal name, e.g.: 'bama.hu' */
  country_slug?: string;
  year?: string;
  person_slug?: string;
}

export type BackendFairyArchiveData = Readonly<{
  /** = Tündérszépek */
  title: string;
  lead: string;
  fo_lany_data?: BackendArchiveMainFairyData;
  finalists: BackendFinalistData[];
  countries: BackendFairyCountry[];
}>;

export type BackendFairyCountry = Readonly<{
  /** Main county name if county filter was in use - otherwise empty */
  country: string;
  data: BackendFairySimpleData[];
}>;

export type BackendArchiveMainFairyData = Readonly<{
  name: string;
  slug: string;
  country: string;
  city: string;
  age: string;
  description: string;
  galeria: { src: string }[];
}>;

export type BackendFinalistData = BackendFairySimpleData & {
  zsuri_altal_valasztott?: '0' | '1';
};

export type BackendFairySimpleData = Readonly<{
  /** portal name -> "country_slug" parameter value */
  country: string;
  /** person-slug */
  slug: string;
  name: string;
  city: string;
  age: string;
  /** relative url to thumbnail */
  thumbnail_src: string;
}>;

export type FairyArchiveResult = Readonly<{
  countyName: string;
  data: FairySimpleData[];
}>;

export type FairyCounty = Readonly<{
  county: string;
  data: FairySimpleData[];
}>;

export type FairyArchiveData = Readonly<{
  title: string;
  lead: string;
  slug: string;
  countyName?: string;
  main_girl_data: ArchiveMainFairyData | undefined;
  finalists: FinalistData[];
  counties: FairyCounty[];
}>;

export type ArchiveMainFairyData = MainFairyInterface;

export type MainFairyInterface = Readonly<{
  portal?: string;
  name: string;
  slug: string;
  county?: string;
  city: string;
  age: number;
  /** html */
  description: string;
  vote_value?: number;
  zsuri_altal_valasztott?: boolean;
  galeria: FairyGalleryInterface[];
}>;

export type FairyPreviewInterface = FinalistData &
  Readonly<{
    vote_value?: number;
  }>;

export type FinalistData = Readonly<
  FairySimpleData & {
    zsuri_altal_valasztott?: boolean;
  }
>;

export type FairySimpleData = Readonly<{
  portal: string;
  slug: string;
  name: string;
  city?: string;
  county?: string;
  age: number;
  /** absolute url to thumbnail */
  thumbnail: string;
}>;

export type FairyEmbedResult = Readonly<{
  title: string;
  /** Person slug */
  href: string;
  /** Absolute thumbnail url */
  img: string;
  name: string;
}>;

export type BackendFairyIndexResult = Readonly<{
  /** API url */
  vote_county: string;
  vid: number;
  lead: string;
  title: string;
  main_girl_data: BackendMainFairyData;
  all_girl_data: BackendFairyPreviewData[];
  previus_cycles: FairyCycle[];
}>;

export type FairyIndexResult = Readonly<{
  /** API url */
  vote_county: string;
  vid: number;
  lead: string;
  title: string;
  main_girl_data: MainFairyData;
  all_girl_data: FairyPreviewData[];
  previous_cycles: FairyCycle[];
}>;

export type BackendFairyGalleryItem = Readonly<{
  id: string;
  lany_id: string;
  name: string;
  cimlapra_mehet_e: string;
  nagy_ajanlokep: string;
  kis_ajanlokep: string;
  sorrend: string;
  src: string;
  created_time: string;
  last_updated_time: string;
  video_embedd: string;
  thumbnail: string;
  thumbnail_src: string;
  facebook_kep: string;
  thumbnail2: string;
  thumbnail2_src: string;
}>;

export type FairyGalleryInterface = Readonly<{
  name: string;
  src: string;
  thumbnail_src: string;
  thumbnail2_src: string;
  facebook_kep: boolean;
  kis_ajanlokep: boolean;
  created_time?: Date;
}>;

export type FairyGalleryItem = FairyGalleryInterface &
  Readonly<{
    id: number;
    lany_id: number;
    cimlapra_mehet_e: boolean;
    nagy_ajanlokep: boolean;
    sorrend: number;
    created_time: Date;
    last_updated_time: Date;
    video_embedd: string;
    thumbnail: boolean;
    thumbnail2: boolean;
  }>;

export type BackendMainFairyData = Readonly<{
  slug: string;
  name: string;
  country: string;
  city: string;
  age: string;
  /** html */
  description: string;
  vote_value: string;
  galeria: BackendFairyGalleryItem[];
  zsuri_altal_valasztott: string;
}>;

export type MainFairyData = MainFairyInterface &
  Readonly<{
    galeria: FairyGalleryItem[];
  }>;

export type BackendFairyPreviewData = Readonly<{
  slug: string;
  name: string;
  country: string;
  city: string;
  age: string;
  vote_value: string;
  zsuri_altal_valasztott: string;
  kis_ajanlokep: BackendFairyGalleryItem;
}>;

export type FairyPreviewData = FinalistData &
  Readonly<{
    vote_value: number;
    kis_ajanlokep?: FairyGalleryItem;
  }>;

export type FairyCycle = Readonly<{
  slug: string;
  name: string;
}>;
