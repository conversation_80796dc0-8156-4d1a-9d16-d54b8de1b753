@use 'shared' as *;

:host {
  kesma-adult-overlay.initial {
    position: initial;
  }

  kesma-adult-overlay.adult-recommendation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;

    ::ng-deep {
      .overlay-container {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.gallery-card {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;
  width: 100%;
  padding: 0 20px 25px;
  cursor: pointer;

  @include media-breakpoint-down(md) {
    display: none;
    &.recommendation-style-2 {
      display: block;
    }
  }

  &.embedded {
    @include imgRatio(16%, 9%);
  }

  .gallery-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
  }

  .gallery-card-link {
    position: relative;
    z-index: 2;

    .gallery-card-title {
      color: $white;
      font-size: 30px;
      line-height: 125%;
      margin-bottom: 24px;
      font-weight: normal;
      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 26px;
      }
    }

    .small-title {
      font-size: 20px;
    }

    .big-title {
      font-size: 40px;
    }
  }

  .gallery-card-bottom {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: $white;
    flex-wrap: wrap;

    .gallery-card-credit {
      font-size: 20px;
      line-height: 30px;
      padding-right: 10px;
      @include media-breakpoint-down(sm) {
        width: 100%;
        margin-bottom: 12px;
      }
    }

    .small-credit {
      font-size: 16px;
    }

    .gallery-card-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 14px;
      line-height: 30px;
      flex-wrap: nowrap;
      justify-content: flex-end;

      span:first-child {
        margin-right: 30px;
        @include media-breakpoint-down(md) {
          margin-right: 15px;
        }
      }
    }
  }

  .card-bottom-small {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .gallery-open-icon {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 54px;
    height: 54px;
    background: rgba(0, 0, 0, 0.3);
    transition-duration: 0.3s;

    &:hover {
      transition-duration: 0.3s;
      background: rgba(0, 0, 0, 0.7);
    }

    &:after {
      content: ' ';
      display: inline-block;
      @include icon('icons/folder-image.svg');
      width: 24px;
      height: 24px;
      margin: 15px;
    }
  }

  &.recommendation-style-2 {
    padding: 20px;

    .gallery-card-link {
      .gallery-card-title {
        @include media-breakpoint-down(md) {
          font-size: 26px;
        }
      }
    }

    .gallery-card-bottom {
      .gallery-card-credit {
        @include media-breakpoint-down(sm) {
          width: auto;
          font-size: 16px;
          margin-bottom: 0;
        }
      }
    }
  }
}

.gallery-card-mobile {
  display: none;
  cursor: pointer;
  position: relative;

  @include media-breakpoint-down(md) {
    display: block;
  }

  &.mobile-big {
    width: 100%;

    .gallery-image {
      width: 100%;
      margin-bottom: 10px;

      img {
        object-fit: cover;
        width: 100%;
        aspect-ratio: 16/9;
      }
    }

    .gallery-card-bottom {
      padding: 0 20px;

      .gallery-card-title {
        font-weight: 500;
        font-size: 26px;
        line-height: 32px;
        color: $black;
        padding-bottom: 6px;
      }

      .gallery-card-row {
        display: flex;
        flex-direction: row;
      }

      .gallery-card-info {
        font-size: 14px;
        line-height: 21px;
        color: $grey-7;
        display: flex;
        flex-direction: row;
        margin-left: auto;

        span:first-of-type {
          margin-right: 5px;
        }
      }
    }
  }

  &.mobile-small {
    flex-direction: row;
    align-items: center;

    @include media-breakpoint-down(md) {
      display: flex;
    }

    .gallery-image {
      height: 100%;
      min-height: 120px;
      flex: 1 0 120px;
      max-width: 120px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center top;

      img {
        aspect-ratio: 1;
        object-fit: cover;
      }
    }

    .gallery-card-bottom {
      padding-left: 10px;

      .gallery-card-title {
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 1px;
        color: $black;
      }

      .gallery-card-info {
        font-size: 14px;
        line-height: 21px;
        color: $grey-7;
        display: flex;
        flex-direction: row;

        span:first-of-type {
          margin-right: 5px;
        }
      }
    }
  }
}

.gallery-header {
  margin-bottom: 30px;
  background-color: $grey-22;
  cursor: pointer;

  .gallery-header-block {
    padding: 10px 15px;
    min-height: 60px;
    display: flex;
    align-items: flex-start;
    position: relative;
    margin-bottom: 20px;
    flex-direction: column;
    @include article-before-line();

    &:before {
      background: $blue;
    }
  }

  .gallery-header-title {
    display: flex;
    align-items: center;
    position: relative;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    @include media-breakpoint-down(sm) {
      font-size: 14px;
      line-height: 18px;
    }
  }

  span {
    font-size: 12px;
    line-height: 16px;
    @include media-breakpoint-down(sm) {
      line-height: 14px;
    }
  }
}
