import { ChangeDetectionStrategy, Component, inject, Input, OnInit, signal, WritableSignal } from '@angular/core';
import { AdultOverlayComponent, GalleryCard, GalleryImage, GalleryRecommendationData, toBool } from '@trendency/kesma-ui';
import { ImageLazyLoadDirective, PublishDatePipe, StorageService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';
import { NgC<PERSON>, NgIf, NgStyle } from '@angular/common';
import { GalleryCardTypes } from '../../definitions';
import { DEFAULT_PUBLISH_DATE_FORMAT } from '../../constants';
import { PortalConfigService } from '../../services';

@Component({
  selector: 'app-gallery-card',
  templateUrl: './gallery-card.component.html',
  styleUrls: ['./gallery-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, NgStyle, AdultOverlayComponent, NgClass, RouterLink, PublishDatePipe, ImageLazyLoadDirective],
})
export class GalleryCardComponent implements OnInit {
  @Input() data: GalleryCard & {
    images?: Readonly<GalleryImage[]>;
    highlightedImage?: {
      url?: string;
    };
  };
  @Input() recommended: GalleryRecommendationData;
  @Input() mobileClass: string;
  @Input() isEmbedded = false;
  @Input() isSmallFont = false;
  @Input() isBigFont = false;
  @Input() isInsideAdultArticleBody = false;
  @Input() cardStyle: GalleryCardTypes = GalleryCardTypes.RECOMMENDATON_STYLE2;
  readonly galleryCardType = GalleryCardTypes;
  readonly toBool = toBool;

  portalConfig = inject(PortalConfigService);

  firstImageGallery: WritableSignal<string> = signal('');

  isUserAdultChoice: boolean;

  private readonly storage = inject(StorageService);

  ngOnInit(): void {
    const placholderUrl = `/assets/images/placeholders/${this.portalConfig.portalName.toLowerCase()}_placeholder.jpg`;
    this.firstImageGallery.set(`url(${this.data?.image || this.data?.highlightedImage?.url || this.data?.images?.[0]?.url?.thumbnail || placholderUrl})`);
    this.isUserAdultChoice = this.storage.getSessionStorageData('isAdultChoice') || false;
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
