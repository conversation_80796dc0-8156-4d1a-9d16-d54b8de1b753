<ng-container *ngIf="data">
  <div class="gallery-card" trImageLazyLoad [style.background-image]="firstImageGallery()" [class]="{ embedded: isEmbedded }">
    <kesma-adult-overlay class="initial" *ngIf="toBool(data?.isAdult) && !isInsideAdultArticleBody && !isUserAdultChoice"></kesma-adult-overlay>
    <ng-container *ngIf="!isEmbedded">
      <div class="gallery-card-overlay"></div>
      <a class="gallery-card-link">
        <h2 class="gallery-card-title" [ngClass]="{ 'small-title': isSmallFont, 'big-title': isBigFont }">
          {{ data.title }}
        </h2>
      </a>
      <div class="gallery-card-bottom" [ngClass]="{ 'card-bottom-small': isSmallFont }">
        <div class="gallery-card-credit" [ngClass]="{ 'small-credit': isSmallFont }">
          <ng-container *ngIf="data.photographer">
            Fotók: <span>{{ data.photographer }}</span>
          </ng-container>
        </div>
        <div class="gallery-card-info">
          <span *ngIf="data.imgCount" class="gallery-card-count">{{ data.imgCount }} fotó</span>
          <span class="gallery-card-date">{{ data.date | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
        </div>
      </div>
    </ng-container>
    <div *ngIf="isEmbedded" class="gallery-open-icon"></div>
  </div>
  <section class="gallery-header" *ngIf="isEmbedded">
    <div class="gallery-header-block">
      <h2 class="gallery-header-title">{{ data.title }}</h2>
      <span *ngIf="data.photographer">Fotók: {{ data.photographer }}</span>
    </div>
  </section>

  <div class="gallery-card-mobile mobile-{{ mobileClass }}" *ngIf="!isEmbedded">
    <div class="gallery-image">
      @if (toBool(data?.isAdult) && !isInsideAdultArticleBody && !isUserAdultChoice) {
        <kesma-adult-overlay>
          <img *ngIf="data?.image" [src]="data.image" loading="lazy" alt="" />
        </kesma-adult-overlay>
      } @else {
        <img *ngIf="data?.image" [src]="data.image" loading="lazy" alt="" />
      }
    </div>
    <div class="gallery-card-bottom">
      <a class="gallery-card-link">
        <h2 class="gallery-card-title">{{ data.title }}</h2>
      </a>
      <div class="gallery-card-row">
        <div class="gallery-card-credit">
          <ng-container *ngIf="data.photographer">
            Fotók: <span>{{ data.photographer }}</span>
          </ng-container>
        </div>
        <div class="gallery-card-info">
          <span *ngIf="data.imgCount" class="gallery-card-count">{{ data.imgCount }} fotó</span>
          <span class="gallery-card-date">{{ data.date | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
        </div>
      </div>
    </div>
  </div>
</ng-container>

<ng-container *ngIf="recommended">
  <a
    [routerLink]="['/', 'galeria', recommended?.slug, 1]"
    class="gallery-card"
    trImageLazyLoad
    [ngStyle]="{ 'background-image': recommended?.highlightedImageUrl ? 'url(' + recommended?.highlightedImageUrl + ')' : 'none' }"
    [ngClass]="{ 'recommendation-style-2': cardStyle === galleryCardType.RECOMMENDATON_STYLE2 }"
  >
    <kesma-adult-overlay
      class="adult-recommendation"
      *ngIf="!toBool(data?.isAdult) && !isInsideAdultArticleBody && !isUserAdultChoice && toBool(recommended?.isAdult)"
    >
    </kesma-adult-overlay>
    <div class="gallery-card-overlay"></div>
    <div class="gallery-card-link">
      <h2 class="gallery-card-title">{{ recommended?.title }}</h2>
    </div>
    <div class="gallery-card-bottom">
      <div class="gallery-card-credit">
        <ng-container *ngIf="recommended?.photographer">
          Fotók: <span>{{ recommended.photographer }}</span>
        </ng-container>
      </div>
      <div class="gallery-card-info">
        <span *ngIf="recommended?.count" class="gallery-card-count">{{ recommended?.count }} fotó</span>
        <span class="gallery-card-date">{{ recommended?.publicDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
      </div>
    </div>
  </a>
</ng-container>
