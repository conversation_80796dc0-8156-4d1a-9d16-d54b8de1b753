<section class="search">
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="header">
        <div *ngIf="title" class="title-container" [ngClass]="{ 'has-title': title }">
          <ng-content select="[slot=title]"></ng-content>
        </div>

        <div class="search-inputs-container">
          <ng-content select="[slot=search-inputs]"></ng-content>
        </div>
      </div>

      <ng-container *ngIf="!isLoading; then resultsTemplate; else loadingSignTemplate"></ng-container>

      <ng-template #resultsTemplate>
        <div class="result-count-container">
          <ng-content select="[slot=result-count]"></ng-content>
        </div>

        <div class="results">
          <app-article-card
            *ngFor="let article of results"
            [@fadeInAnimation]
            [styleID]="articleCardType.STYLE8"
            [article]="article"
            [isTagVisible]="isTagVisible"
            [isPreTitleVisible]="!isTagVisible"
          ></app-article-card>
        </div>

        <app-pager
          [currentPage]="page"
          [rowAllCount]="totalResultCount"
          [rowOnPageCount]="MAX_RESULTS_PER_PAGE"
          (pageChange)="onPageChange($event)"
        ></app-pager>
      </ng-template>

      <ng-template #loadingSignTemplate>
        <div class="loading-sign">betöltés...</div>
      </ng-template>
    </div>

    <aside>
      <ng-content select="[slot=sidebar]"></ng-content>
      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.box_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
    </aside>
  </div>
</section>
