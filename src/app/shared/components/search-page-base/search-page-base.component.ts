import { animate, style, transition, trigger } from '@angular/animations';
import { Ng<PERSON><PERSON>, NgF<PERSON>, NgIf } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, ArticleCard } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { MAX_RESULTS_PER_PAGE } from '../../constants';
import { ArticleCardTypes, List } from '../../definitions';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { PagerComponent } from '../pager/pager.component';

@Component({
  selector: 'app-search-page-base',
  templateUrl: './search-page-base.component.html',
  styleUrls: ['./search-page-base.component.scss'],
  animations: [trigger('fadeInAnimation', [transition(':enter', [style({ opacity: 0 }), animate('.300s ease-out', style({ opacity: 1 }))])])],
  imports: [NgIf, NgClass, NgFor, ArticleCardComponent, PagerComponent, AdvertisementAdoceanComponent],
})
export class SearchPageBaseComponent implements OnInit, OnDestroy {
  @Input() title: string;
  @Input() results: List<ArticleCard>;
  @Input() isLoading: boolean;
  @Input() totalResultCount: number;
  @Input() page: number;
  @Input() isTagVisible = false;

  @Output() readonly pageChange = new EventEmitter<number>();

  readonly MAX_RESULTS_PER_PAGE = MAX_RESULTS_PER_PAGE;
  readonly articleCardType = ArticleCardTypes;

  adverts: AdvertisementsByMedium;

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onPageChange(page: number): void {
    this.pageChange.emit(page);
  }

  private initAds(): void {
    this.adStoreAdo.advertisemenets$
      .pipe(
        map((ads) => this.adStoreAdo.separateAdsByMedium(ads)),
        takeUntil(this.destroy$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.changeRef.detectChanges();
      });
  }
}
