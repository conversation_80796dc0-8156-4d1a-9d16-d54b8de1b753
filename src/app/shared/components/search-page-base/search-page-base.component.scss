@use 'shared' as *;

:host {
  .left-column {
    padding-bottom: 50px;
  }

  .header {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-right: -15px;

    > * {
      margin-right: 15px;
      margin-bottom: 15px;
    }
  }

  .title-container {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    position: relative;
    padding: 20px 0;
    min-height: 70px;
    max-width: 100%;
    @include article-before-line();

    &.has-title {
      padding: 20px;
    }
  }

  .search-inputs-container {
    flex: 1 0 auto;
    display: flex;
    align-items: center;
    max-width: 100%;
  }

  .result-count-container {
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    text-align: center;
    margin-bottom: 20px;
  }

  app-pager {
    display: flex;
    justify-content: center;
  }

  .loading-sign {
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    text-align: center;
  }

  aside {
    margin-top: -30px;
  }
}
