import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { JobListingsService } from '../../services';
import { JobListingsComponent } from './components/job-listings/job-listings.component';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-job-listings-adapter',
  imports: [JobListingsComponent],
  templateUrl: './job-listings-adapter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class JobListingsAdapterComponent {
  protected readonly jobListingsService = inject(JobListingsService);
  jobListings = toSignal(this.jobListingsService.getJobListings());
}
