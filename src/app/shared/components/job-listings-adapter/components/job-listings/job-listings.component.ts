import { Component, inject, Input } from '@angular/core';
import { BlockTitleSidebarComponent } from '../../../block-title-sidebar/block-title-sidebar.component';
import { SlicePipe } from '@angular/common';
import { BlockTitle } from '@trendency/kesma-ui';
import { JobListing } from '../../../../definitions';
import { PortalConfigService } from '../../../../services';

@Component({
  selector: 'app-job-listings',
  imports: [BlockTitleSidebarComponent, SlicePipe],
  templateUrl: './job-listings.component.html',
  styleUrl: './job-listings.component.scss',
})
export class JobListingsComponent {
  @Input() data: JobListing[];
  private readonly portalConfigService = inject(PortalConfigService);
  readonly url = `${this.portalConfigService.siteUrl}/allas/mediaajanlat`;
  blockTitle: BlockTitle = { text: 'Álláshirdetések' };
}
