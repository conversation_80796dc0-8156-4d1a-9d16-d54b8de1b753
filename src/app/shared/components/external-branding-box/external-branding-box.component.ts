import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { AsyncPipe, NgFor, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { BrandingBoxArticle, BrandingBoxBrand } from '@trendency/kesma-ui';
import { NewsBrandingBoxComponent } from '../news-branding-box/news-branding-box.component';
import { BrandingBoxExService } from '../../services/branding-box-ex.service';

@Component({
  selector: 'app-external-branding-box',
  templateUrl: 'external-branding-box.component.html',
  styleUrls: ['external-branding-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, NgFor, AsyncPipe, SlicePipe, NewsBrandingBoxComponent],
})
export class ExternalBrandingBoxComponent implements OnInit {
  @Input() brand?: BrandingBoxBrand;
  @Input() isSidebar?: boolean;

  logo?: string;
  articles$?: Observable<BrandingBoxArticle[] | undefined>;
  brandingBoxService = inject(BrandingBoxExService);

  ngOnInit(): void {
    if (this.brand !== 'hirado') {
      this.articles$ = this.brandingBoxService.getBrandingBoxRequest$(this.brand);
    }
  }

  getCuttedLead(lead: string): string {
    // Gives back the first sentence of the string
    return lead.split(/(?<=[.!?])\s+/)[0];
  }
}
