@use 'shared' as *;

$szabadfoldColor: #008c44;
$backgroundColor: #f5f5f5;

:host {
  display: block;
  width: 100%;

  .box-container {
    border-top: 2px solid $szabadfoldColor;
    background-color: $backgroundColor;
  }

  .header {
    grid-area: header;
    display: flex;
    align-items: center;
    overflow: hidden;
    gap: 10px;

    @include media-breakpoint-down(md) {
      display: block;
    }

    &-link {
      display: flex;
      align-items: center;
      gap: 30px;

      @include media-breakpoint-down(md) {
        justify-content: center;
        flex-direction: column;
      }
    }

    &-logo {
      max-height: 85px;
    }

    &-slogen {
      max-width: 300px;

      @include media-breakpoint-down(md) {
        width: 245px;
      }
    }

    .header-bottom-divider {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      border: 0;
      height: 1px;
      width: 100%;
      background: black;
    }
  }

  .title {
    font-family: $font-family;

    &:hover {
      color: $szabadfoldColor;
    }
  }

  .big {
    grid-area: big;
  }

  .small1 {
    grid-area: small1;
  }

  .article-divider {
    border: 1px solid $szabadfoldColor;
    max-width: 170px;
    margin: 20px 0;
  }

  .main-article {
    .image-container {
      position: relative;
      width: 100%;
      aspect-ratio: 4 / 3;
      margin-bottom: 20px;
      backdrop-filter: invert(0.1); // This is used as a placeholder for the image.

      .image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .title {
      font-size: 35px;
      font-weight: 700;
    }

    .lead {
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 25px;
      margin-top: 20px;
      font-family: $font-family;
    }
  }

  .small-article {
    .image {
      width: 100%;
      object-fit: cover;
      aspect-ratio: 16 / 9;
      margin-bottom: 15px;
    }

    .title {
      font-weight: 700;
      font-size: 18px;
      line-height: 25px;
      font-family: $font-family;
    }

    .lead {
      margin-top: 20px;
      font-family: $font-family;
    }
  }

  @include media-breakpoint-down(md) {
    .box-container {
      grid-template-columns: 1fr;
      grid-template-rows: initial;
      grid-template-areas:
        'header'
        'big'
        'small1';
    }

    .small-article {
      .title {
        display: block;
        margin-bottom: 17px;
      }

      .title,
      .lead {
        font-size: 20px;
      }
    }
  }
}

.box-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: 100px 1fr;
  row-gap: 25px;
  column-gap: 15px;
  grid-auto-flow: row;
  grid-template-areas:
    'header header header'
    'big big small1';
  padding: 20px;
}

.sidebar-container {
  grid-template-columns: 1fr;
  grid-template-rows: initial;
  grid-template-areas:
    'header'
    'big'
    'small1';
  row-gap: 0;

  .header {
    margin-bottom: 25px;
  }
}
