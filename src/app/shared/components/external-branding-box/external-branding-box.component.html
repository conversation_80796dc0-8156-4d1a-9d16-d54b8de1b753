@if (brand === 'hirado') {
  <app-news-branding-box></app-news-branding-box>
} @else {
  <div class="box-container" [class.sidebar-container]="isSidebar" *ngIf="articles$ | async as articles">
    <div class="header">
      <a class="header-link" href="https://szabadfold.hu/" target="_blank">
        <img src="https://szabadfold.hu/assets/images/logo.svg" alt="Szabad Föld" class="header-logo" loading="lazy" />
      </a>
    </div>
    <div *ngIf="articles" class="big">
      <div class="main-article-container">
        <ng-container *ngTemplateOutlet="mainArticle; context: { $implicit: articles?.[0] }"></ng-container>
      </div>
    </div>
    <div *ngIf="articles" class="small1">
      <ng-container *ngTemplateOutlet="smallArticleList; context: { $implicit: articles | slice: 1 : 3 }"></ng-container>
    </div>
  </div>
}

<ng-template #mainArticle let-article>
  <div *ngIf="article" class="main-article">
    <div class="image-container">
      <a *ngIf="article.imageUrl" [href]="article.url" target="_blank">
        <img [src]="article.imageUrl" alt="" class="image" loading="lazy" />
      </a>
    </div>
    <a [href]="article.url" target="_blank">
      <h2 class="title">{{ article.title }}</h2>
    </a>
    <p *ngIf="article.lead" class="lead">{{ getCuttedLead(article.lead) }}</p>
  </div>
</ng-template>

<ng-template #smallArticle let-article>
  <div class="small-article" *ngIf="article">
    <a [href]="article.url" target="_blank" class="title">{{ article.title }}</a>
    <p *ngIf="article.lead" class="lead">{{ getCuttedLead(article.lead) }}</p>
  </div>
</ng-template>

<ng-template #smallArticleList let-articles>
  <ng-container *ngFor="let article of articles; let index = index">
    <hr class="article-divider" />
    <ng-container *ngTemplateOutlet="smallArticle; context: { $implicit: article }"></ng-container>
  </ng-container>
</ng-template>
