import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { isAfter } from 'date-fns';
import { throwError } from 'rxjs';
import { catchError, finalize, first } from 'rxjs/operators';
import { StorageService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { SectionHeaderComponent } from '../section-header/section-header.component';
import { AsyncPipe, NgClass, NgFor, NgIf, NgStyle, NgTemplateOutlet } from '@angular/common';
import { backendDateToDate, VoteData } from '@trendency/kesma-ui';
import { RecaptchaService, ViewModelService } from '../../services';
import { ArticleCardTypes } from '../../definitions';
import { VotingService } from 'src/app/feature/article/voting.service';

const VOTE_STORAGE_KEY = 'voted';
const TWO_MONTHS_IN_MS = 5_184_000_000;

interface ViewModel {
  id: string;
  hasVoted: boolean;
  isLoading: boolean;
  isSaveError: boolean;
  isVoteOver: boolean;
  myVote?: string;
  selectedVoteIndex?: number;
  voteData?: VoteData;
}

@Component({
  selector: 'app-voting-box',
  templateUrl: './voting-box.component.html',
  styleUrls: ['./voting-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ViewModelService],
  imports: [
    NgIf,
    SectionHeaderComponent,
    FormsModule,
    ReactiveFormsModule,
    NgFor,
    NgClass,
    NgTemplateOutlet,
    NgStyle,
    ArticleCardComponent,
    RouterLink,
    AsyncPipe,
  ],
})
export class VotingBoxComponent implements OnInit {
  static prevVotes: Record<string, { value: string; expiresAt: number }> = {};
  @Input() type: 'layout' | 'article' = 'layout';
  @Input() previousVotingsLink: string | string[] = ['/'];
  readonly articleCardType = ArticleCardTypes;
  votingForm: UntypedFormGroup;

  constructor(
    private readonly votingService: VotingService,
    private readonly storageService: StorageService,
    private readonly recaptchaService: RecaptchaService,
    public readonly viewModel: ViewModelService<ViewModel>
  ) {
    // Load previous votes from localStorage
    VotingBoxComponent.prevVotes = this.storageService.getLocalStorageData(VOTE_STORAGE_KEY) || {};
  }

  @Input()
  set voting(value: VoteData) {
    const endDate = typeof value?.endDate === 'string' ? backendDateToDate(value.endDate) : this.voting?.endDate;
    this.viewModel.next({ isVoteOver: !!endDate && isAfter(new Date(), endDate), voteData: value, id: value.id });
  }

  get voteId(): string {
    return this.viewModel.value.id || '';
  }

  get answersControl(): UntypedFormControl {
    return this.votingForm.controls['answers'] as UntypedFormControl;
  }

  ngOnInit(): void {
    this.votingForm = new UntypedFormGroup({
      answers: new UntypedFormControl('', [Validators.required]),
    });

    this.setUserVote(this.voteId);
    this.initVoteWithDelay(this.voteId).then();
  }

  delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async initVoteWithDelay(votingId: string): Promise<void> {
    this.viewModel.next({ isLoading: true });

    await this.delay(500); // Wait for the recaptcha to be ready

    this.recaptchaService.enqueue((token) => {
      this.votingService
        .getCurrentVotingStatus(votingId, token)
        .pipe(
          first(),
          catchError((error) => {
            this.viewModel.next({ isSaveError: true });
            console.error('Voting failed: ', error);
            return throwError(() => error);
          }),
          finalize(() => {
            this.viewModel.next({ isLoading: false });
          })
        )
        .subscribe((res) => this.viewModel.next({ voteData: res.data }));
    });
  }

  onVoteAnswerClick(index: number): void {
    this.viewModel.next({ selectedVoteIndex: index });
  }

  onVotingSubmit(): void {
    if (!this.votingForm.valid) {
      Object.values(this.votingForm.controls).forEach((control: AbstractControl) => {
        control.markAsTouched();
        control.updateValueAndValidity();
      });
      return;
    }

    this.viewModel.next({ isLoading: true });
    this.recaptchaService.enqueue((token) => this.registerPost(token));
  }

  registerPost(token: string): void {
    this.viewModel.next({
      myVote: this.votingForm.value.answers,
      isLoading: true,
    });
    this.votingService
      .vote(this.votingForm.value.answers, token)
      .pipe(
        first(),
        catchError((error) => {
          this.viewModel.next({ isSaveError: true });
          console.error('Voting failed: ', error);
          return throwError(() => error);
        }),
        finalize(() => {
          this.viewModel.next({ isLoading: false });
        })
      )
      .subscribe(({ data: { answers } }) => {
        this.viewModel.next({
          voteData: {
            ...(this.viewModel.value.voteData as VoteData),
            answers,
          },
        });

        // Remove expired records to avoid clutter
        Object.keys(VotingBoxComponent.prevVotes)
          .filter((key) => VotingBoxComponent.prevVotes[key].expiresAt < Date.now())
          .forEach((key) => delete VotingBoxComponent.prevVotes[key]);

        // Add new record
        VotingBoxComponent.prevVotes[this.voteId] = {
          value: this.votingForm?.value?.answers,
          expiresAt: Date.now() + TWO_MONTHS_IN_MS,
        };

        // Save to localStorage
        this.storageService.setLocalStorageData(VOTE_STORAGE_KEY, VotingBoxComponent.prevVotes);

        this.viewModel.next({ hasVoted: true });
      });
  }

  private setUserVote(voteId: string): void {
    if (VotingBoxComponent.prevVotes[voteId]) {
      this.viewModel.next({ hasVoted: true });
    }
  }
}
