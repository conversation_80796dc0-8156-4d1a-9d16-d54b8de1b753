@use 'shared' as *;

.voting-box {
  background-color: $grey-22;
  padding: 30px 40px;
  margin-bottom: 40px;

  .error {
    margin-bottom: 16px;
  }

  @include media-breakpoint-down(sm) {
    padding: 30px 0;
    margin: 0 -15px;

    app-article-card {
      ::ng-deep {
        .article-card.style-6 .article-block {
          margin-left: 0;
        }
      }
    }
  }

  .voting-box-title {
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }

  .radio-container {
    user-select: none;
    font-size: 16px;
    line-height: 24px;
    padding: 17px 25px 17px 80px;
    background-color: $white;
    border: 1px solid $grey-16;
    border-radius: 6px;
    position: relative;
    cursor: pointer;
    width: 100%;
    margin-bottom: 20px;

    &.active {
      color: $link-color;
      border-color: $link-color;
    }

    input {
      position: absolute;
      opacity: 0;
      height: 0;
      width: 0;

      &:checked ~ .checkmark {
        border-color: $secondary-color;
      }

      &:checked ~ .checkmark:after {
        display: block;
      }
    }

    .checkmark {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 19px;
      width: 30px;
      height: 30px;
      background-color: $white;
      border: 1px solid $grey-16;
      border-radius: 50%;

      &:after {
        content: '';
        position: absolute;
        display: none;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        background-color: $link-color;
        border-radius: 50%;
      }
    }
  }

  .voting-box-voted {
    background-color: $white;
    border: 1px solid $grey-16;
    border-radius: 6px;
    padding: 17px 25px 17px 32px;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 24px;
    color: $black;

    &.active {
      border-color: $active-color;
      color: $active-color;

      span {
        background-color: $secondary-color;
      }
    }
  }

  .voting-box-voted-bottom {
    display: flex;
    align-items: flex-end;
    margin-top: 10px;
  }

  .voting-box-voted-percentage {
    margin-left: auto;
    font-size: 14px;
    line-height: 1;
  }

  .voting-box-voted-line {
    background-color: $grey-16;
    border-radius: 6px;
    height: 6px;
    width: 80%;

    span {
      display: block;
      height: 6px;
      border-radius: 6px;
      background-color: $grey-22;
    }
  }

  .voting-box-submit {
    font-family: $font-family;
    display: flex;
    height: 50px;
    width: 100%;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 14px;
    color: $white;
    cursor: pointer;
    margin-bottom: 50px;
    position: relative;
    background-color: $link-color;
    border-radius: 6px;
    transition: 0.5s all;

    &.is-voting-success {
      background-color: $grey-16;

      &:before {
        @include icon('icons/white-check.svg');

        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: $grey-7;
        content: '';
        background-size: 9px 6px;
        margin-right: 5px;
      }

      &:hover {
        background-color: $grey-16;
        color: $white;
      }
    }

    &:hover {
      background-color: $white;
      color: $blue;
    }
  }

  .voting-box-more-wrapper {
    display: none;
    margin-top: 25px;
    @include media-breakpoint-down(sm) {
      display: flex;
      justify-content: flex-end;
    }
  }

  .voting-box-more {
    @include read-more-blue-arrow();
    display: inline-flex;
  }
}
