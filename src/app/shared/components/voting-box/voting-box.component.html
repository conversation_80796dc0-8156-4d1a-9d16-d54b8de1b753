<app-section-header *ngIf="type === 'article'" [link]="previousVotingsLink" linkTitle="Korá<PERSON>i s<PERSON>vazások" sectionTitle="Szavazás"></app-section-header>
<section *ngIf="viewModel.state$ | async as vm" class="voting-box">
  <div class="wrapper">
    <h2 class="voting-box-title">{{ vm.voteData?.question || vm.voteData?.title }}</h2>
    <!-- Ha még nem szavazott a user  -->
    <ng-container *ngIf="!vm.isVoteOver && !vm.hasVoted">
      <p *ngIf="!votingForm?.value?.answers && votingForm?.touched" class="error"><PERSON><PERSON><PERSON>j<PERSON><PERSON>, hogy válasszon a lehetőségek közül!</p>

      <form (submit)="onVotingSubmit()" [formGroup]="votingForm">
        <ng-container *ngIf="vm.voteData.answers && !vm.isVoteOver && !vm.hasVoted">
          <ng-container *ngFor="let item of vm.voteData.answers; let i = index">
            <label (click)="onVoteAnswerClick(i)" [ngClass]="{ active: i === vm.selectedVoteIndex }" class="radio-container">
              {{ item?.answer }}
              <input [formControl]="answersControl" [value]="item?.id" type="radio" />
              <span class="checkmark"></span>
            </label>
          </ng-container>
        </ng-container>
        <button [disabled]="vm.isLoading || vm.isSaveError" class="voting-box-submit" type="submit">
          <ng-container *ngIf="vm?.isLoading; else buttonText" [ngTemplateOutlet]="loading"></ng-container>
          <ng-template #buttonText>
            <ng-container *ngIf="!vm.isSaveError && !vm.hasVoted; else voteError">Szavazok!</ng-container>
          </ng-template>
          <ng-template #loading>Kérjük várjon</ng-template>
        </button>
      </form>
    </ng-container>

    <!-- Ha éppen szavazott, vagy már szavazott régebben akkor ezt kell megjeleníteni  -->
    <ng-container *ngIf="vm.isVoteOver || vm.hasVoted">
      <ng-container *ngFor="let item of vm.voteData?.answers ?? []">
        <div [ngClass]="{ active: vm.myVote === item.id }" class="voting-box-voted">
          {{ item.answer }}
          <div class="voting-box-voted-bottom">
            <div class="voting-box-voted-line">
              <span [ngStyle]="{ width: item.votePercentage + '%' }"></span>
            </div>
            <div class="voting-box-voted-percentage">{{ item.votePercentage || 0 }} %</div>
          </div>
        </div>
      </ng-container>

      <button *ngIf="vm?.hasVoted" class="voting-box-submit is-voting-success" disabled type="submit"><i class="voting-check"></i> Szavaztál</button>
    </ng-container>

    <app-article-card
      *ngIf="vm.voteData?.article"
      [article]="vm.voteData?.article"
      [isSubTitleVisible]="true"
      [isTagVisible]="false"
      [styleID]="articleCardType.STYLE6"
    ></app-article-card>

    <app-article-card
      *ngIf="(vm.isVoteOver || vm.hasVoted) && vm.voteData?.evaluationArticle?.title"
      [article]="vm.voteData?.evaluationArticle"
      [isSubTitleVisible]="true"
      [isTagVisible]="false"
      [styleID]="articleCardType.STYLE6"
    ></app-article-card>

    <div class="voting-box-more-wrapper">
      <a [routerLink]="previousVotingsLink" class="voting-box-more">
        Korábbi szavazások
        <svg fill="none" height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8H15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
          <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
        </svg>
      </a>
    </div>
  </div>
</section>

<ng-template #voteError>Sikertelen szavazás!</ng-template>
<ng-template #voteSuccess>Sikeres szavazás!</ng-template>
