import { ChangeDetectionStrategy, Component, Input, OnChanges, signal, SimpleChanges } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf, Ng<PERSON>tyle } from '@angular/common';
import { ImageLazyLoadDirective, PublishDatePipe } from '@trendency/kesma-core';
import { ArticleLinkPipe, DossierArticleShort, DossierData } from '@trendency/kesma-ui';
import { DEFAULT_PUBLISH_DATE_FORMAT } from '../../constants';

enum DossierBoxTypes {
  MainArticle = 1,
  MainArticleAbsoluteTitleDate = 2,
}

@Component({
  selector: 'app-dossier-card',
  templateUrl: './dossier-card.component.html',
  styleUrls: ['./dossier-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PublishDatePipe, ArticleLinkPipe, ImageLazyLoadDirective],
})
export class DossierCardComponent implements OnChanges {
  @Input() set dossiers(dossiers: DossierData[]) {
    this.data = dossiers;
    if (!this.data[0]) {
      return;
    }
    this.dossierArticles = [...(this.data[0]?.secondaryArticles ?? [])];
    this.dossier = this.data[0];
  }

  @Input() preferMainArticleThumbnail = false;
  @Input() styleID: number;
  @Input() widthDesktop: number;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.['dossiers']) {
      this.coverImage.set(
        this.preferMainArticleThumbnail && this.dossier?.mainArticle?.thumbnailUrl ? this.dossier.mainArticle.thumbnailUrl : (this.dossier?.headerImage ?? '')
      );
    }
  }

  data: DossierData[];
  dossierArticles?: DossierArticleShort[];
  dossier?: DossierData;
  coverImage = signal<string>('');

  readonly dossierBoxType = DossierBoxTypes;
  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
