<div class="dossier-card style-{{ styleID }}" *ngIf="dossier">
  <ng-container *ngIf="styleID === dossierBoxType.MainArticle">
    <a
      [routerLink]="['/', 'dosszie', dossier.slug]"
      class="dossier-block"
      [ngClass]="{ 'dossier-image': !!dossier.headerImage }"
      trImageLazyLoad
      [ngStyle]="{ 'background-image': coverImage() ? 'url(' + coverImage() + ')' : 'none' }"
    >
      <a *ngIf="dossier.tag" [routerLink]="['/', dossier.tag?.slug]" class="dossier-tag">
        {{ dossier.tag?.title }}
      </a>
    </a>
    <div class="dossier-bottom">
      <a class="dossier-link" [routerLink]="['/', 'dosszie', dossier.slug]">
        <div class="dossier-sub-title-wrapper">
          <span class="dossier-sub-title">{{ dossier.title }}</span>
          <span class="dossier-date">{{ dossier.mainArticle?.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
        </div>
      </a>
      <a class="main-article-link" [routerLink]="dossier.mainArticle | articleLink">
        <div class="main-article-wrapper">
          <h2 class="main-article-title">{{ dossier.mainArticle.title }}</h2>
        </div>
      </a>
    </div>
    <div class="dossier-list" *ngIf="dossierArticles?.length > 0">
      <ul class="rec-list">
        <ng-container *ngFor="let article of dossierArticles">
          <li *ngIf="article?.title">
            <a class="rec-link" [routerLink]="article | articleLink">{{ article.title }}</a>
          </li>
        </ng-container>
      </ul>
    </div>
  </ng-container>
  <ng-container *ngIf="styleID === dossierBoxType.MainArticleAbsoluteTitleDate">
    <div class="dossier-main">
      <a [routerLink]="['/', 'dosszie', dossier.slug]">
        <img class="dossier-img" [src]="data[0]?.mainArticle?.thumbnailUrl" alt="" />
      </a>
      <div class="dossier-main-data">
        <a class="dossier-title-block" [routerLink]="['/', 'dosszie', dossier.slug]">
          <p class="dossier-title"><i class="dossier-icon-gray"></i>{{ data[0].title }}</p>
        </a>
        <a class="dossier-main-article" [routerLink]="dossier.mainArticle | articleLink">
          <h1 class="dossier-main-article-title" [class.big-font-size]="widthDesktop >= 8">{{ data[0].mainArticle?.title }}</h1>
          <div class="dossier-main-article-date">{{ data[0].mainArticle?.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</div>
        </a>
      </div>
    </div>
    <div class="dossier-list" *ngIf="data[0]?.secondaryArticles">
      <ul class="rec-list">
        <div *ngFor="let article of data[0]?.secondaryArticles">
          <li *ngIf="article?.title">
            <a [routerLink]="article | articleLink">{{ article?.title }}</a>
          </li>
        </div>
      </ul>
      <a class="dossier-list-more" [routerLink]="['/', 'dosszie', dossier.slug]">
        <i class="dossier-icon-blue"></i>
        <p>
          További cikkek a <span class="dossier-list-more-title"> {{ data[0].title }}</span> dossziéban
        </p>
        <i class="right-arrow"></i>
      </a>
    </div>
  </ng-container>
</div>
