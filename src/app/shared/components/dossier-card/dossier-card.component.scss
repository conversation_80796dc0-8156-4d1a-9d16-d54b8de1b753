@use 'shared' as *;

.dossier-card {
  margin-bottom: 40px;

  &.style-1 {
    .dossier-image {
      // background-size: cover;
      // background-position: center top;
      // background-repeat: no-repeat;
      @include imgRatio(440%, 248%);
      position: relative;
      @include imgZoom();

      .dossier-tag {
        position: absolute;
        top: 20px;
        left: 20px;
        font-weight: 600;
        font-size: 12px;
        text-transform: uppercase;
        color: $grey-7;
        height: 30px;
        background-color: $white;
        display: inline-flex;
        align-items: center;
        border-radius: 6px;
        padding: 0 10px;
        font-family: $font-inter;

        &.black {
          color: $white;
          background-color: $black;
        }

        &.red {
          color: $white;
          background-color: $red;
        }

        &.blue {
          color: $white;
          background-color: $blue;
        }

        &.branded {
          color: $tag-text-color;
          background-color: $tag-color;
        }

        &.primary {
          color: $white;
          background-color: $primary-color;
        }

        &.secondary {
          color: $white;
          background-color: $secondary-color;
        }

        &.simple {
          background: transparent;
          padding: 0;
          text-transform: capitalize;
          font-size: 14px;
        }

        &.to-right {
          right: 20px;
          left: unset;
        }
      }
    }

    .dossier-bottom {
      .dossier-link,
      .main-article-link {
        .dossier-sub-title-wrapper {
          display: flex;
          margin-top: 11px;
          margin-bottom: 2px;

          .dossier-sub-title {
            font-weight: normal;
            font-size: 14px;
            line-height: 21px;
            color: $black;
            position: relative;
            padding-left: 24px;

            &:before {
              content: ' ';
              position: absolute;
              top: 3px;
              left: 0;
              width: 16px;
              height: 12px;
              @include icon('icons/dossier.svg');
            }
          }

          .dossier-date {
            margin-left: auto;
            font-size: 14px;
            color: $grey-7;
            text-align: right;
            min-width: 80px;
          }
        }

        .main-article-wrapper {
          display: flex;
          margin-bottom: 2px;
        }

        .main-article-title {
          font-style: normal;
          font-weight: 500;
          font-size: 20px;
          line-height: 30px;
          color: $black;
        }
      }
    }

    .dossier-list {
      .rec-list {
        padding: 0;
        margin: 10px 0;

        li {
          list-style-type: none;
          position: relative;
          margin: 5px 0;
          padding-left: 20px;

          &:before {
            position: absolute;
            width: 6px;
            height: 6px;
            top: 6px;
            left: 0;
            content: ' ';
            background-color: $blue;
            border-radius: 50%;
          }

          .rec-link {
            font-size: 14px;
            line-height: 18px;
            color: $black;
          }
        }
      }
    }
  }

  &.style-2 {
    .dossier {
      &-img {
        aspect-ratio: 16/9;
        object-fit: cover;
        width: 100%;

        @include media-breakpoint-down(sm) {
          width: 100vw;
          position: relative;
          left: 50%;
          right: 50%;
          margin-left: -50vw;
          margin-right: -50vw;
          max-width: none;
          aspect-ratio: 1/1;
        }
      }

      &-main {
        position: relative;

        &-data {
          position: absolute;
          bottom: 20px;
          width: 100%;
          padding: 0 40px;

          @include media-breakpoint-down(sm) {
            padding: 0 20px;
          }
        }
      }

      &-title-block {
        display: flex;
      }

      &-title {
        display: flex;
        align-items: center;
        background: $white;
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        padding: 8px 15px;
        border-radius: 6px;
        gap: 10px;
        margin-bottom: 10px;
      }

      &-icon-gray {
        @include icon('icons/dossier-gray.svg');
        height: 13.5px;
        width: 18px;
      }

      &-main-article {
        color: $white;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        &-title {
          color: $white;
          font-size: 20px;
          font-weight: 500;
          line-height: 26px;
        }

        &-date {
          display: flex;
          align-items: flex-end;
          font-size: 14px;
          font-weight: 400;
          line-height: 30px;
          text-align: right;
          white-space: nowrap;
        }
      }

      &-list {
        padding-left: 40px;

        @include media-breakpoint-down(sm) {
          padding-left: 0;
        }

        .rec-list {
          padding: 0;
          margin: 20px 0;
          font-size: 18px;
          line-height: 24px;
          font-weight: 500;

          @include media-breakpoint-down(sm) {
            font-size: 16px;
            line-height: 22px;
          }

          li {
            list-style-type: none;
            position: relative;
            margin: 10px 0;
            padding-left: 20px;

            &:before {
              position: absolute;
              width: 6px;
              height: 6px;
              top: 6px;
              left: 0;
              content: ' ';
              background-color: $black;
              border-radius: 50%;
            }

            .rec-link {
              font-size: 14px;
              line-height: 18px;
              color: $black;
            }
          }
        }

        &-more {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          line-height: 24.19px;
          color: $blue;
          gap: 10px;

          &-title {
            text-decoration: underline;
          }
        }

        .dossier-icon-blue {
          @include icon('icons/dossier-blue.svg');
          height: 14px;
          width: 14px;
        }

        .right-arrow {
          @include icon('icons/right-blue-arrow.svg');
          height: 14px;
          width: 14px;
        }
      }
    }

    .big-font-size {
      font-size: 40px;
      line-height: 50px;

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 26px;
      }
    }
  }
}
