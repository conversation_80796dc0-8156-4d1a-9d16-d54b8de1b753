import { Component, Input } from '@angular/core';
import { AdvertisementAdoceanComponent, AdvertisementsByMedium, ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { SectionHeaderComponent } from '../section-header/section-header.component';
import { NgFor, NgIf } from '@angular/common';
import { ArticleCardTypes, FairyCycle } from '../../definitions';

@Component({
  selector: 'app-galleries-bottom',
  templateUrl: './galleries-bottom.component.html',
  styleUrls: ['./galleries-bottom.component.scss'],
  imports: [NgIf, SectionHeaderComponent, NgFor, ArticleCardComponent, AdvertisementAdoceanComponent],
})
export class GalleriesBottomComponent {
  @Input() relatedArticles: ArticleCard[] = [];
  @Input() relatedArticlesSecondary: ArticleCard[] = [];
  @Input() adverts: AdvertisementsByMedium;
  @Input() ad: string;
  @Input() leftBlockTitle: string;
  readonly articleCardType = ArticleCardTypes;
  previousCycleCards: ArticleCard[] = [];

  @Input()
  set previousCycles(value: FairyCycle[]) {
    this.previousCycleCards = (value || []).map((item) => ({
      slug: item.slug,
      title: item.name,
      category: { category: 'Tündérszépek', slug: 'tunderszepek' },
      label: { text: 'Tündérszépek' },
      publishDate: undefined,
      publishYear: undefined,
      publishMonth: undefined,
    }));
  }
}
