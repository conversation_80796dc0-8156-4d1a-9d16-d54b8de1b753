@use 'shared' as *;

.galleries-bottom {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  @include media-breakpoint-down(md) {
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
  }

  .related-contents-column {
    width: calc(33.333% - 26px);
    margin: 0;
    max-width: 100%;
    @include media-breakpoint-down(md) {
      // width: 100%;
      margin: auto;
      width: 100vw;
    }

    app-section-header {
      .section-header {
        @include media-breakpoint-down(md) {
          margin-left: 0;
          margin-right: 0;
        }
      }
    }

    .article-card {
      &.style-6 {
        align-items: center;
      }

      &.style-3 {
        display: flex;
        flex-direction: row;
        height: 80px;
        align-items: center;
        @include media-breakpoint-down(md) {
          width: calc(100% - 40px);
          margin-left: 20px;
        }
      }
    }
  }
}

::ng-deep {
  .galleries-bottom {
    .article-card {
      &.style-3 {
        .article-block {
          @include media-breakpoint-down(md) {
            &:before {
              margin-left: 20px !important;
            }
          }
        }
      }
    }
  }
}
