<div class="galleries-bottom">
  <div class="related-contents-column" *ngIf="previousCycleCards?.length">
    <app-section-header sectionTitle="<PERSON>r<PERSON><PERSON><PERSON> fordulók"></app-section-header>
    <app-article-card [styleID]="articleCardType.STYLE6" *ngFor="let item of previousCycleCards" [article]="item"> </app-article-card>
  </div>
  <div class="related-contents-column">
    <app-section-header [sectionTitle]="leftBlockTitle ? leftBlockTitle : 'Kapcsolódó tartalmak'"></app-section-header>
    <app-article-card
      [styleID]="previousCycleCards?.length || leftBlockTitle ? articleCardType.STYLE3 : articleCardType.STYLE6"
      *ngFor="let article of relatedArticles"
      [article]="article"
    ></app-article-card>
  </div>
  <div class="related-contents-column" *ngIf="!previousCycleCards?.length">
    <app-section-header sectionTitle="Kapcs<PERSON>ó<PERSON><PERSON> tartalmak"></app-section-header>
    <app-article-card [styleID]="articleCardType.STYLE3" *ngFor="let article of relatedArticlesSecondary" [article]="article"></app-article-card>
  </div>
  <div class="related-contents-column">
    <kesma-advertisement-adocean *ngIf="adverts?.desktop?.[ad] as ad" [ad]="ad"> </kesma-advertisement-adocean>
    <kesma-advertisement-adocean *ngIf="adverts?.mobile?.[ad] as ad" [ad]="ad"> </kesma-advertisement-adocean>
  </div>
</div>
