import { Component, Input } from '@angular/core';
import { ArticleCard, buildArticleUrl } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-note-card',
  templateUrl: './note-card.component.html',
  styleUrls: ['./note-card.component.scss'],
  imports: [NgIf, RouterLink],
})
export class NoteCardComponent {
  @Input() data: ArticleCard;
  readonly buildArticleUrl = buildArticleUrl;
}
