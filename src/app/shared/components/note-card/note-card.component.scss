@use 'shared' as *;

.note-card {
  background: $primary-color;
  display: flex;
  text-align: center;
  flex-wrap: wrap;
  justify-content: center;
  min-height: 336px;
  height: 100%;

  .poser {
    padding: 40px;

    .tag {
      display: block;
      width: 100%;
      color: $white;
      font-style: normal;
      font-weight: 600;
      font-size: 12px;
      line-height: 21px;
      font-family: $font-inter;
      margin-bottom: 74px;
      text-transform: uppercase;
      position: relative;
      padding-top: 20px;

      &:before {
        content: ' ';
        display: block;
        position: absolute;
        width: 80px;
        height: 69px;
        top: 0;
        left: calc(50% - 40px);
        @include icon('icons/quote.svg');
      }
    }

    .name {
      display: block;
      width: 100%;
      color: $white;
      font-style: italic;
      font-weight: normal;
      font-size: 16px;
      line-height: 21px;
      font-family: $font-zilla;
      margin-bottom: 10px;
    }

    .title {
      display: block;
      width: 100%;
      color: $white;
      font-style: italic;
      font-weight: bold;
      font-size: 30px;
      line-height: 30px;
      font-family: $font-zilla;
      margin-bottom: 20px;
    }

    .lead {
      display: block;
      width: 100%;
      color: $white;
      font-weight: normal;
      font-size: 14px;
      line-height: 21px;
    }
  }
}
