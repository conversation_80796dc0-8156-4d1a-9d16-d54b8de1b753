import { Component, inject, Input } from '@angular/core';
import { isFuture, isPast } from 'date-fns';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';
import { FormatDatePipe } from '@trendency/kesma-core';
import { BackendProgramBlock } from '../../definitions';
import { PortalConfigService } from '../../services';

@Component({
  selector: 'app-program-block',
  templateUrl: './program-block.component.html',
  styleUrls: ['./program-block.component.scss'],
  imports: [NgIf, RouterLink, FormatDatePipe],
})
export class ProgramBlockComponent {
  @Input() set programs(data: BackendProgramBlock) {
    if (!data) {
      return;
    }

    this.data = {
      ...data,
      dates: this.getActiveOrClosestDate(data),
    };
  }

  private readonly portalConfig = inject(PortalConfigService);

  placeholder = `/assets/images/placeholders/${this.portalConfig.portalName.toLowerCase()}_placeholder.jpg`;
  data: BackendProgramBlock;

  getActiveOrClosestDate(data: BackendProgramBlock):
    | {
        id: string;
        end_date: string;
        start_date: string;
      }[]
    | undefined {
    if ((data?.dates?.length ?? 0) <= 1) {
      return data?.dates;
    }

    const activeDate = (data?.dates ?? []).filter(({ start_date, end_date }) => isPast(new Date(start_date)) && isFuture(new Date(end_date)));

    const soonestDate = (data?.dates ?? []).sort((a: any) => (new Date(a.start_date) > new Date() ? 1 : -1));

    return activeDate.length ? activeDate : soonestDate;
  }
}
