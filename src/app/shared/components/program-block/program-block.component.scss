@use 'shared' as *;

.program-card {
  background-color: $grey-22;
  display: flex;
  justify-content: flex-start;
  height: 160px;
  margin: 20px 0;
  position: relative;
  width: 100%;

  &:hover {
    color: black;
  }

  img {
    width: 120px;
    height: 160px;
    object-fit: cover;
  }

  .program-left {
    padding: 15px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .article-title {
      text-transform: uppercase;
      font-size: 16px;
      margin-bottom: 30px;
      font-weight: 400;
      line-height: 16px;
    }

    p {
      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
    }
  }
}
