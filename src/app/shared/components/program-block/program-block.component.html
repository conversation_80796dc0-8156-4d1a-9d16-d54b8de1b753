<a *ngIf="data !== null" [routerLink]="['/', 'program-ajanlo', data?.slug]" class="program-card">
  <img [src]="data?.thumbnail_url || placeholder" [alt]="data?.title" />
  <div class="program-left">
    <h3 class="article-title">{{ data?.title }}</h3>
    <div class="article-sub-title">
      <p *ngIf="data?.dates?.[0]?.start_date && data?.dates?.[0]?.end_date">
        {{ data?.dates?.[0].start_date | formatDate: 'hungarian' }} - {{ data?.dates?.[0].end_date | formatDate: 'hungarian' }}
      </p>
      <p>{{ data?.lead }}</p>
      <p>{{ data?.address }}</p>
    </div>
  </div>
</a>
