import { Component, inject, Input, OnInit } from '@angular/core';
import { PublishDatePipe, StorageService } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';
import { AdultOverlayComponent, GalleryCard } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { DEFAULT_PUBLISH_DATE_FORMAT } from '../../constants';

@Component({
  selector: 'app-gallery-block-card',
  templateUrl: './gallery-block-card.component.html',
  styleUrls: ['./gallery-block-card.component.scss'],
  imports: [RouterLink, AdultOverlayComponent, NgIf, PublishDatePipe],
})
export class GalleryBlockCardComponent implements OnInit {
  @Input() data: GalleryCard;

  isUserAdultChoice: boolean;

  private readonly storage = inject(StorageService);

  ngOnInit(): void {
    this.isUserAdultChoice = this.storage.getSessionStorageData('isAdultChoice') || false;
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
