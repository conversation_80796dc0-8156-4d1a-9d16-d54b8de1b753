<a class="gallery-card" [routerLink]="['/', 'galeria', data.slug, '1']">
  @if (data?.isAdult && !isUserAdultChoice) {
    <kesma-adult-overlay>
      <img *ngIf="data.image" [src]="data.image" loading="lazy" [alt]="data.title" />
    </kesma-adult-overlay>
  } @else {
    <img *ngIf="data.image" [src]="data.image" loading="lazy" [alt]="data.title" />
  }
  <div class="gallery-card-wrapper">
    <time class="gallery-card-date">{{ data.date | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</time>
    <h2 class="gallery-card-title">{{ data.title }}</h2>
  </div>
</a>
