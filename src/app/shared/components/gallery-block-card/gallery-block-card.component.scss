@use 'shared' as *;

:host {
  kesma-adult-overlay::ng-deep {
    position: initial;

    .overlay-container {
      width: 100%;
      height: 100%;
    }
  }
}

.gallery-card {
  height: 540px;
  min-width: 240px;
  position: relative;
  color: $white;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 30px;
  margin-bottom: 30px;
  cursor: pointer;

  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @include media-breakpoint-down(sm) {
    height: 300px;
    padding: 20px;
    margin-bottom: 15px;
  }

  &:before {
    content: '';
    position: absolute;
    background: linear-gradient(180deg, rgba($black, 0) 0%, rgba($black, 0.7) 100%);
    z-index: 1;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 283px;
  }

  .gallery-card-wrapper {
    position: relative;
    z-index: 2;
  }

  .gallery-card-date {
    font-size: 14px;
    margin-bottom: 20px;
    display: block;

    @include media-breakpoint-down(sm) {
      font-size: 12px;
      margin-bottom: 10px;
    }
  }

  .gallery-card-title {
    font-weight: 500;
    font-size: 30px;
    line-height: 40px;

    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 26px;
    }
  }
}
