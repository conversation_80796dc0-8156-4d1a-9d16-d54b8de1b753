import { DOCUMENT } from '@angular/common';
import { AfterViewInit, Component, inject, Input, OnInit } from '@angular/core';
import { PortalConfigService } from '../../services';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { Title } from '@angular/platform-browser';

const FBSDK_URL = 'https://connect.facebook.net/hu_HU/sdk.js';
const TWITTER_WIDGET_URL = 'https://platform.twitter.com/widgets.js';

@Component({
  selector: 'app-social-row',
  templateUrl: './social-row.component.html',
  styleUrls: ['./social-row.component.scss'],
})
export class SocialRowComponent implements OnInit, AfterViewInit {
  @Input() shareUrl?: string;
  @Input() shareTitle?: string;
  twitterShareUrl: string;
  private readonly document: Document = inject(DOCUMENT);

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly titleService: Title
  ) {
    if (!utilsService.isBrowser()) {
      return;
    }

    const fbLoaded = this.document.querySelector(`script[src^='${FBSDK_URL}']`);
    // load facebook sdk if required
    if (!fbLoaded) {
      // initialise facebook sdk after it loads if required
      if (!(window as any)['fbAsyncInit']) {
        (window as any)['fbAsyncInit'] = this.initFb;
      }

      const script = this.document.createElement('script');
      script.src = FBSDK_URL;
      this.document.body.appendChild(script);
    }
    if (fbLoaded) {
      this.initFb();
    }

    if (!this.document.querySelector(`script[src^='${TWITTER_WIDGET_URL}']`)) {
      const script = this.document.createElement('script');
      script.src = TWITTER_WIDGET_URL;
      this.document.body.appendChild(script);
    }
  }

  get portalName(): string {
    return this.portalConfig.portalName;
  }

  ngAfterViewInit(): void {
    // render facebook button
    setTimeout(() => {
      if (this.utilsService.isBrowser() && window['FB']) {
        window['FB'].XFBML?.parse();
      }
    }, 500);
  }

  ngOnInit(): void {
    if (!this.shareTitle) {
      this.shareTitle = this.titleService.getTitle();
    }
    if (!this.shareUrl) {
      this.shareUrl = this.seoService.currentUrl;
    }
    this.twitterShareUrl = this.buildTwitterShareUrl();
  }

  onFBShareClick(): void {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${this.shareUrl}`, 'pop', 'width=600, height=400, scrollbars=no');
  }

  private initFb(): void {
    (window as any)['FB'].init({
      appId: this.portalConfig.facebookAppId,
      autoLogAppEvents: true,
      xfbml: true,
      version: 'v12.0',
    });
  }

  private buildTwitterShareUrl(): string {
    const params = {
      original_referer: encodeURIComponent(this.seoService.hostUrl),
      ref_src: encodeURIComponent('twsrc^tfw|twcamp^buttonembed|twterm^share|twgr^'),
      text: encodeURIComponent(this.shareTitle ?? ''),
      url: encodeURIComponent(this.shareUrl ?? ''),
    } as const;

    const query = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return `https://twitter.com/intent/tweet?${query}`;
  }
}
