@use 'shared' as *;

.social-row {
  display: flex;
  flex-direction: row;

  .social-button {
    padding: 14px 19px 14px 60px;
    border-radius: 6px;
    margin-right: 15px;
    // margin-bottom: 15px;
    position: relative;
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;

    @include media-breakpoint-down(lg) {
      padding-left: 40px;
      padding-right: 10px;
    }

    &:not(.facebook-like) {
      @include media-breakpoint-down(md) {
        width: 50px;
        height: 50px;
        padding: 0;
        font-size: 0;
      }
    }

    &:before {
      width: 25px;
      height: 26px;
      content: ' ';
      display: block;
      position: absolute;
      top: calc(50% - 13px);
      left: 18px;
      @include media-breakpoint-down(lg) {
        left: 11px;
        width: 20px;
      }
      @include media-breakpoint-down(md) {
        left: calc(50% - 10px);
      }
    }

    &.facebook {
      color: $white;
      background: $socialcolor-facebook;

      &:before {
        @include icon('icons/facebook.svg');
      }
    }

    &.facebook-like {
      color: $white;
      background: $socialcolor-facebook;
      margin-right: 0;
      margin-left: auto;

      &:before {
        @include icon('icons/like.svg');
        top: calc(50% - 15px);
      }

      @include media-breakpoint-down(md) {
        &:before {
          left: 13px;
        }
      }
    }

    &.twitter {
      color: $white;
      background: $socialcolor-twitter;

      &:before {
        @include icon('icons/twitter.svg');
      }
    }

    &.email {
      color: $grey-7;
      background: $grey-22;
      border: 1px solid $grey-16;

      &:before {
        @include icon('icons/email.svg');
      }
    }
  }

  .fb-like {
    width: 85px;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    overflow: hidden;
    margin-left: 15px;
  }
}
