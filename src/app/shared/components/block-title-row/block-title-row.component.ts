import { Component, Input, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgClass, NgIf } from '@angular/common';
import { BaseComponent, BlockTitle } from '@trendency/kesma-ui';

@Component({
  selector: 'app-block-title-row',
  templateUrl: './block-title-row.component.html',
  styleUrls: ['./block-title-row.component.scss'],
  imports: [NgIf, NgClass, RouterLink],
})
export class BlockTitleRowComponent extends BaseComponent<BlockTitle> implements OnInit {
  @Input() isFullWidth = false;
  isExternal: boolean;

  override ngOnInit(): void {
    if (!this.data) {
      this.setData({
        text: 'Blokk cím',
      });
      return;
    }
    this.isExternal = this.data && !!this.data.url && (this.data.url.includes('http') || this.data.url.includes('www'));
  }
}
