<div class="block-title-row" [ngClass]="{ 'full-width': isFullWidth }" *ngIf="data">
  <h1 *ngIf="!data.url" class="block-title">
    <i *ngIf="data.visibleIcon" class="icon" [ngClass]="data.icon ? data.icon : ''"></i>
    <span>{{ data.text }}</span>
  </h1>
  <ng-container *ngIf="data && data.url">
    <a *ngIf="!isExternal" class="block-title link" [routerLink]="data.url">
      <i *ngIf="data.visibleIcon" class="icon" [ngClass]="data.icon ? data.icon : ''"></i>
      <span>{{ data.text }}</span>
      <span *ngIf="data?.urlName" class="link-text"
        >{{ data?.urlName }}
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8H15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </span>
    </a>
    <a *ngIf="isExternal" class="block-title link" [href]="data.url" target="_blank">
      <i *ngIf="data.visibleIcon" class="icon" [ngClass]="data.icon ? data.icon : ''"></i>
      <span>{{ data.text }}</span>
      <span *ngIf="data?.urlName" class="link-text"
        >{{ data?.urlName }}
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8H15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </span>
    </a>
  </ng-container>
</div>
