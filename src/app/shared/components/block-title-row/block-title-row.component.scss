@use 'shared' as *;

.block-title-row {
  background-color: $grey-22;
  margin-bottom: 30px;
  padding: 7px 5px;
  border-left: 5px solid $primary-color;

  &.full-width {
    .block-title {
      width: 100%;
    }
  }

  @include media-breakpoint-down(md) {
    width: calc(100% + 30px);
    margin: 0 0px 30px;
    margin-left: -15px;
  }

  .folder {
    @include icon('icon-folder-white.png');
    width: 16px;
    height: 13px;
  }

  .icon {
    margin-left: 10px;
  }

  .block-title {
    display: inline-flex;
    align-items: center;

    span {
      color: $black;
      font-size: 14px;
      font-weight: 500;
      line-height: 32px;
      text-align: left;
      text-transform: uppercase;
      display: inline-block;
      padding: 0 10px 0;

      @include media-breakpoint-down(md) {
        text-align: left;
        width: 100%;
      }
    }
  }

  .link {
    display: flex;
    justify-content: space-between;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }

    .link-text {
      text-transform: unset;
      color: $blue;
    }
  }

  svg {
    margin-left: 10px;
    margin-top: -3px;
  }
}
