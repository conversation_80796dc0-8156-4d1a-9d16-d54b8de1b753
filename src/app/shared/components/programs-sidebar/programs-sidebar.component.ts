import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { NgFor } from '@angular/common';
import { SectionHeaderComponent } from '../section-header/section-header.component';
import { ArticleCardTypes } from '../../definitions';
import { ArticleCard } from '@trendency/kesma-ui';

@Component({
  selector: 'app-programs-sidebar',
  templateUrl: './programs-sidebar.component.html',
  styleUrls: ['./programs-sidebar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SectionHeaderComponent, NgFor, ArticleCardComponent],
})
export class ProgramsSidebarComponent {
  readonly articleCardType = ArticleCardTypes;
  @Input() recommendedArticles: ArticleCard[];
}
