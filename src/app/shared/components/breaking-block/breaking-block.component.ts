import { Component, Input } from '@angular/core';
import { BreakingNews, buildArticleUrl } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';
import { PublishDatePipe } from '@trendency/kesma-core';
import { DEFAULT_PUBLISH_DATE_FORMAT } from '../../constants';

@Component({
  selector: 'app-breaking-block',
  templateUrl: './breaking-block.component.html',
  styleUrls: ['./breaking-block.component.scss'],
  imports: [NgIf, RouterLink, PublishDatePipe],
})
export class BreakingBlockComponent {
  @Input() data: BreakingNews;
  readonly buildArticleUrl = buildArticleUrl;
  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
