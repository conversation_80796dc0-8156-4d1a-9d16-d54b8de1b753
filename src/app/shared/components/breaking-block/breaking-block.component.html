<section *ngIf="data" class="article-card style-4">
  <div class="article-block">
    <div class="article-seperator first">
      <span class="article-tag red">Rend<PERSON><PERSON><PERSON><PERSON><PERSON></span>
    </div>
    <div class="article-seperator second">
      <a [routerLink]="buildArticleUrl(data)" class="article-link">
        <h2 class="article-title">{{ data.title }}</h2>
      </a>
    </div>
    <div class="article-seperator third">
      <span class="article-date">{{ data.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
    </div>
    <div class="article-seperator fourth">
      <a [routerLink]="buildArticleUrl(data)" class="article-link article-more">
        Tov<PERSON><PERSON><PERSON> r<PERSON>zletek
        <svg fill="none" height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8H15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
          <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
        </svg>
      </a>
    </div>
  </div>
</section>
