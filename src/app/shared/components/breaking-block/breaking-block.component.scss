@use 'shared' as *;

/* .breaking-block {
  margin-bottom: 30px;
  margin-left: -15px;

  @include media-breakpoint-down(sm) {
    margin: 0 -30px 30px;
  }
  .breaking-block-block {
    background-color: $black;
    min-height: 80px;
    padding: 20px 20px 20px 35px;
    display: flex;
    align-items: center;
    position: relative;
    flex-direction: row-reverse;
    @include article-before-line();
    width: calc(100% + 30px);

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
      width: calc(100% + 60px);
    }

    &:before {
      background-color: $red;
    }
  }
  .breaking-block-icon {
    @include icon('icons/alert-circle.svg');
    flex: 1 0 36px;
    height: 36px;
    width: 36px;
    margin-right: 23px;
    margin-bottom: -7px;
  }
  .breaking-block-title {
    font-weight: 600;
    font-size: 30px;
    line-height: 40px;
    color: $white;
    font-family: $font-inter;
    display: flex;
    align-items: center;
    margin-right: auto;

    a {
      text-decoration: none;
      color: $white;
    }

    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 30px;
    }
  }
  .breaking-block-tag {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: $grey-7;
    height: 30px;
    display: inline-flex;
    align-items: center;
    border-radius: 6px;
    padding: 0 10px;
    font-family: $font-inter;

    @include media-breakpoint-down(sm) {
      margin-bottom: 15px;
    }

    &.red {
      color: $white;
      background-color: $red;
    }
  }
}*/

.article-card {
  margin-bottom: 20px;
  position: relative;

  .article-block {
    min-height: inherit;
    display: flex;
    align-items: flex-end;
    justify-content: stretch;
  }

  .article-link {
    display: table;
    color: $white;
    @include media-breakpoint-down(sm) {
      width: 100%;
    }
  }

  .article-date {
    font-size: 14px;
    color: $grey-22;
    text-align: right;
    min-width: 80px;
  }

  .article-title {
    font-weight: 500;
    margin-top: 6px;
  }

  .article-tag {
    position: absolute;
    top: 20px;
    left: 20px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: $grey-7;
    height: 30px;
    background-color: $white;
    display: inline-flex;
    align-items: center;
    border-radius: 6px;
    padding: 0 10px;
    font-family: $font-inter;

    &.black {
      color: $white;
      background-color: $black;
    }

    &.red {
      color: $white;
      background-color: $red;
    }

    &.blue {
      color: $white;
      background-color: $blue;
    }

    &.branded {
      color: $tag-text-color;
      background-color: $tag-color;
    }

    &.primary {
      color: $white;
      background-color: $primary-color;
    }

    &.secondary {
      color: $white;
      background-color: $secondary-color;
    }

    &.simple {
      background: transparent;
      padding: 0;
      text-transform: capitalize;
      font-size: 14px;
    }

    &.to-right {
      right: 20px;
      left: unset;
    }
  }
}

.style-4 {
  background-color: $grey-22;

  .article-block {
    align-items: center;
    padding: 25px 33px;
    @include article-before-line();

    &:before {
      background-color: $red;
    }

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
      padding: 25px;
      margin: unset;
    }
  }

  .article-tag {
    position: static;
    @include media-breakpoint-down(sm) {
      margin-bottom: 10px;
    }
  }

  .article-title {
    color: $black;
    font-weight: 600;
    font-size: 20px;
    @include media-breakpoint-down(sm) {
      margin-bottom: 10px;
    }
  }

  .article-date {
    color: $grey-7;
    font-weight: 500;
  }

  .article-more {
    @include read-more-blue-arrow();
  }

  .article-seperator {
    &.first {
      margin-right: 50px;
      @include media-breakpoint-down(sm) {
        margin-right: 0;
      }
    }

    &.second {
      max-width: 550px;
      @include media-breakpoint-down(sm) {
        max-width: unset;
      }
    }

    &.third {
      min-width: 70px;
      margin-left: auto;
      padding-left: 10px;
      @include media-breakpoint-down(sm) {
        padding-left: 0;
        position: absolute;
        top: 30px;
        right: 27px;
      }
    }

    &.fourth {
      margin-left: 40px;
      @include media-breakpoint-down(sm) {
        margin-left: 0;
      }
    }
  }
}
