import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, Input, OnInit, QueryList, ViewChildren } from '@angular/core';
import { EmbeddingService, RunScriptsDirective, UtilService } from '@trendency/kesma-core';
import { NgFor, NgIf } from '@angular/common';
import { BypassPipe } from '@trendency/kesma-ui';

@Component({
  selector: 'app-wysiwyg-box',
  templateUrl: './wysiwyg-box.component.html',
  styleUrls: ['./wysiwyg-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, BypassPipe, RunScriptsDirective],
})
export class WysiwygBoxComponent implements OnInit, AfterViewInit {
  @Input() htmlArray: string[];
  @Input() html: string;
  @ViewChildren('wysiwygelement', { read: ElementRef }) wsElements: QueryList<ElementRef>;

  htmlBlocks: string[] = [];

  constructor(
    private readonly embeddingService: EmbeddingService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.htmlBlocks = (this.htmlArray?.length ? this.htmlArray : [this.html ?? ''])
      .map((htmlContent) => this.embeddingService.deferIframeLoad(htmlContent, { useNativeLazyLoading: true }))
      .map((s: any) => this.findVidea(s))
      .map((s: any) => this.findYoutube(s));
  }

  ngAfterViewInit(): void {
    this.wsElements.forEach((element: ElementRef<HTMLElement>) => {
      const oembedElements = element.nativeElement.getElementsByTagName('oembed');
      if (this.utilsService.isBrowser()) {
        if (oembedElements.length > 0) {
          this.embeddingService.createAnchorForEmbedly(Array.from(oembedElements));
        }
        this.removeEmptyCaptions(element);
      }
    });

    this.embeddingService.loadEmbedMedia(this.wsElements.map(({ nativeElement }) => nativeElement).filter((e) => !!e));
  }

  private removeEmptyCaptions(element: ElementRef<HTMLElement>): void {
    const figCaptions = element.nativeElement.getElementsByTagName('figcaption');
    if (figCaptions.length > 0) {
      Array.from(figCaptions).forEach((caption) => {
        if (caption.innerText === String.fromCharCode(160)) {
          caption.style.display = 'none';
        }
      });
    }
  }

  private findVidea(html: string): string {
    return html?.replace(
      /<iframe width=".{0,6}" height=".{0,6}" src="\/\/videa.hu/g,
      '<iframe style="aspect-ratio: 16/9; width: 100%; height: auto;" src="//videa.hu'
    );
  }
  findYoutube(html: string): string {
    return html?.replace(
      /<iframe width=".{0,6}" height=".{0,6}" src="https:\/\/www.youtube.com/g,
      '<iframe style="aspect-ratio: 16/9; width: 100%; height: auto;" src="https://www.youtube.com'
    );
  }
}
