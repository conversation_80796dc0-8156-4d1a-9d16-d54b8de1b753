@use 'shared' as *;

.clearfix {
  @include clearfix();
}

.block-content {
  margin-bottom: $block-bottom-margin;
  font-size: 20px;
  line-height: 40px;

  @include media-breakpoint-down(md) {
    margin-bottom: $block-bottom-margin-mobile;
  }
  // > * {
  @include media-breakpoint-down(sm) {
    margin-bottom: 32px;
  }
  // }
}

.block-content::ng-deep {
  > :first-child {
    margin-top: 0;
  }

  a {
    color: $blue;
    font-weight: bold;
    text-decoration: underline;
  }

  strong,
  b {
    font-weight: bold;
  }

  i,
  em {
    font-style: italic;
  }

  figure.image {
    display: table;
    clear: both;
    text-align: center;
    margin: 1em auto;
    margin-bottom: 48px;

    // @include media-breakpoint-down(sm) {
    //   width: 100vw;
    //   margin-left: -15px;
    // }

    &.image-style-align-left {
      float: left;
      margin-right: 20px;
      margin-bottom: unset;
      margin-top: 0;

      @include clearfix();
      @include media-breakpoint-down(sm) {
        margin-right: 10px;
      }
    }

    &.image-style-align-right {
      float: right;
      margin-left: 20px;
      margin-bottom: unset;
      margin-top: 0;

      @include clearfix();
      @include media-breakpoint-down(sm) {
        margin-left: 10px;
      }
    }

    figcaption {
      text-align: left;
      padding: 10px 15px;
      background-color: $grey-22;
      position: relative;
      line-height: 16px;
      font-size: 12px;

      p {
        margin: 0;

        &::first-line {
          display: block;
          font-size: 16px;

          margin: 200px;
          line-height: 22px;
        }
      }

      &::before {
        content: '';
        background: $blue;
        position: absolute;
        left: 0;
        top: 0;
        width: 5px;
        height: 100%;
      }
    }
  }

  ol,
  ul {
    li {
      padding-left: 15px;
      position: relative;
      margin-bottom: 20px;
    }
  }

  ol {
    list-style-type: decimal;
    list-style-position: outside;
    margin-left: 30px;

    li {
      padding-left: 0;
    }
  }

  ul {
    margin-left: 10px;

    li {
      &:before {
        width: 6px;
        height: 6px;
        background: $grey-7;
        position: absolute;
        left: 0;
        top: 18px;
        content: ' ';
        display: block;
        opacity: 0.5;
      }
    }
  }

  .table-scroller {
    width: 100%;
    overflow: auto;
  }

  table {
    max-width: 100%;
    border: 0;
    background: $grey-25;

    thead {
      tr {
        td,
        th {
          background: $grey-22;
          padding: 9px 16px;
          font-size: 12px;
        }

        th {
          text-transform: uppercase;
        }

        @include media-breakpoint-down(md) {
          padding: 5px 16px;
        }
      }
    }

    tr {
      td,
      th {
        border: 0;
        border-bottom: 2px solid $white;
        font-size: 16px;
        line-height: 30px;
        padding: 22px 16px;

        @include media-breakpoint-down(md) {
          padding: 10px 16px;
        }

        &:first-child {
          padding-left: 33px;
        }

        &:last-child {
          padding-right: 33px;
        }
      }

      th {
        font-weight: 500;
      }
    }
  }

  blockquote {
    margin: 50px 0;
    @include media-breakpoint-down(md) {
      margin: 30px 0;
    }
  }

  blockquote.quote {
    font-family: $font-zilla;
    font-size: 30px;
    font-style: italic;
    font-weight: 500;
    line-height: 50px;
    color: $blue;

    > :last-child::after {
      content: '\201D';
    }

    > :first-child::before {
      content: '\201E';
    }
  }

  blockquote.highlight {
    padding: 2px 10px;
    background: $blue;
    color: $white;
    font-family: $font-family;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 40px;
  }

  blockquote.border-text {
    padding: 28px 37px;
    background: $grey-22;
    border: 1px solid $grey-17;
    color: $blue;
    font-family: $font-family;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 36px;
    @include media-breakpoint-down(md) {
      padding: 17px 21px;
    }
  }

  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    display: block;
    margin: 50px 0;

    @include media-breakpoint-down(md) {
      margin: 30px 0;
    }
  }

  .custom-text-style {
    display: block;
    clear: both;

    &.quote {
      > :first-child::before {
        content: '\201E';
      }

      > :last-child::after {
        content: '\201D';
      }

      margin: 35px 0;
      font-family: $font-zilla;
      font-size: 30px;
      font-style: italic;
      font-weight: 500;
      color: $blue;

      p:first-of-type {
        display: inline;
      }

      p:last-of-type {
        display: inline;
      }

      li:before {
        background: $blue;
      }
    }

    &.highlight {
      display: block !important;
      padding: 2px 10px;
      background: $blue;
      -webkit-box-decoration-break: clone;
      box-decoration-break: clone;
      display: inline;
      color: $white;
      font-family: $font-family;
      text-align: left;
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 37px;

      ul li::before {
        background: $white;
      }

      a {
        color: $white;
      }
    }

    &.border-text {
      padding: 15px 25px;
      display: block;
      background: $grey-22;
      border: 1px solid $grey-17;
      color: $blue;
      font-family: $font-family;
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      @include media-breakpoint-down(md) {
        padding: 17px 21px;
      }
    }
  }

  span {
    &.highlight-block,
    &.quote-block,
    &.border-text-block {
      display: block;
      margin: 50px 0;

      p {
        margin: 0;
      }

      @include media-breakpoint-down(md) {
        margin: 30px 0;
      }
    }
  }

  p {
    span {
      display: unset;
      margin: unset;

      @include media-breakpoint-down(md) {
        margin: unset;
      }
    }
  }

  // !injektált JS scriptekkel működő aloldalak használják!
  .puzzle {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 100%;
    flex: 0 1 100%;
    @include media-breakpoint-down(md) {
      overflow: hidden;
      overflow-x: scroll;
      align-items: flex-start;
    }
  }

  .raw-html-embed {
    width: 100%;
    text-align: center;

    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
    display: block;

    > * {
      margin: 0 auto;
    }

    iframe {
      max-width: 100%;
    }
  }
}
