import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-popup',
  template: '',
  styleUrls: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
})
export abstract class PopupComponent {
  isVisible$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  private readonly document: Document = inject(DOCUMENT);

  open(): void {
    this.isVisible$.next(true);
    this.disableBodyScroll();
  }

  close(): void {
    this.isVisible$.next(false);
    this.enableBodyScroll();
  }

  disableBodyScroll(): void {
    if (!this.document.body.classList.contains('modal-open')) {
      this.document.documentElement.classList.add('modal-open');
      this.document.body.classList.add('modal-open');
    }
  }

  enableBodyScroll(): void {
    if (this.document.body.classList.contains('modal-open')) {
      this.document.documentElement.classList.remove('modal-open');
      this.document.body.classList.remove('modal-open');
    }
  }
}
