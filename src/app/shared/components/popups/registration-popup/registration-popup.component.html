@if (isVisible$ | async) {
  <div class="modal">
    <div class="modal-wrapper auth-popup registration-popup">
      <button (click)="close()" class="close-popup-button"></button>
      <h3 class="popup-title">Regisztráció</h3>
      <p class="popup-lead"><PERSON>z<PERSON> létre a profilod, hogy k<PERSON>yelmesebb legyen a cikkek olvasása</p>
      <div class="form-block">
        <form>
          <div class="input-wrapper" [ngClass]="{ error: false }">
            <div class="input-header">
              <label for="vezeteknev">Vezetéknév</label>
              <div class="error">Hibás vezetéknév</div>
            </div>
            <input id="vezeteknev" type="text" placeholder="Add meg az e-mail címed" />
            <div class="error-mobile">Hibás vezetéknév</div>
          </div>

          <div class="input-wrapper" [ngClass]="{ error: false }">
            <div class="input-header">
              <label for="keresztnev">Keresztnév</label>
              <div class="error">Hibás keresztnév</div>
            </div>
            <input id="keresztnev" type="text" placeholder="Add meg az e-mail címed" />
            <div class="error-mobile">Hibás keresztnév</div>
          </div>

          <div class="input-wrapper" [ngClass]="{ error: false }">
            <div class="input-header">
              <label for="email">E-mail cím</label>
              <div class="error">Hibás email cím</div>
            </div>
            <input id="email" type="text" placeholder="Add meg az e-mail címed" />
            <div class="error-mobile">Hibás email cím</div>
          </div>
          <div class="input-wrapper" [ngClass]="{ error: false }">
            <div class="input-header">
              <label for="input1">Jelszó</label>
              <div class="error">Nem biztonságos jelszó</div>
            </div>
            <input id="input1" type="password" placeholder="" />
            <div class="error-mobile">Nem biztonságos jelszó</div>
          </div>

          <div class="form-block-bottom">
            <div class="left">
              <div class="custom-input">
                <input type="checkbox" id="input6" />
                <label for="input6">Megismerem és elfogadom a bama.hu internetes honlap felhasználási feltételeit.</label>
              </div>
            </div>
          </div>

          <button class="button">Regisztráció</button>

          <div class="divider"></div>
          <div class="text-wrapper text-wrapper-b">
            <p>Már van profilod?</p>
            <a href="/" class="link">Vissza a belépéshez</a>
          </div>
        </form>
      </div>
    </div>
    <div class="modal-backdrop" (click)="close()"></div>
  </div>
}
