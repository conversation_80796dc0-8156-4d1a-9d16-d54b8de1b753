import { Component } from '@angular/core';
import { Async<PERSON>ip<PERSON>, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PopupComponent } from '../popup.component';

@Component({
  selector: 'app-login-popup',
  templateUrl: './login-popup.component.html',
  styleUrls: ['./login-popup.component.scss'],
  imports: [FormsModule, NgClass, AsyncPipe],
})
export class LoginPopupComponent extends PopupComponent {}
