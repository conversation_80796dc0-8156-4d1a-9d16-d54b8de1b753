@if (isVisible$ | async) {
  <div class="modal">
    <div class="modal-wrapper auth-popup login-popup">
      <button (click)="close()" class="close-popup-button"></button>
      <h3 class="popup-title">Belépés a profilodba</h3>
      <p class="popup-lead">Lépj be a profilodba, hogy k<PERSON>bb legyen a cikkek olvasása</p>
      <div class="form-block">
        <form>
          <div class="input-wrapper" [ngClass]="{ error: false }">
            <div class="input-header">
              <label for="input2">E-mail cím</label>
              <div class="error">Hibás email cím</div>
            </div>
            <input id="input2" type="text" placeholder="Add meg az e-mail címed" />
            <div class="error-mobile">Hibás email cím</div>
          </div>
          <div class="input-wrapper" [ngClass]="{ error: false }">
            <div class="input-header">
              <label for="input1"><PERSON><PERSON><PERSON><PERSON></label>
              <div class="error">Nem biz<PERSON> j<PERSON></div>
            </div>
            <input id="input1" type="password" placeholder="" />
            <div class="error-mobile">Nem biztonságos jelszó</div>
          </div>
          <button class="button">Belépés</button>
          <div class="text-wrapper">
            <a href="/" class="link">Elfelejtetted a jelszavad?</a>
          </div>
          <div class="divider"></div>
          <div class="text-wrapper text-wrapper-b">
            <p>Még nincs profilod?</p>
            <a href="/" class="link">Regisztrálj most!</a>
          </div>
        </form>
      </div>
    </div>
    <div class="modal-backdrop" (click)="close()"></div>
  </div>
}
