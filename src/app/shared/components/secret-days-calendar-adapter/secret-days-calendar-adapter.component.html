@if (calendar()) {
  <kesma-secret-days-calendar [data]="calendar()" (modalCloseEvent)="closeModal()" (dayClickEvent)="openDayModal($event)">
    @if (openedDayData(); as day) {
      <div class="secret-day-data" [style.background-image]="'url(' + day?.backgroundImageUrl + ')'">
        @if (day?.sponsorshipHeader?.title && day?.isSponsorHeaderActive) {
          <a [href]="day?.sponsorshipHeader?.url" class="day-sponsorship">
            <img [src]="day?.sponsorshipHeader?.logo?.thumbnailUrl" alt="{{ day?.sponsorshipHeader?.title }}" class="day-sponsorship-logo" />
          </a>
        }

        @for (wysiwygDetail of day?.body?.[0].details; track i; let i = $index) {
          <app-wysiwyg-box [html]="wysiwygDetail?.value"></app-wysiwyg-box>
        }

        @if (day?.sponsorshipFooter?.title && day?.isSponsorFooterActive) {
          <a [href]="day?.sponsorshipFooter?.url" class="day-sponsorship">
            <img [src]="day?.sponsorshipFooter?.logo?.thumbnailUrl" alt="{{ day?.sponsorshipFooter?.title }}" class="day-sponsorship-logo" />
          </a>
        }
      </div>
    }
    @if (isDayLocked()) {
      <div class="calendar-day-notification">
        <h3 class="calendar-day-notification-title">A kiválasztott nap jelenleg nem elérhető</h3>
        <p class="calendar-day-notification-description">
          A kiválasztott nap tartalma jelenleg nem megtekinthető. Lehet még nem nyílt meg vagy már lezárult az elérhetősége. Kérjük nézz vissza más időpontban
          vagy válassz egy másik napot a kalendáriumban!
        </p>
      </div>
    }
  </kesma-secret-days-calendar>
}
