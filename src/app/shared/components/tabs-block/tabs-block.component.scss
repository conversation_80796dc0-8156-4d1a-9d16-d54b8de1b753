@use 'shared' as *;

.tabs-block-navigation {
  // padding: 0 15px;
  .tab-navigator {
    display: flex;
    background-color: $white;
    color: $black;
    @include media-breakpoint-down(sm) {
      margin: 0 -15px;
    }
  }

  .tabs-block-navigation-wrapper {
    display: flex;
    align-items: center;
  }

  .tab-navigator-item {
    cursor: pointer;
  }

  .tab-navigator-button {
    font-family: $font-family;
    font-weight: 500;
    font-size: 20px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    transition: 0.5s all;

    &:hover {
      color: $blue;
    }

    &.is-active {
      background-color: $grey-22;
    }
  }

  .tab-navigator-more {
    align-items: center;
    display: none;
    margin-left: auto;
    @include media-breakpoint-down(sm) {
      display: flex;
    }

    span {
      display: block;
      height: 4px;
      width: 4px;
      background-color: $black;
      margin: 0 2.5px;
    }
  }
}

.tabs-block-body {
  position: relative;

  &:after {
    width: 100vw;
    background-color: $grey-22;
    right: 0;
    height: 100%;
    top: 0;
    display: block;
    content: ' ';
    position: absolute;
    z-index: -1;
    height: 100%;
  }

  &:before {
    width: 100vw;
    background-color: $grey-22;
    left: 0;
    height: 100%;
    top: 0;
    display: block;
    content: ' ';
    position: absolute;
    z-index: -1;
    height: 100%;
  }

  background-color: $grey-22;
  padding: 30px 0px;

  color: $black;

  .tabs-block-link {
    display: table;
    color: $black;

    &.title-wrapper {
      @include media-breakpoint-down(sm) {
        width: 100%;
        margin: 10px 0 0 50px;
      }
    }
  }

  .tabs-block-body-bottom {
    margin-top: 20px;
  }

  .tabs-block-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    @include media-breakpoint-down(sm) {
      flex-wrap: wrap;
    }
  }

  .tabs-block-color {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
  }

  .tabs-block-city {
    font-weight: 600;
    font-size: 30px;
    margin-right: 24px;
  }

  .tabs-block-title {
    font-size: 20px;
    font-weight: 400;
  }

  .tabs-block-more {
    font-weight: 500;
    font-size: 16px;
    color: var(--link-color);
    margin: 0 0 0 auto;
    display: flex;
    justify-content: flex-end;
    @include media-breakpoint-down(sm) {
      display: none;
    }

    svg {
      margin-left: 14px;
    }

    &.with-arrow {
      display: none;
      margin: 20px 0 0 0;
      @include read-more-blue-arrow();
      @include media-breakpoint-down(sm) {
        display: inline-flex;
      }
    }
  }

  .tabs-block-more-wrapper {
    display: flex;
    justify-content: flex-end;

    @include media-breakpoint-down(sm) {
      &:after {
        width: 100vw;
        background-color: $grey-22;
        right: 0;
        height: 100%;
        top: 0;
        display: block;
        content: ' ';
        position: absolute;
        z-index: -1;
      }
    }
  }
}

.local-wrapper {
  width: 100%;
}

.tab-wrapper {
  width: 100%;
}
