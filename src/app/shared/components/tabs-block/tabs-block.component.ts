import { Component, Input, OnInit } from '@angular/core';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { RouterLink } from '@angular/router';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf, Ng<PERSON>tyle } from '@angular/common';
import { ArticleCardTypes, TabsBlock } from '../../definitions';
import { PortalConfigService } from '../../services';

@Component({
  selector: 'app-tabs-block',
  templateUrl: './tabs-block.component.html',
  styleUrls: ['./tabs-block.component.scss'],
  imports: [NgFor, Ng<PERSON>lass, NgIf, NgStyle, RouterLink, ArticleCardComponent],
})
export class TabsBlockComponent implements OnInit {
  tabs: TabsBlock[] = [];
  readonly articleCardType = ArticleCardTypes;
  activeTab: TabsBlock;
  primaryColor: string;

  constructor(private readonly portalConfig: PortalConfigService) {}

  @Input() set data(tabs: TabsBlock[]) {
    this.tabs = tabs;
    if (!this.tabs[0]) {
      return;
    }
    this.activeTab = this.tabs[0];
  }

  ngOnInit(): void {
    this.primaryColor = this.portalConfig?.primaryColor;
  }

  onTabChange(tab: TabsBlock): void {
    this.activeTab = tab;
  }
}
