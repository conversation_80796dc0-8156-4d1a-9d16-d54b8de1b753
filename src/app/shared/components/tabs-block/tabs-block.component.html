<section class="tabs-block-navigation">
  <div class="tab-wrapper">
    <div class="tabs-block-navigation-wrapper">
      <ul class="tab-navigator">
        <li class="tab-navigator-item" *ngFor="let tab of tabs" (click)="onTabChange(tab)">
          <div
            class="tab-navigator-button"
            [ngClass]="{
              'is-active': activeTab?.id === tab?.id,
            }"
          >
            {{ tab?.region }}
          </div>
        </li>
      </ul>
      <button class="tab-navigator-more" *ngIf="tabs?.length > 0">
        <span></span>
        <span></span>
        <span></span>
      </button>
    </div>
  </div>
</section>
<section class="tabs-block-body">
  <div class="local-wrapper">
    <div class="tabs-block-header">
      <div class="tabs-block-color" [ngStyle]="{ 'background-color': primaryColor }"></div>
      <h2 class="tabs-block-city">{{ activeTab?.region }}</h2>
      <!--<a href="" class="tabs-block-link title-wrapper">
        <h3 class="tabs-block-title">A mediterrán hangulatok városa</h3>
      </a>-->
      <a [routerLink]="['/', 'regio', activeTab?.regionSlug]" class="tabs-block-more">
        További hírek ebben a témában
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8H15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </a>
    </div>
    <div class="row" *ngIf="activeTab?.topArticles?.length">
      <div class="col-md-8">
        <app-article-card *ngIf="activeTab?.topArticles[0]" [styleID]="articleCardType.STYLE1" [isTagVisible]="true" [article]="activeTab?.topArticles[0]">
        </app-article-card>
      </div>
      <div class="col-md-4">
        <div class="row">
          <div class="col-12">
            <app-article-card *ngIf="activeTab?.topArticles[1]" [styleID]="articleCardType.STYLE2" [isTagVisible]="true" [article]="activeTab?.topArticles[1]">
            </app-article-card>
          </div>
          <div class="col-12">
            <app-article-card *ngIf="activeTab?.topArticles[2]" [styleID]="articleCardType.STYLE2" [isTagVisible]="true" [article]="activeTab?.topArticles[2]">
            </app-article-card>
          </div>
        </div>
      </div>
    </div>
    <div class="tabs-block-body-bottom">
      <div class="row" *ngIf="activeTab?.bottomArticles?.length">
        <div class="col-md-3" *ngFor="let bottomArticle of activeTab?.bottomArticles">
          <app-article-card
            *ngIf="bottomArticle"
            [styleID]="articleCardType.STYLE6"
            [isTagVisible]="false"
            [isSubTitleVisible]="true"
            [article]="bottomArticle"
          >
          </app-article-card>
        </div>
      </div>
    </div>
    <div class="tabs-block-more-wrapper">
      <a href="#" class="tabs-block-more with-arrow">
        További hírek ebben a témában
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8H15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </a>
    </div>
  </div>
</section>
