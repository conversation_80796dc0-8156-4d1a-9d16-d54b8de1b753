import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { GalleryBlockCardComponent } from '../gallery-block-card/gallery-block-card.component';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { GalleryCard } from '@trendency/kesma-ui';

@Component({
  selector: 'app-gallery-block',
  templateUrl: './gallery-block.component.html',
  styleUrls: ['./gallery-block.component.scss'],
  imports: [NgIf, NgFor, GalleryBlockCardComponent, RouterLink],
})
export class GalleryBlockComponent {
  currentPage = 1;
  pageCount = 1;
  pageData: GalleryCard[] = [];
  galleries: string[] = [];
  private cardData: GalleryCard[] = [];
  private displayGalleriesCount = 3;

  get displayCount(): number {
    return this.displayGalleriesCount;
  }

  @Input() set displayCount(count: number) {
    if (this.displayGalleriesCount === count) {
      return;
    }
    this.displayGalleriesCount = count;
    this.setData();
  }

  get data(): GalleryCard[] {
    return this.cardData;
  }

  @Input() set data(cards: GalleryCard[]) {
    if (cards.map(({ id }) => id).filter((id) => !this.galleries.includes(id)).length === 0) {
      return;
    }
    this.cardData = cards;
    this.galleries = this.cardData.map(({ id }) => id);
    this.setData();
  }

  get fromItem(): number {
    return (this.currentPage - 1) * this.displayGalleriesCount;
  }

  get isFirstPage(): boolean {
    return this.currentPage === 1;
  }

  get isLastPage(): boolean {
    return this.currentPage === this.pageCount;
  }

  setData(): void {
    this.pageCount = Math.ceil(this.cardData?.length / this.displayCount) ?? 0;
    this.setPageData();
  }

  setPageData(): void {
    this.pageData = this.cardData?.slice(this.fromItem, this.fromItem + this.displayCount) ?? [];
  }

  onPrevPageClick(): void {
    this.currentPage--;
    this.setPageData();
  }

  onNexPageClick(): void {
    this.currentPage++;
    this.setPageData();
  }
}
