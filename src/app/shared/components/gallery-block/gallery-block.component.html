<section class="gallery-block">
  <div class="wrapper">
    <div class="gallery-block-header">
      <h2 class="gallery-block-title">Képgaléria</h2>
      <div class="carousel-controls" *ngIf="data?.length && displayCount < data?.length">
        <button class="left-arrow" [disabled]="isFirstPage" (click)="onPrevPageClick()"></button>
        <button class="right-arrow" [disabled]="isLastPage" (click)="onNexPageClick()"></button>
      </div>
    </div>

    <div class="gallery-item-container">
      <ng-container *ngFor="let item of pageData">
        <div class="gallery-item">
          <app-gallery-block-card [data]="item"></app-gallery-block-card>
        </div>
      </ng-container>
    </div>
    <div class="gallery-block-more-wrapper">
      <a [routerLink]="['/', 'galeriak']" class="gallery-block-more">
        To<PERSON><PERSON><PERSON><PERSON>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8H15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </a>
    </div>
  </div>
</section>
