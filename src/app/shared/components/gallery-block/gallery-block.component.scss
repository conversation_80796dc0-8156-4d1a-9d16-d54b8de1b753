@use 'shared' as *;

.gallery-block {
  background-color: $grey-5;
  padding: 30px 0;
  position: relative;

  &:after {
    width: 100vw;
    background-color: $grey-5;
    right: 0;
    height: 100%;
    top: 0;
    display: block;
    content: ' ';
    position: absolute;
    z-index: -1;
    // height: 102%;
  }

  &:before {
    width: 100vw;
    background-color: $grey-5;
    left: 0;
    height: 100%;
    top: 0;
    display: block;
    content: ' ';
    position: absolute;
    z-index: -1;
    // height: 102%;
  }

  .carousel-controls {
    margin-left: auto;
    display: flex;

    .left-arrow,
    .right-arrow {
      width: 30px;
      height: 30px;
      cursor: pointer;
      filter: brightness(0) invert(1);
    }

    .left-arrow {
      @include icon('icons/carousel-left.svg');
      margin-right: 10px;
    }

    .right-arrow {
      @include icon('icons/carousel-right.svg');
    }
  }

  .gallery-block-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;

    .carousel-controls {
      margin-left: auto;
      display: flex;

      .left-arrow,
      .right-arrow {
        width: 30px;
        height: 30px;
        cursor: pointer;
      }

      .left-arrow {
        @include icon('icons/carousel-left.svg');
        margin-right: 10px;
        transition-duration: 0.3s;

        &[disabled] {
          opacity: 0.3;
          transition-duration: 0.3s;
        }

        &:hover {
          filter: brightness(1) invert(0);
          transition-duration: 0.3s;
        }
      }

      .right-arrow {
        @include icon('icons/carousel-right.svg');
        transition-duration: 0.3s;

        &[disabled] {
          opacity: 0.3;
          transition-duration: 0.3s;
        }

        &:hover {
          filter: brightness(1) invert(0);
          transition-duration: 0.3s;
        }
      }
    }
  }

  .gallery-block-title {
    @include article-before-line();
    @include article-before-title-style();

    &:before {
      background-color: $white;
    }

    color: $white;
    margin-bottom: 30px;
  }

  .gallery-block-more-wrapper {
    display: flex;
    justify-content: flex-end;
  }

  .gallery-item-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    @include media-breakpoint-down(xs) {
      flex-wrap: nowrap;
      overflow: auto;
    }
  }

  .gallery-item {
    width: calc(33% - 17px);
    @include media-breakpoint-down(sm) {
      width: calc(50% - 30px);
    }
    @include media-breakpoint-down(xs) {
      width: auto;
      flex: 1 0 240px;
      margin-right: 15px;
      &:last-child {
        margin-right: -15px;
      }
    }
  }

  .gallery-block-more {
    display: inline-flex;
    color: $white;

    svg {
      margin-left: 14px;

      path {
        stroke: $white;
      }
    }
  }
}

.wrapper {
  max-width: 100%;
}
