import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { RunScriptsDirective, UtilService } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';
import { BypassPipe, HtmlEmbed } from '@trendency/kesma-ui';

@Component({
  selector: 'app-html-embed',
  templateUrl: './html-embed.component.html',
  styleUrls: ['./html-embed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, BypassPipe, RunScriptsDirective],
})
export class HtmlEmbedComponent implements OnInit, OnDestroy {
  @Input() data: HtmlEmbed;
  @ViewChild('desktopContainer') readonly desktopContainer: ElementRef<HTMLDivElement>;
  @ViewChild('mobileContainer') readonly mobileContainer: ElementRef<HTMLDivElement>;
  isBrowser: boolean;
  isMobile: boolean;

  MOBILE_BREAKPOINT = '(min-width: 768px)';

  private breakpointSubscription: Subscription;

  constructor(
    private readonly utils: UtilService,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.isBrowser = this.utils.isBrowser();
    this.breakpointSubscription = this.breakpointObserver.observe([this.MOBILE_BREAKPOINT]).subscribe((state: BreakpointState) => {
      this.isMobile = !state.matches;
      this.changeRef.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.breakpointSubscription?.unsubscribe();
  }
}
