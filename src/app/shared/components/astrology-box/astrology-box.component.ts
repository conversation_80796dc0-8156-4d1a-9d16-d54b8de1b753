import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { filter, map } from 'rxjs';
import { AstrologyCardComponent } from '../astrology-card/astrology-card.component';
import { SwiperBaseComponent } from '@trendency/kesma-ui';
import { AstrologyBox, BackendAstrologyResponse } from '../../definitions';
import { ApiService } from '../../services';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-astrology-box',
  templateUrl: './astrology-box.component.html',
  styleUrls: ['./astrology-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AstrologyCardComponent, NgIf, AsyncPipe, NgForOf],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AstrologyBoxComponent extends SwiperBaseComponent<AstrologyBox> {
  private readonly apiService: ApiService = inject(ApiService);

  swiperBreakpoints = {
    1400: {
      slidesPerView: 4.2,
      slidesPerGroup: 1,
    },
    1024: {
      slidesPerView: 3.2,
      slidesPerGroup: 1,
    },
    576: {
      slidesPerView: 2.2,
      slidesPerGroup: 1,
    },
  };

  astrologyData$ = this.apiService.getAstrology().pipe(
    filter((res: BackendAstrologyResponse) => !!res),
    map((astrologyResponse: BackendAstrologyResponse) => {
      return this.getAstrologyCardData(astrologyResponse);
    })
  );

  private getAstrologyCardData(astrologyResponse: BackendAstrologyResponse): AstrologyBox {
    const { jegy_linkek, jegy_szovegek } = astrologyResponse;

    return {
      slug: '',
      data: [
        {
          title: 'KOS',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_kos.png',
          date: 'III. 21. - IV. 20.',
          lead: jegy_szovegek?.kos,
          url: jegy_linkek?.kos,
        },
        {
          title: 'BIKA',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_bika.png',
          date: 'IV. 20. - V. 21.',
          lead: jegy_szovegek?.bika,
          url: jegy_linkek?.bika,
        },
        {
          title: 'IKREK',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_ikrek.png',
          date: 'V. 21. - VI. 21.',
          lead: jegy_szovegek?.ikrek,
          url: jegy_linkek?.ikrek,
        },
        {
          title: 'RÁK',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_rak.png',
          date: 'VI. 21. - VII. 23.',
          lead: jegy_szovegek?.rak,
          url: jegy_linkek?.rak,
        },
        {
          title: 'OROSZLÁN',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_oroszlan.png',
          date: 'VII. 23. - VIII. 23.',
          lead: jegy_szovegek?.oroszlan,
          url: jegy_linkek?.oroszlan,
        },
        {
          title: 'SZŰZ',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_szuz.png',
          date: 'VIII. 23. - IX. 23.',
          lead: jegy_szovegek?.szuz,
          url: jegy_linkek?.szuz,
        },
        {
          title: 'MÉRLEG',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_merleg.png',
          date: 'IX. 23. - X. 23.',
          lead: jegy_szovegek?.merleg,
          url: jegy_linkek?.merleg,
        },
        {
          title: 'SKORPIÓ',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_skorpio.png',
          date: 'X. 23. - XI. 22.',
          lead: jegy_szovegek?.skorpio,
          url: jegy_linkek?.skorpio,
        },
        {
          title: 'NYILAS',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_nyilas.png',
          date: 'XI. 22. - XII. 21.',
          lead: jegy_szovegek?.nyilas,
          url: jegy_linkek?.nyilas,
        },
        {
          title: 'BAK',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_bak.png',
          date: 'XII. 21. - I. 20.',
          lead: jegy_szovegek?.bak,
          url: jegy_linkek?.bak,
        },
        {
          title: 'VÍZÖNTŐ',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_vizonto.png',
          date: 'I. 20. - II. 19.',
          lead: jegy_szovegek?.vizonto,
          url: jegy_linkek?.vizonto,
        },
        {
          title: 'HALAK',
          image: 'https://www.astronet.hu/wp-content/plugins/mw-horoszkop/assets/astronet_icon_halak.png',
          date: 'II. 19. - III. 21.',
          lead: jegy_szovegek?.halak,
          url: jegy_linkek?.halak,
        },
      ],
    };
  }
}
