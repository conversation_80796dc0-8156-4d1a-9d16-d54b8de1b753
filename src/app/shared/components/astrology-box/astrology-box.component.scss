@use 'shared' as *;

.astrology-box {
  background-color: $grey-22;
  padding: 30px 0;
  color: $black;
  position: relative;
  margin-bottom: 50px;
  width: 1900px;

  @include media-breakpoint-down(md) {
    width: calc(100% + 30px);
  }

  &:before,
  &:after {
    position: absolute;
    content: ' ';
    display: block;
    width: 50vw;
    height: 100%;
    top: 0;
    z-index: 0;
    background: $grey-22;
  }

  &:before {
    right: 100%;
  }

  &:after {
    left: 100%;
    // z-index: 100;
  }

  .astrology-box-title {
    @include article-before-line();
    @include article-before-title-style();
  }

  .astrology-box-body {
    position: relative;
    max-width: 100%;
    z-index: 2;

    &:before {
      position: absolute;
      content: '';
      width: 50vw;
      left: -50vw;
      height: 100%;
      background-color: $grey-22;
      z-index: 1;
    }
  }

  .astrology-box-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    width: calc(100vw - 40px);
    max-width: $global-wrapper-width;
    @include media-breakpoint-down(md) {
      width: unset;
      padding-right: 30px;
    }
  }

  ::ng-deep {
    .astrology-card {
      display: block;
      margin-right: 30px;
      height: 450px;
      @include media-breakpoint-down(md) {
        height: 470px;
      }
    }
  }

  .carousel-controls {
    margin-left: auto;
    display: flex;

    .left-arrow,
    .right-arrow {
      width: 30px;
      height: 30px;
      cursor: pointer;
    }

    .left-arrow {
      @include icon('icons/carousel-left.svg');
      margin-right: 10px;
    }

    .right-arrow {
      @include icon('icons/carousel-right.svg');
    }
  }

  .astrology-box-more-wrapper {
    margin-top: 25px;
    display: flex;
    justify-content: flex-end;
    width: calc(100vw - 40px);
    max-width: $global-wrapper-width;
    @include media-breakpoint-down(md) {
      width: unset;
      padding-right: 30px;
    }
  }

  .astrology-box-more {
    @include read-more-blue-arrow();
    display: inline-flex;
  }
}

.full {
  margin: 0;
  width: $global-wrapper-width;
  overflow: hidden;
}

.wrapper {
  width: 100%;
  max-width: $global-wrapper-width;
}
