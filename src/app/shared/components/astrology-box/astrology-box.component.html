<section class="astrology-box">
  <div class="wrapper full">
    <div class="astrology-box-header">
      <h2 class="astrology-box-title">Asztrológia</h2>
      <div class="carousel-controls">
        <button class="left-arrow" (click)="swipePrev()"></button>
        <button class="right-arrow" (click)="swipeNext()"></button>
      </div>
    </div>
    <div class="astrology-box-body">
      <swiper-container #swiper [breakpoints]="swiperBreakpoints" slides-per-view="1.2" slides-per-group="1" speed="1000" rewind="true" loop="true">
        <ng-container *ngIf="astrologyData$ | async as data">
          <swiper-slide *ngFor="let item of data.data">
            <div>
              <app-astrology-card [data]="item" class="astrology-card"></app-astrology-card>
            </div>
          </swiper-slide>
        </ng-container>
      </swiper-container>
      <div class="astrology-box-more-wrapper">
        <a href="https://www.astronet.hu/horoszkop/" class="astrology-box-more">
          További hírek ebben a témában
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 8H15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>
