@use 'shared' as *;

.fairy-card {
  width: 100%;
  position: relative;
  margin-bottom: 40px;
  @include media-breakpoint-down(md) {
    margin-bottom: 20px;
  }

  &.style-1 {
    .fairy-card-inner {
      @include imgRatio(440%, 518%);
      background-size: cover;

      &:before {
        content: ' ';
        display: block;
        position: absolute;
        left: 0;
        bottom: 0;
        height: 45%;
        width: 100%;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
      }

      .poser {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: 30px 40px;
        color: $white;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        flex-wrap: wrap;

        .left-column {
          width: calc(100% - 90px - 10px);

          .name {
            font-style: normal;
            font-weight: 500;
            font-size: 30px;
            line-height: 40px;
            width: 100%;
          }

          .age {
            font-weight: normal;
            font-size: 16px;
            line-height: 30px;
            display: inline-block;
          }

          .location {
            font-weight: normal;
            font-size: 16px;
            line-height: 30px;
            margin-left: 30px;
            display: inline-block;
          }
        }

        .right-column {
          width: 90px;
          display: flex;
          justify-content: flex-end;
          align-items: flex-end;

          .point {
            background: $white;
            border-radius: 6px;
            color: $red;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            padding: 13px;
            white-space: nowrap;

            &:before {
              margin-bottom: -6px;
              display: inline-block;
              content: ' ';
              width: 22px;
              height: 22px;
              @include icon('icons/heart-red.svg');
              margin-right: 12px;
            }
          }
        }
      }
    }
  }

  &.style-2 {
    .fairy-card-inner {
      @include media-breakpoint-down(sm) {
        margin-left: -15px;
        margin-right: -15px;
        width: calc(100% + 30px);
      }

      &.desktop {
        @include imgRatio(320%, 376%);
        background-size: cover;

        .poser {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          padding: 30px;
          color: $white;
          display: flex;
          align-items: flex-end;
          flex-wrap: wrap;

          .name {
            width: 100%;
            font-weight: 500;
            font-size: 20px;
            line-height: 26px;
          }

          .age {
            font-weight: normal;
            font-size: 16px;
            line-height: 30px;
            display: inline-block;
            margin-right: 30px;
          }

          .location {
            font-weight: normal;
            font-size: 16px;
            line-height: 30px;
            display: inline-block;
          }
        }

        &:before {
          content: ' ';
          display: block;
          position: absolute;
          left: 0;
          bottom: 0;
          height: 45%;
          width: 100%;
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
        }

        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      &.mobile {
        display: none;
        justify-content: space-between;
        @include media-breakpoint-down(md) {
          display: flex;
        }

        .image-wrapper {
          width: 120px;

          .image {
            @include imgRatio(1%, 1%);
            background-size: cover;
            background-position: center;
          }
        }

        .content {
          width: calc(100% - 140px);
          color: $black;

          .name {
            font-weight: 500;
            font-size: 20px;
            line-height: 26px;
          }

          .age {
            font-weight: normal;
            font-size: 16px;
            line-height: 30px;
            display: inline-block;
            margin-right: 30px;
          }

          .location {
            font-weight: normal;
            font-size: 16px;
            line-height: 30px;
            display: inline-block;
          }
        }
      }
    }
  }
}
