<article class="fairy-card style-{{ styleID }}">
  <!-- STYLE 1 -->
  <ng-container *ngIf="data && styleID === fairyCardType.BIG_CARD_WITH_VOTE">
    <a
      [routerLink]="['/', 'tunderszepek', data.slug]"
      class="fairy-card-inner"
      trImageLazyLoad
      [ngStyle]="{ 'background-image': data.thumbnail ? 'url(' + data.thumbnail + ')' : 'none' }"
    >
      <div class="poser">
        <div class="left-column">
          <h4 class="name">{{ data.name }}</h4>
          <span class="age">{{ data.age }}</span>
          <span class="location">{{ data.county }}</span>
        </div>
        <div class="right-column">
          <span class="point">{{ data.vote_value }}</span>
        </div>
      </div>
    </a>
  </ng-container>
  <!-- STYLE 2 -->
  <ng-container *ngIf="data && styleID === fairyCardType.SMALL_CARD_NO_VOTE">
    <a
      [routerLink]="['/', 'tunderszepek', data.slug]"
      class="fairy-card-inner desktop"
      trImageLazyLoad
      [ngStyle]="{ 'background-image': data.thumbnail ? 'url(' + data.thumbnail + ')' : 'none' }"
    >
      <div class="poser">
        <h4 class="name">{{ data.name }}</h4>
        <span class="age">{{ data.age }}</span>
        <span class="location">{{ data.county }}</span>
      </div>
    </a>
    <a [routerLink]="['/', 'tunderszepek', data.slug]" class="fairy-card-inner mobile">
      <div class="image-wrapper">
        <div class="image" trImageLazyLoad [ngStyle]="{ 'background-image': data.thumbnail ? 'url(' + data.thumbnail + ')' : 'none' }"></div>
      </div>
      <div class="content">
        <h4 class="name">{{ data.name }}</h4>
        <span class="age">{{ data.age }}</span>
        <span class="location">{{ data.county }}</span>
      </div>
    </a>
  </ng-container>
</article>
