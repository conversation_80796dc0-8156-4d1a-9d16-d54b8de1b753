import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgIf, Ng<PERSON>tyle } from '@angular/common';
import { FairyCardTypes, FairyPreviewInterface } from '../../definitions';
import { ImageLazyLoadDirective } from '@trendency/kesma-core';

@Component({
  selector: 'app-fairy-card',
  templateUrl: './fairy-card.component.html',
  styleUrls: ['./fairy-card.component.scss'],
  imports: [NgIf, RouterLink, NgStyle, ImageLazyLoadDirective],
})
export class FairyCardComponent {
  @Input() styleID: number;
  @Input() data: FairyPreviewInterface;

  readonly fairyCardType = FairyCardTypes;
}
