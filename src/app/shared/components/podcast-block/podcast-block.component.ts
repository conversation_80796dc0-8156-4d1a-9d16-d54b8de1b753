import { Component, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { PodcastCardComponent } from '../podcast-card/podcast-card.component';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'app-podcast-block',
  templateUrl: './podcast-block.component.html',
  styleUrls: ['./podcast-block.component.scss'],
  imports: [NgIf, NgF<PERSON>, PodcastCardComponent],
})
export class PodcastBlockComponent {
  @Input() data: ArticleCard[];
  @Input() buttonUrl = '';
}
