@use 'shared' as *;

.podcast-block {
  background-color: $grey-5;
  padding: 30px 0 30px 0;
  position: relative;

  @include media-breakpoint-down(md) {
    padding: 30px;
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
  }

  &::before,
  &::after {
    content: ' ';
    display: block;
    position: absolute;
    top: 0;
    width: calc((#{$global-wrapper-width} / 2) + 2px);
    height: 100%;
    background-color: $grey-5;
  }

  &::before {
    left: 1px;
    transform: translateX(-100%);
  }

  &::after {
    right: 1px;
    transform: translateX(100%);
  }

  &-title {
    font-size: 20px;
    font-weight: 600;
    color: white;
    border-left: solid 5px white;
    padding: 5px 0 5px 10px;
    cursor: default;
  }

  &-wrapper {
    margin-top: 10px;
    display: flex;
    gap: 40px;
    flex-direction: row;
    justify-content: space-between;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
  }

  &-card {
    width: 100%;

    @include media-breakpoint-up(lg) {
      max-width: 320px;
    }
  }

  &-button {
    padding: 25px 0 10px 0;

    &-wrapper {
      color: $white;
      display: flex;
      align-items: center;
      cursor: pointer;
      justify-content: flex-end;

      p {
        font-family: 'Prompt', sans-serif;
        font-weight: 500;
        font-size: 16px;
        line-height: 25px;
      }

      img {
        width: 14px;
        height: 14px;
        margin-left: 15px;
      }
    }

    @include media-breakpoint-down(md) {
      display: none;
    }
  }
}
