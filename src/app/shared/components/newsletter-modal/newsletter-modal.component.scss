@use 'shared' as *;

.newsletter-box {
  position: fixed;
  z-index: 1001;
  bottom: 0;
  right: 15%;
  // Ha fix szín:
  background: linear-gradient(73.24deg, #005d9d 4.39%, #006d35 93.26%);
  // Ha az <PERSON>al s<PERSON> kell, hogy felvegye:
  // background: linear-gradient(73.24deg, var(--primary-color) 4.39%, var(--secondary-color) 93.26%);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.25);
  width: 446px;
  height: 335px;
  animation: 1s slide-up;
  text-align: center;

  @include media-breakpoint-down(md) {
    width: 100%;
    right: 0;
  }

  @include media-breakpoint-down(sm) {
    height: auto;
  }

  &-wrapper {
    background: url('/assets/images/level-pattern.svg') no-repeat center center;
    background-size: cover;
    padding: 30px;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .title {
    font-weight: 500;
    font-size: 35px;
    line-height: 40px;
    color: $white;
    margin-bottom: 15px;
  }

  .text {
    font-weight: 500;
    font-size: 15px;
    line-height: 23px;
    color: $white;
  }

  .signup-button {
    max-width: 315px;
    margin: 30px auto 25px auto;
    background-color: $white;
    padding: 15px 25px;
    display: block;
    font-size: 22px;
    line-height: 26px;
    font-weight: 500;
    color: var(--link-color);
    cursor: pointer;
  }

  .close-button {
    color: $white;
    font-weight: 500;
    font-size: 15px;
    line-height: 23px;
  }
}

.closed {
  animation: 1s slide-down;
}

@keyframes slide-up {
  from {
    height: 0;
  }
  to {
    height: 335px;
  }
}
