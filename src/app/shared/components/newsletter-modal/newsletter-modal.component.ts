import { DOCUMENT, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, OnDestroy, OnInit } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { NewsletterModalService, PortalConfigService } from '../../services';
import { AnalyticsService, NEWSLETTER_COMPONENT_TYPE } from '@trendency/kesma-ui';

@Component({
  selector: 'app-newsletter-modal',
  templateUrl: './newsletter-modal.component.html',
  styleUrls: ['./newsletter-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class NewsletterModalComponent implements OnInit, OnDestroy {
  isOpen: boolean;
  window?: Window; // = window;
  urlUtmEmailMedium = false; // = window.location.search.includes('utm_medium=email');
  private readonly document: Document = inject(DOCUMENT);

  constructor(
    private readonly newsletterService: NewsletterModalService,
    private readonly analyticsService: AnalyticsService,
    private readonly portalConfig: PortalConfigService,
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  get portalName(): string {
    const { portalName } = this.portalConfig;
    return portalName;
  }

  ngOnInit(): void {
    this.urlUtmEmailMedium = this.document.location.search.includes('utm_medium=email');
    if (this.utilsService.isBrowser()) {
      this.window = window;
      window.addEventListener('scroll', this.scrollEvent, true);
      if (this.urlUtmEmailMedium) {
        this.newsletterService.postponeFormMonth();
        this.isOpen = false;
      }
    }
  }

  scrollEvent = (): void => {
    const offset = this.window?.scrollY || this.window?.document.documentElement.scrollTop || this.window?.document.body.scrollTop || 0;
    if (!this.newsletterService.isPostponed) {
      if (offset > 100) {
        this.openModal();
        this.removeEventListener();
      }
    } else {
      this.removeEventListener();
    }
  };

  openModal(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.analyticsService.newsLetterPopupVisible();
        this.isOpen = true;
        this.changeRef.detectChanges();
      }, 1000);
    }
  }

  onCloseClicked(): void {
    this.newsletterService.postponeFormMonth();
    this.isOpen = false;
    this.changeRef.detectChanges();
  }

  onSubscribeClicked(): void {
    this.newsletterService.postponeForYear();
    this.isOpen = false;
    this.changeRef.detectChanges();

    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.POPOVER);
  }

  removeEventListener(): void {
    if (this.utilsService.isBrowser()) {
      window.removeEventListener('scroll', this.scrollEvent, true);
    }
  }

  ngOnDestroy(): void {
    if (this.utilsService.isBrowser()) {
      this.removeEventListener();
    }
  }
}
