import { ChangeDetectionStrategy, Component, effect, inject, input, signal } from '@angular/core';
import { ApiService } from '../../services';
import { VariableDidYouKnowBox } from '@trendency/kesma-ui';
import { VariableSponsoredDidYouKnowComponent } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';

@Component({
  selector: 'app-variable-sponsored-did-you-know-wrapper',
  templateUrl: './variable-sponsored-did-you-know-wrapper.component.html',
  styleUrls: ['./variable-sponsored-did-you-know-wrapper.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [VariableSponsoredDidYouKnowComponent, ArticleCardComponent],
})
export class VariableSponsoredDidYouKnowWrapperComponent {
  readonly #api = inject(ApiService);
  id = input<string>();
  data = signal<VariableDidYouKnowBox | null>(null);
  constructor() {
    effect(() => {
      const id = this.id();
      if (!id?.length) {
        return;
      }
      this.#api.getVariableSponsoredDidYouKnowBox(id).subscribe((data) => {
        this.data.set(data);
      });
    });
  }
}
