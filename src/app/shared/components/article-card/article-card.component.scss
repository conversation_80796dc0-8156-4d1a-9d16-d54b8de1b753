@use 'shared' as *;

.article-card {
  margin-bottom: 20px;
  position: relative;

  .sponsor-title {
    position: absolute;
    top: 20px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    height: 30px;
    display: inline-flex;
    align-items: center;
    border-radius: 6px;
    padding: 0 10px;
    font-family: $font-inter;
    color: $white;
    background-color: $red;
    right: 20px;
    left: unset;
    z-index: 20;

    &.relative {
      position: relative;
      top: auto;
      left: auto;
      right: auto;
    }

    &.desktop {
      @include media-breakpoint-down(sm) {
        display: none;
      }
    }

    &.mobile {
      display: none;
      @include media-breakpoint-down(sm) {
        display: inline-flex;
      }
    }
  }

  .article-only-desktop {
    @include media-breakpoint-down(sm) {
      display: none !important;
    }
  }

  .article-only-mobile {
    display: none !important;
    @include media-breakpoint-down(sm) {
      display: flex !important;
    }
  }

  .article-left-image {
    height: 120px;
    flex: 0 120px;
    width: 120px;
    filter: blur(0);
  }

  .article-block {
    min-height: inherit;
    display: flex;
    align-items: flex-end;
    justify-content: stretch;
  }

  .article-bottom {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    flex: 1;
    z-index: 1;
    position: relative;
    pointer-events: none;

    @include media-breakpoint-down(sm) {
      align-items: unset;
      width: 100%;
    }
  }

  .article-image-special {
    background-size: cover;
    display: block;
    background-position: center top;
    background-repeat: no-repeat;
    transition: all 0.15s ease-in-out;
    object-fit: cover;
    filter: blur(0);

    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    position: absolute;

    &:hover {
      @include media-breakpoint-up(sm) {
        transform: scale(1.1);
      }
    }
  }

  .article-image,
  .article-link,
  .background-image {
    position: relative;
    display: block;
    overflow: hidden;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    object-fit: cover;

    img.article-thumbnail {
      width: 100%;
      height: 100%;
    }

    &::after {
      content: '';
    }

    &::after,
    img.article-thumbnail {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: inherit;
      background-size: cover;
      transform-origin: center;
      transition: all 0.15s ease-in-out;
      object-fit: cover;
      filter: blur(0);
    }

    &:focus,
    &:hover {
      img.article-thumbnail {
        transform: scale(1.2);
      }

      &::after {
        transform: scale(1.2);
      }
    }
  }

  .article-link {
    display: table;
    color: $white;
    @include media-breakpoint-down(sm) {
      width: 100%;
    }
  }

  .article-date {
    font-size: 14px;
    color: $grey-22;
    text-align: right;
    min-width: 80px;
  }

  .article-title {
    font-weight: 500;
    margin-top: 6px;
  }

  .article-sub-title-wrapper {
    display: flex;

    .article-date {
      margin-left: auto;
    }
  }

  .article-tag {
    position: absolute;
    top: 20px;
    left: 20px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: $grey-7;
    height: 30px;
    background-color: $white;
    display: inline-flex;
    align-items: center;
    border-radius: 6px;
    padding: 0 10px;
    font-family: $font-inter;

    &.black {
      color: $white;
      background-color: $black;
    }

    &.red {
      color: $white;
      background-color: $red;
    }

    &.blue {
      color: $white;
      background-color: $blue;
    }

    &.branded {
      color: $tag-text-color;
      background-color: $tag-color;
    }

    &.primary {
      color: $white;
      background-color: $primary-color;
    }

    &.secondary {
      color: $white;
      background-color: $secondary-color;
    }

    &.simple {
      background: transparent;
      padding: 0;
      text-transform: capitalize;
      font-size: 14px;
    }

    &.to-right {
      right: 20px;
      left: unset;
    }
  }

  &.style-1,
  &.style-2 {
    overflow: hidden;

    &:after {
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
      position: absolute;
      width: 100%;
      height: 50%;
      bottom: 0;
      left: 0;
      content: '';
      pointer-events: none;
    }
  }

  &.style-1 {
    min-height: 540px;
    @include media-breakpoint-down(sm) {
      min-height: 211px;
      width: 100vw;
      margin-left: -15px;

      &:after {
        display: none;
      }
    }

    .article-block {
      display: flex;
      align-items: flex-end;

      @include media-breakpoint-down(sm) {
        padding: 0;
        margin: 0 -15px;
      }
    }

    .article-tag {
      @include media-breakpoint-down(sm) {
        left: 0;
      }
    }

    .article-only-mobile {
      padding: 0 15px;
      display: flex;
      align-items: flex-end;
      position: relative;

      .article-link {
        color: black;
      }

      .article-date {
        color: $grey-7;
      }
    }

    .article-only-desktop {
      position: absolute;
      width: 100%;
      bottom: 0;
      padding: 53px 40px;
    }

    .article-bottom {
      pointer-events: auto;
      @include media-breakpoint-down(sm) {
        align-items: flex-start;
        margin-top: 10px;
      }
    }

    .article-date {
      margin-bottom: 7px;
      @include media-breakpoint-down(sm) {
        margin-bottom: 0;
      }
    }

    .article-title {
      font-size: 40px;
      line-height: 50px;

      @include media-breakpoint-down(sm) {
        font-size: 26px;
        line-height: 32px;
      }
    }

    .article-sub-title {
      font-size: 20px;
      margin-bottom: 10px;

      @include media-breakpoint-down(sm) {
        font-size: 14px;
        margin-bottom: 0;
      }
    }

    .article-image-special {
      filter: blur(0);
      @include media-breakpoint-down(sm) {
        margin-bottom: 10px;
        position: relative;
      }

      &:hover {
        @include media-breakpoint-up(sm) {
          transform: scale(1.2);
        }
      }
    }
  }

  &.style-2 {
    min-height: 260px;

    @include media-breakpoint-down(sm) {
      min-height: auto;
      margin-left: -15px;
      &:after {
        display: none;
      }
    }

    .article-title {
      font-size: 20px;
      line-height: 24px;

      @include media-breakpoint-down(sm) {
        font-size: 16px;
        line-height: 22px;
      }
    }

    .article-bottom {
      @include media-breakpoint-down(sm) {
        padding-left: 10px;
        flex-direction: column;
      }
    }

    .article-tag {
      @include media-breakpoint-down(sm) {
        position: static;
        margin-left: auto;
        height: 24px;
        font-size: 10px;
      }
    }

    .article-block {
      padding: 20px;
      display: flex;
      @include media-breakpoint-down(sm) {
        padding: 0;
        align-items: flex-start;
      }
    }

    .article-footer {
      margin-top: 10px;
      width: 100%;
      display: flex;
      align-items: center;
    }

    .article-sub-title {
      font-size: 14px;

      &.isSponsored {
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }

    .article-date {
      margin-bottom: 2px;
      @include media-breakpoint-down(sm) {
        color: $grey-16;
        font-size: 12px;
        min-width: unset;
      }
    }

    .article-link {
      @include media-breakpoint-down(sm) {
        color: $black;
      }
    }

    .article-image {
      position: relative;

      img.article-thumbnail {
        -webkit-transform-style: unset;
        transform-style: unset;
      }
    }

    .article-left-image {
      background-size: cover;
      display: block;
      background-position: center top;
      filter: blur(0);
    }
  }

  &.style-3 {
    .article-title {
      color: $black;
      font-size: 16px;
      line-height: 22px;
    }

    min-height: 87px;
    margin-top: 0;

    .article-block {
      align-items: baseline;
      padding: 0px 25px 0px 25px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      @include article-before-line();
    }

    @include media-breakpoint-down(md) {
      .article-block {
        padding-left: 45px;
      }

      .article-block:before {
        margin-left: 5px;
      }
    }
  }

  &.style-4 {
    background-color: $grey-22;
    // margin-right: 15px;
    // margin-left: 15px;
    .article-block {
      align-items: center;
      padding: 25px 33px;
      @include article-before-line();

      &:before {
        background-color: $red;
      }

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        align-items: flex-start;
        padding: 25px;
        margin: unset;
      }
    }

    .article-tag {
      position: static;
      @include media-breakpoint-down(sm) {
        margin-bottom: 10px;
      }
    }

    .article-title {
      color: $black;
      font-weight: 600;
      font-size: 20px;
      @include media-breakpoint-down(sm) {
        margin-bottom: 10px;
      }
    }

    .article-date {
      color: $grey-7;
      font-weight: 500;
    }

    .article-more {
      @include read-more-blue-arrow();
    }

    .article-seperator {
      &.first {
        margin-right: 50px;
        @include media-breakpoint-down(sm) {
          margin-right: 0;
        }
      }

      &.second {
        max-width: 550px;
        @include media-breakpoint-down(sm) {
          max-width: unset;
        }
      }

      &.third {
        min-width: 70px;
        margin-left: auto;
        padding-left: 10px;
        @include media-breakpoint-down(sm) {
          padding-left: 0;
          position: absolute;
          top: 30px;
          right: 27px;
        }
      }

      &.fourth {
        margin-left: 40px;
        @include media-breakpoint-down(sm) {
          margin-left: 0;
        }
      }
    }
  }

  &.style-5 {
    .article-link {
      color: $black;
      position: relative;

      &.show-play-button {
        &:before {
          @include icon('icons/play-button-black.svg');

          content: '';
          width: 50px;
          height: 50px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    .article-block {
      flex-direction: column;
      @include media-breakpoint-down(sm) {
        margin: 0 -15px;
        padding: 0;
      }
    }

    .article-image {
      @include imgRatio(16, 9);
      @include imgZoom();
      margin-bottom: 10px;
      filter: blur(0);
      position: relative;
    }

    .article-date {
      color: $grey-7;
    }

    .article-link {
      width: 100%;
    }

    .article-bottom {
      align-items: unset;
      width: 100%;
      pointer-events: all;
      @include media-breakpoint-down(sm) {
        padding: 0 15px;
      }
    }

    .article-sub-title {
      font-size: 14px;
      line-height: 21px;
    }

    .article-title {
      font-size: 20px;
      line-height: 30px;
    }

    .article-tag {
      &.black {
        &:before {
          @include icon('icons/play-button-white.svg');

          content: '';
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
      }
    }
  }

  &.style-6 {
    .article-block {
      // display: flex;
      // flex-direction: column;
      height: 100%;
      justify-content: center;
      align-items: stretch;

      @include media-breakpoint-down(sm) {
        margin-left: -15px;
      }
    }

    .article-left-image {
      transform: translateZ(0px);
      filter: blur(0);
      object-fit: cover;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .article-bottom {
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      padding-left: 10px;
    }

    .article-link {
      color: $black;
    }

    .article-title {
      font-size: 16px;
      line-height: 22px;
    }

    .article-sub-title {
      font-size: 14px;
      line-height: 21px;
    }

    .article-tag {
      position: static;
      margin-bottom: 10px;
    }
  }

  &.style-7 {
    .article-block {
      &:visited,
      &:active,
      &:link {
        color: $base-text-color;
      }

      align-items: center;
      background: $grey-25;
      border: 1px solid $primary-color;
      border-radius: 6px;
      padding: 20px;
    }

    .article-left {
      width: 150px;
      margin-right: 30px;
      overflow: hidden;
      filter: blur(0);
      @include media-breakpoint-down(md) {
        width: 128px;
        margin-right: 10px;
      }

      .article-left-image {
        flex: initial;
        min-height: 0;
        position: relative;
        max-width: none;
        transition: all 0.15s ease-in-out;
        object-fit: cover;
        width: 100%;
        height: 100%;

        &:hover {
          transform: scale(1.1);
          transition: all 300ms;
        }
      }
    }

    .article-bottom {
      flex-direction: column;
      align-items: flex-start;
      padding-left: 10px;
      width: calc(100% - 200px);
      position: relative;
      @include media-breakpoint-down(md) {
        margin-top: -16px;
        padding-bottom: 35px;
        width: calc(100% - 128px);
      }

      .tag-list {
        .article-tag:not(:last-child) {
          margin-right: 10px;
        }
      }

      .article-link {
        color: $black;
      }

      .article-title {
        font-weight: 500;
        font-size: 24px;
        line-height: 30px;
        @include media-breakpoint-down(md) {
          font-weight: 500;
          font-size: 16px;
          line-height: 22px;
        }
      }

      .article-sub-title,
      .article-excerpt,
      .article-publishdate {
        font-weight: normal;
        font-size: 14px;
        line-height: 21px;
      }

      .article-publishdate {
        position: absolute;
        bottom: 3px;
        color: $grey-16;
      }

      .article-tag {
        position: static;
        margin-bottom: 10px;

        &.branded {
          @include media-breakpoint-down(md) {
            position: absolute;
            bottom: 0;
            right: 0;
            left: initial;
            top: initial;
            margin: 0;
            margin-right: 0 !important; //todo szebben ha lesz ido
          }
        }

        @media (max-width: 417px) {
          display: none;
        }
      }
    }
  }

  &.style-8 {
    .article-block {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      @include media-breakpoint-down(sm) {
        flex-wrap: wrap;
      }

      .left {
        width: 200px;
        margin-right: 40px;
        @include media-breakpoint-down(sm) {
          width: 120px;
          margin-right: 10px;
        }

        .article-image {
          @include imgRatio(1%, 1%);
          @include imgZoom();
        }
      }

      .right {
        width: calc(100% - 240px);
        @include media-breakpoint-down(sm) {
          width: calc(100% - 130px);
        }

        .article-card-content-head {
          a {
            &,
            &:link,
            &:visited,
            &:active {
              color: $black;
            }
          }

          color: $black;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          .article-date {
            color: $grey-7;
            font-size: 14px;
            line-height: 19px;
          }

          .article-tag {
            color: $black;
            position: relative;
            padding: 0;
            margin: 0;
            background: transparent;
            top: 0;
            left: 0;
            font-size: 14px;
            line-height: 21px;
            height: auto;
            font-weight: 400;
            font-family: $font-family;
            text-transform: capitalize;
          }
        }

        .article-link {
          .article-title {
            font-weight: 500;
            font-size: 20px;
            line-height: 26px;
            color: $black;
            margin-bottom: 18px;
            @include media-breakpoint-down(sm) {
              font-size: 16px;
              line-height: 22px;
            }
          }

          .article-sub-title {
            font-size: 16px;
            line-height: 24px;
            color: $black;
            @include media-breakpoint-down(sm) {
              display: none;
            }
          }
        }
      }

      .bottom-mobile {
        display: none;
        padding: 10px 0;
        @include media-breakpoint-down(sm) {
          display: block;
        }

        .article-sub-title {
          font-size: 16px;
          line-height: 22px;
        }
      }
    }
  }

  &.style-9 {
    .article-block {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      @include media-breakpoint-down(sm) {
        flex-wrap: wrap;
      }

      .left {
        width: 200px;
        margin-right: 40px;
        position: relative;
        @include media-breakpoint-down(sm) {
          width: 120px;
          margin-right: 10px;
        }

        .article-image {
          @include imgRatio(1%, 1%);
          @include imgZoom();
        }
      }

      .right {
        margin-right: 5px;
        width: calc(100% - 240px);
        @include media-breakpoint-down(sm) {
          width: calc(100% - 135px);
        }

        .article-card-content-head {
          a {
            &,
            &:link,
            &:visited,
            &:active {
              color: $black;
            }
          }

          color: $black;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          .article-sub-title {
            color: $grey-7;
            font-size: 14px;
            line-height: 19px;
          }

          .article-tag {
            color: $black;
            position: relative;
            text-transform: none;
            padding: 0;
            margin: 0;
            background: transparent;
            top: 0;
            left: 0;
            font-size: 14px;
            line-height: 21px;
            height: auto;
          }
        }

        .article-link {
          .article-title {
            font-weight: 500;
            font-size: 20px;
            line-height: 26px;
            color: $black;
            margin-bottom: 18px;
            @include media-breakpoint-down(sm) {
              font-size: 16px;
              line-height: 22px;
            }
          }

          .article-sub-title {
            font-size: 16px;
            line-height: 24px;
            color: $black;
            @include media-breakpoint-down(sm) {
              display: none;
            }
          }
        }
      }

      .bottom-mobile {
        display: none;
        padding: 10px 0;
        @include media-breakpoint-down(sm) {
          display: block;
        }

        .article-sub-title {
          font-size: 16px;
          line-height: 22px;
          @include media-breakpoint-down(sm) {
            display: none;
          }
        }
      }
    }
  }

  &.style-10 {
    .article-recommendation-block {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
      margin: auto;
      flex-direction: column;
      text-align: center;
      padding: 0;

      @include media-breakpoint-down(md) {
        padding: 0;
      }

      .border {
        width: 100%;
        display: block;
        height: 1px;
        background: radial-gradient(50% 50% at 50% 50%, $red 0%, #c4c4c4 100%);
        margin: 25px 0;
      }

      .block-title {
        font-size: 18px;
        font-weight: 500;
        line-height: 18px;
        text-align: left;
        color: $red;
        text-transform: uppercase;
        margin-bottom: 10px;

        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 16px;
        }
      }

      .article-title {
        font-size: 24px;
        font-style: italic;
        font-weight: 700;
        line-height: 32px;

        i {
          @include icon('icons/arrow-right.svg');
          display: inline-block;
          height: 12px;
          width: 12px;
        }

        @include media-breakpoint-down(md) {
          font-size: 20px;
          font-style: italic;
        }
      }
    }
  }
}

.logo-box-big {
  position: absolute;
  display: flex;
  right: 0;
  z-index: 10;
  height: 70px;
  width: 100px;
  pointer-events: none;

  img {
    // height: 70px;
    // width: 70px;
    margin-left: auto;

    @include media-breakpoint-down(sm) {
      height: 50px;
      width: 50px;
    }
  }

  @include media-breakpoint-down(sm) {
    // height: 50px;
    // width: 70px;
    right: -15px;
  }
}

.logo-box-small {
  position: relative;
  display: flex;
  right: 0;
  z-index: 10;
  height: 30px;
  width: auto;
  padding: 5px;
  font-size: 14px;
  pointer-events: none;

  img {
    display: block;
    height: auto;
    width: 20px;
    margin-left: auto;

    @include media-breakpoint-down(sm) {
      height: auto;
      width: 20px;
    }
  }

  p {
    margin: auto 5px auto 5px;
    font-size: 12px;
    color: $white;
    text-transform: uppercase;
  }
}

img {
  object-fit: cover;
}
