<article *ngIf="article" [ngClass]="{ sidebar: isSidebar }" class="article-card style-{{ styleID }}">
  <!-- STYLE 1 -->
  <ng-container *ngIf="styleID === articleCardType.STYLE1">
    <div *ngIf="articleCard?.gameLogoUrl" class="logo-box-big">
      <img [src]="articleCard.gameLogoUrl" [alt]="articleCard.title" />
    </div>
    <p *ngIf="articleCard?.sponsorTitle" class="sponsor-title">{{ articleCard?.sponsorTitle }}</p>
    <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" class="article-block">
      <img
        withFocusPoint
        *ngIf="articleCard?.thumbnail?.urlHuge || articleCard.thumbnail?.url as img"
        [data]="articleCard?.thumbnailFocusedImages"
        [displayedAspectRatio]="{ desktop: '1:1', mobile: '16:9' }"
        [alt]="articleCard.thumbnail?.alt ? articleCard.thumbnail?.alt : ''"
        [ngClass]="{ 'article-image-special': articleCard.thumbnail?.url }"
        [displayedUrl]="img"
        loading="lazy"
      />

      <div class="article-bottom article-only-desktop">
        <div class="article-link">
          <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
          <h2 class="article-title">{{ articleCard.title }}</h2>
        </div>
        <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
      </div>
    </a>
    <ng-template #externalLink>
      <a
        [href]="externalRecommendation.url | safe: 'url'"
        [ngClass]="{ 'article-image': articleCard.thumbnail?.url }"
        [ngStyle]="{ 'background-image': articleCard.thumbnail?.url ? 'url(' + articleCard.thumbnail?.url + ')' : 'none' }"
        trImageLazyLoad
        class="article-block"
        target="_blank"
      >
        <div class="article-bottom article-only-desktop">
          <div class="article-link">
            <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
            <h2 class="article-title">{{ articleCard.title }}</h2>
          </div>
          <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
        </div>
      </a>
    </ng-template>
    <div class="article-bottom article-only-mobile">
      <a *ngIf="!externalRecommendation.url; else externalLinkMobile" [routerLink]="articleLink" class="article-link">
        <div class="article-sub-title-wrapper">
          <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
          <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
        </div>
        <h2 class="article-title">{{ articleCard.title }}</h2>
      </a>
      <ng-template #externalLinkMobile>
        <a [href]="externalRecommendation.url | safe: 'url'" class="article-link" target="_blank">
          <div class="article-sub-title-wrapper">
            <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
            <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
          </div>
          <h2 class="article-title">{{ articleCard.title }}</h2>
        </a>
      </ng-template>
    </div>
  </ng-container>

  <!-- STYLE 2 -->
  <ng-container *ngIf="styleID === articleCardType.STYLE2">
    <div *ngIf="articleCard?.gameLogoUrl" class="logo-box-big">
      <img [src]="articleCard.gameLogoUrl" [alt]="articleCard.title" />
    </div>
    <p *ngIf="articleCard?.sponsorTitle" class="sponsor-title desktop">{{ articleCard?.sponsorTitle }}</p>
    <a
      *ngIf="!externalRecommendation.url; else externalLink"
      [ngClass]="{ 'article-image': articleCard.thumbnail?.url }"
      [routerLink]="articleLink"
      class="article-block article-only-desktop"
    >
      <img
        withFocusPoint
        [data]="articleCard?.thumbnailFocusedImages"
        *ngIf="articleCard?.thumbnail?.urlHuge || articleCard.thumbnail?.url as img"
        [displayedUrl]="img"
        [displayedAspectRatio]="{ desktop: '16:9' }"
        [alt]="articleCard?.thumbnail?.alt"
        class="article-thumbnail"
        loading="lazy"
      />
      <span *ngIf="isNowHappeningTagVisible" class="article-tag black to-right">Most történt</span>
      <div class="article-bottom">
        <div class="article-link">
          <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
          <h2 class="article-title">{{ articleCard.title }}</h2>
        </div>
        <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
      </div>
    </a>

    <ng-template #externalLink>
      <a
        [href]="externalRecommendation.url | safe: 'url'"
        [ngClass]="{ 'article-image': articleCard.thumbnail?.url }"
        [ngStyle]="{ 'background-image': articleCard.thumbnail?.url ? 'url(' + articleCard.thumbnail?.url + ')' : 'none' }"
        trImageLazyLoad
        class="article-block article-only-desktop"
        target="_blank"
      >
        <span *ngIf="isNowHappeningTagVisible" class="article-tag black to-right">Most történt</span>
        <div class="article-bottom">
          <div class="article-link">
            <p *ngIf="articleCard?.sponsorTitle" class="sponsor-title mobile relative">{{ articleCard?.sponsorTitle }}</p>
            <span [ngClass]="{ isSponsored: articleCard?.sponsorTitle }" class="article-sub-title">{{
              articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle
            }}</span>
            <h2 class="article-title">{{ articleCard.title }}</h2>
          </div>
          <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
        </div>
      </a>
    </ng-template>

    <a *ngIf="!externalRecommendation.url; else externalLinkMobile" [routerLink]="articleLink" class="article-block article-only-mobile">
      <div class="article-left-image">
        <img
          withFocusPoint
          [data]="articleCard?.thumbnailFocusedImages"
          [displayedAspectRatio]="{ desktop: '16:9' }"
          *ngIf="articleCard.thumbnail?.url"
          [displayedUrl]="articleCard.thumbnail?.url"
          [alt]="articleCard?.thumbnail?.alt"
          loading="lazy"
        />
      </div>
      <div class="article-bottom">
        <div class="article-link">
          <p *ngIf="articleCard?.sponsorTitle" class="sponsor-title mobile relative">{{ articleCard?.sponsorTitle }}</p>
          <span [ngClass]="{ isSponsored: articleCard?.sponsorTitle }" class="article-sub-title">{{
            articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle
          }}</span>
          <h2 class="article-title">{{ articleCard.title }}</h2>
        </div>
        <div class="article-footer">
          <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
          <span *ngIf="isNowHappeningTagVisible" class="article-tag black to-right">Most történt</span>
        </div>
      </div>
    </a>
    <ng-template #externalLinkMobile>
      <a [href]="externalRecommendation.url | safe: 'url'" class="article-block article-only-mobile" target="_blank">
        <div [routerLink]="articleLink">
          <img *ngIf="articleCard.thumbnail?.url" [src]="articleCard.thumbnail?.url" [alt]="articleCard?.thumbnail?.alt" loading="lazy" />
        </div>
        <div class="article-bottom">
          <div class="article-link">
            <p *ngIf="articleCard?.sponsorTitle" class="sponsor-title mobile relative">{{ articleCard?.sponsorTitle }}</p>
            <span [ngClass]="{ isSponsored: articleCard?.sponsorTitle }" class="article-sub-title">{{
              articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle
            }}</span>
            <h2 class="article-title">{{ articleCard.title }}</h2>
          </div>
          <div class="article-footer">
            <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
            <span *ngIf="isNowHappeningTagVisible" class="article-tag black to-right">Most történt</span>
          </div>
        </div>
      </a>
    </ng-template>
  </ng-container>

  <!-- STYLE 3 -->
  <ng-container *ngIf="styleID === articleCardType.STYLE3">
    <div class="article-block">
      <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" class="article-link">
        <h2 class="article-title">
          <ng-container *ngIf="articleCard?.title?.length > MAX_CHAR_COUNT; else normal"> {{ articleCard?.title?.slice(0, 125) }}... </ng-container>
          <ng-template #normal>
            {{ articleCard.title }}
          </ng-template>
        </h2>
      </a>
      <ng-template #externalLink>
        <a [href]="externalRecommendation.url | safe: 'url'" class="article-link" target="_blank">
          <h2 class="article-title">
            <ng-container *ngIf="articleCard?.title?.length > MAX_CHAR_COUNT; else normal">
              {{ articleCard?.title?.slice(0, 125) }}
              ...
            </ng-container>
            <ng-template #normal>
              {{ articleCard.title }}
            </ng-template>
          </h2>
        </a>
      </ng-template>
      <div *ngIf="articleCard?.gameLogoUrl" [ngStyle]="{ background: articleCard.gameBackgroundColor }" class="logo-box-small">
        <img [src]="articleCard.gameLogoUrl" [alt]="articleCard?.title" />
        <p>{{ articleCard.gameTitle }}</p>
      </div>
    </div>
  </ng-container>

  <!-- STYLE 4 -->
  <ng-container *ngIf="styleID === articleCardType.STYLE4">
    <div class="article-block">
      <div class="article-seperator first">
        <span class="article-tag black">Rendkívüli</span>
      </div>
      <div class="article-seperator second">
        <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" class="article-link">
          <h2 class="article-title">{{ articleCard.title }}</h2>
        </a>
        <ng-template #externalLink>
          <a [href]="externalRecommendation.url | safe: 'url'" class="article-link" target="_blank">
            <h2 class="article-title">{{ articleCard.title }}</h2>
          </a>
        </ng-template>
      </div>
      <div class="article-seperator third">
        <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
      </div>
      <div class="article-seperator fourth">
        <a *ngIf="!externalRecommendation.url; else moreExternalLink" [routerLink]="articleLink" class="article-link article-more">
          További részletek
          <svg fill="none" height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 8H15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
            <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
          </svg>
        </a>
        <ng-template #moreExternalLink>
          <a [href]="externalRecommendation.url | safe: 'url'" class="article-link article-more" target="_blank">
            További részletek
            <svg fill="none" height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 8H15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
              <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
            </svg>
          </a>
        </ng-template>
      </div>
    </div>
  </ng-container>

  <!-- STYLE 5 -->
  <ng-container *ngIf="styleID === articleCardType.STYLE5">
    <div *ngIf="articleCard?.gameLogoUrl" class="logo-box-big">
      <img [src]="articleCard.gameLogoUrl" [alt]="articleCard?.title" />
    </div>
    <p *ngIf="articleCard?.sponsorTitle" class="sponsor-title">{{ articleCard?.sponsorTitle }}</p>
    <div class="article-block">
      <a
        [ngClass]="{ 'show-play-button': isLiveTagVisible }"
        [routerLink]="articleLink"
        [target]="isTargetBlank ? '_blank' : '_self'"
        class="article-link article-image"
      >
        <img
          withFocusPoint
          [data]="articleCard?.thumbnailFocusedImages"
          *ngIf="articleCard?.thumbnail?.urlHuge || articleCard.thumbnail?.url as img"
          [displayedUrl]="img"
          [displayedAspectRatio]="{ desktop: '16:9' }"
          class="article-thumbnail"
          [alt]="articleCard?.thumbnail?.alt"
          loading="lazy"
        />
        <!--<span *ngIf="isTagVisible && articleCard.tag" class="article-tag">{{ articleCard.tag.name }}</span>-->
        <span *ngIf="isLiveTagVisible" class="article-tag black to-right">Élő</span>
      </a>
      <div class="article-bottom">
        <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" [target]="isTargetBlank ? '_blank' : '_self'" class="article-link">
          <div class="article-sub-title-wrapper">
            <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
            <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
          </div>
          <h2 class="article-title">{{ articleCard.title }}</h2>
        </a>
        <ng-template #externalLink>
          <a [href]="externalRecommendation.url | safe: 'url'" class="article-link" target="_blank">
            <div class="article-sub-title-wrapper">
              <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
              <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
            </div>
            <h2 class="article-title">{{ articleCard.title }}</h2>
          </a>
        </ng-template>
      </div>
    </div>
  </ng-container>

  <!-- STYLE 6 -->
  <ng-container *ngIf="styleID === articleCardType.STYLE6">
    <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" class="article-block">
      <img
        withFocusPoint
        [data]="articleCard?.thumbnailFocusedImages"
        *ngIf="articleCard.thumbnail?.url"
        [displayedUrl]="articleCard.thumbnail?.url"
        [displayedAspectRatio]="{ desktop: '1:1' }"
        [alt]="articleCard?.thumbnail?.alt"
        class="article-left-image"
        loading="lazy"
      />
      <div class="article-bottom">
        <div class="article-link">
          <p *ngIf="articleCard?.sponsorTitle; else noSponsor" class="sponsor-title relative">
            {{ articleCard?.sponsorTitle }}
          </p>
          <ng-template #noSponsor>
            <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
          </ng-template>
          <h2 class="article-title">{{ articleCard.title }}</h2>
          <span *ngIf="articleCard.lead && isLeadVisible" class="article-sub-title">{{ articleCard.lead }}</span>
        </div>
        <div *ngIf="articleCard?.gameLogoUrl" [ngStyle]="{ background: articleCard.gameBackgroundColor }" class="logo-box-small">
          <img [src]="articleCard.gameLogoUrl" [alt]="articleCard?.title" />
          <p>{{ articleCard.gameTitle }}</p>
        </div>
      </div>
    </a>

    <ng-template #externalLink>
      <a [href]="externalRecommendation.url | safe: 'url'" class="article-block" target="_blank">
        <div class="article-left-image">
          <img *ngIf="articleCard.thumbnail?.url" [src]="articleCard.thumbnail?.url" [alt]="articleCard?.thumbnail?.alt" loading="lazy" />
        </div>
        <div class="article-bottom">
          <div class="article-link">
            <p *ngIf="articleCard?.sponsorTitle; else noSponsor" class="sponsor-title relative">
              {{ articleCard?.sponsorTitle }}
            </p>
            <ng-template #noSponsor>
              <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
            </ng-template>
            <h2 class="article-title">{{ articleCard.title }}</h2>
            <span *ngIf="articleCard.lead && isLeadVisible" class="article-sub-title">{{ articleCard.lead }}</span>
          </div>
        </div>
      </a>
    </ng-template>
  </ng-container>

  <!-- STYLE 7 -->

  <ng-container *ngIf="styleID === articleCardType.STYLE7">
    <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" class="article-block">
      <ng-container [ngTemplateOutlet]="articleContainer"></ng-container>
    </a>
    <ng-template #externalLink>
      <a [href]="externalRecommendation.url | safe: 'url'" class="article-block" target="_blank">
        <ng-container [ngTemplateOutlet]="articleContainer"></ng-container>
      </a>
    </ng-template>
    <ng-template #articleContainer>
      <div class="article-left">
        <img
          withFocusPoint
          *ngIf="articleCard.thumbnail?.url"
          [data]="articleCard?.thumbnailFocusedImages"
          [displayedAspectRatio]="{ desktop: '1:1' }"
          [displayedUrl]="articleCard.thumbnail?.url"
          [alt]="articleCard?.thumbnail?.alt"
          class="article-left-image"
          loading="lazy"
        />
      </div>
      <div class="article-bottom">
        <div class="tag-list">
          <div class="article-tag branded">Ezt ne hagyja ki!</div>
          <span class="article-sub-title">{{ articleCard.preTitle }}</span>
        </div>
        <div class="article-link">
          <h2 class="article-title">{{ articleCard.title }}</h2>
          <span class="article-excerpt mobile-hidden">{{ articleCard.excerpt }}</span>
        </div>
        <span class="article-publishdate desktop-hidden">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
      </div>
    </ng-template>
  </ng-container>

  <!-- STYLE 8 -->
  <ng-container *ngIf="styleID === articleCardType.STYLE8">
    <div class="article-block">
      <div class="left">
        <a
          *ngIf="!externalRecommendation.url; else externalImageLink"
          [ngClass]="{ 'show-play-button': isLiveTagVisible }"
          [routerLink]="articleLink"
          class="article-link"
        >
          <img
            withFocusPoint
            *ngIf="articleCard.thumbnail?.url"
            [data]="articleCard?.thumbnailFocusedImages"
            [displayedAspectRatio]="{ desktop: '1:1' }"
            [displayedUrl]="articleCard.thumbnail?.url"
            [alt]="articleCard?.thumbnail?.alt"
            loading="lazy"
          />
        </a>
        <ng-template #externalImageLink>
          <a
            [href]="externalRecommendation.url | safe: 'url'"
            [ngClass]="{ 'show-play-button': isLiveTagVisible }"
            [ngStyle]="{ 'background-image': articleCard.thumbnail?.url ? 'url(' + articleCard.thumbnail?.url + ')' : 'none' }"
            trImageLazyLoad
            class="article-link article-image"
            target="_blank"
          >
          </a>
        </ng-template>
      </div>
      <div class="right">
        <div class="article-card-content-head">
          <ng-container *ngIf="articleCard.foundationTagTitle; else noFoundationTag">
            <span class="article-sub-title">{{ articleCard.foundationTagTitle }}</span>
          </ng-container>
          <ng-template #noFoundationTag>
            <span *ngIf="articleCard.tag && isTagVisible; else preTitle" class="article-tag">{{ articleCard.tag.name }}</span>
          </ng-template>
          <ng-template #preTitle>
            <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
          </ng-template>
          <span class="article-date">{{ articleCard.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
        </div>
        <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" class="article-link">
          <h2 class="article-title">{{ articleCard.title }}</h2>
          <span class="article-sub-title">{{ articleCard.lead }}</span>
        </a>
        <ng-template #externalLink>
          <a [href]="externalRecommendation.url | safe: 'url'" class="article-link" target="_blank">
            <span class="article-sub-title">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
            <h2 class="article-title">{{ articleCard.title }}</h2>
            <span class="article-sub-title">{{ articleCard.lead }}</span>
          </a>
        </ng-template>
      </div>
      <div class="bottom-mobile">
        <span class="article-sub-title">{{ articleCard.lead }}</span>
      </div>
    </div>
  </ng-container>

  <!-- STYLE 9  Same as Style 8, but column title on the top, not date-->
  <ng-container *ngIf="styleID === articleCardType.ImageLeftWithColumnTitleArticleTitleAndLead">
    <div class="article-block">
      <div class="left">
        <a
          *ngIf="!externalRecommendation.url; else externalImageLink"
          [ngClass]="{ 'show-play-button': isLiveTagVisible }"
          [routerLink]="articleLink"
          class="article-link article-image"
        >
          <img
            withFocusPoint
            [data]="articleCard?.thumbnailFocusedImages"
            [displayedAspectRatio]="{ desktop: '1:1' }"
            *ngIf="articleCard.thumbnail?.url"
            [displayedUrl]="articleCard.thumbnail?.url"
            [alt]="articleCard?.thumbnail?.alt"
            class="article-thumbnail"
            loading="lazy"
          />
        </a>
        <ng-template #externalImageLink>
          <a
            [href]="externalRecommendation.url | safe: 'url'"
            [ngClass]="{ 'show-play-button': isLiveTagVisible }"
            class="article-link article-image"
            target="_blank"
          >
            <img
              *ngIf="articleCard.thumbnail?.url"
              [src]="articleCard.thumbnail?.url"
              [alt]="articleCard?.thumbnail?.alt"
              class="article-thumbnail"
              loading="lazy"
            />
          </a>
        </ng-template>
      </div>
      <div class="right">
        <div class="article-card-content-head">
          <!--<span class="article-tag" *ngIf="articleCard.tag">{{ articleCard.tag.name }}</span>-->
          <a [routerLink]="['/rovat', articleCard.columnSlug]">
            <span class="article-column">{{ articleCard?.preTitle ? articleCard?.preTitle : articleCard?.columnTitle }}</span>
          </a>
        </div>
        <a *ngIf="!externalRecommendation.url; else externalLink" [routerLink]="articleLink" class="article-link">
          <h2 class="article-title">{{ articleCard.title }}</h2>
          <span class="article-sub-title">{{ articleCard.lead }}</span>
        </a>
        <ng-template #externalLink>
          <a [href]="externalRecommendation.url | safe: 'url'" class="article-link" target="_blank">
            <h2 class="article-title">{{ articleCard.title }}</h2>
            <span class="article-sub-title">{{ articleCard.lead }}</span>
          </a>
        </ng-template>
      </div>
      <div class="bottom-mobile">
        <span class="article-sub-title">{{ articleCard.lead }}</span>
      </div>
    </div>
  </ng-container>

  <!-- STYLE 10  CIKK BODY AJANLO-->
  <ng-container *ngIf="styleID === articleCardType.ArticleBodyRecommendation">
    <a [routerLink]="articleLink" class="article-recommendation-block">
      <span class="border"></span>
      <h2 class="block-title">Ezt ne hagyja ki!</h2>
      <h3 class="article-title">{{ articleCard.title }} <i></i></h3>
      <span class="border"></span>
    </a>
  </ng-container>
</article>
