import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { PortalConfigService } from '../../services';
import { ArticleCard, ExternalRecommendation, FocusPointDirective, ThumbnailImage } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, NgIf, NgStyle, NgTemplateOutlet } from '@angular/common';
import { ArticleCardTypes } from '../../definitions';
import { ImageLazyLoadDirective, PublishDatePipe, SafePipe } from '@trendency/kesma-core';
import { DEFAULT_PUBLISH_DATE_FORMAT } from '../../constants';

@Component({
  selector: 'app-article-card',
  templateUrl: './article-card.component.html',
  styleUrls: ['./article-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink, FocusPointDirective, NgStyle, NgT<PERSON>plateOutlet, SafePipe, PublishDatePipe, ImageLazyLoadDirective],
})
export class ArticleCardComponent {
  @Input() styleID: number;
  @Input() isTargetBlank: boolean;
  @Input() isTagVisible: boolean;
  @Input() isSubTitleVisible: boolean;
  @Input() isNowHappeningTagVisible: boolean;
  @Input() isLiveTagVisible: boolean;
  @Input() isLeadVisible: boolean;
  @Input() isPreTitleVisible = true;
  @Input() article: ArticleCard | ExternalRecommendation;
  @Input() isSidebar: boolean;
  @Input() widthDesktop: number;

  readonly MAX_CHAR_COUNT = 127;

  readonly articleCardType = ArticleCardTypes;

  constructor(private readonly portalConfig: PortalConfigService) {}

  get externalRecommendation(): ExternalRecommendation {
    return this.article as ExternalRecommendation;
  }

  get articleCard(): ArticleCard {
    const article = this.article as ArticleCard;

    let url = `/assets/images/placeholders/${this.portalConfig.portalName.toLowerCase()}_placeholder.jpg`;
    if (article?.thumbnail?.url) {
      url = article?.thumbnail.url;
    } else if (typeof article?.thumbnail === 'string' && !!article?.thumbnail) {
      url = article?.thumbnail;
    }
    (article.thumbnail as ThumbnailImage) = {
      url,
      urlHuge: this.widthDesktop >= 8 ? article?.thumbnail?.['urlHuge'] || article?.['thumbnailUrlHuge'] : undefined,
      alt: article.thumbnail?.alt || '',
    };

    return { ...article } as ArticleCard;
  }

  get articleLink(): string | string[] {
    return this.externalRecommendation.url
      ? ['/']
      : [
          '/',
          (this.articleCard.columnSlug || this.articleCard?.category?.slug) ?? '',
          `${this.articleCard.publishYear}`,
          `${this.articleCard.publishMonth}`.padStart(2, '0'), // for safety
          this.articleCard.slug ?? '',
        ];
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
