import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Ng<PERSON><PERSON>, NgFor } from '@angular/common';

@Component({
  selector: 'app-overflow-button',
  templateUrl: './overflow-button.component.html',
  styleUrls: ['./overflow-button.component.scss'],
  imports: [NgFor, NgClass],
})
export class OverflowButtonComponent {
  @Input() items: string[];
  @Input() selectedItem: string;
  @Output() readonly itemClick = new EventEmitter<string>();

  onItemClick(item: string): void {
    this.itemClick.emit(item);
  }
}
