@use 'shared' as *;

:host {
  $size: 24px;
  $content-offset: 14px;

  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: $size;
  height: $size;
  border-radius: 50%;
  background-color: $grey-19;
  z-index: 1000;

  .dot {
    $dot-size: 4px;

    width: $dot-size;
    height: $dot-size;
    margin: 1px;
    background-color: #000;
    border-radius: 50%;
  }

  .overflow-menu {
    position: absolute;
    top: $size;
    right: -$content-offset;
    padding-top: $content-offset;
    opacity: 0;
    transform: scale(0);
    transition:
      opacity 0.2s,
      transform 0.2s;
  }

  &:hover .overflow-menu {
    opacity: 1;
    transform: initial;
  }

  .overflow-menu-content {
    padding: 10px;
    border: 1px solid $grey-19;
    border-radius: 6px;
    background-color: white;
  }

  .overflow-menu-item {
    display: block;
    // A leghosszabb szöveg szélessége
    width: 140px;
    text-align: left;
    font-size: 16px;
    border-radius: 6px;
    padding: 8px 10px;
    line-height: 24px;

    &.selected {
      color: $blue;
      background-color: rgba($blue, 0.2);
      white-space: nowrap;

      &:before {
        content: ' ';
        width: 14px;
        height: 14px;
        margin-right: 5px;
        margin-bottom: -1px;
        @include icon('icons/bluearrow.svg');
      }
    }
  }
}
