<ng-container *ngIf="!layoutImageData?.url; else link">
  <img
    [src]="layoutImageData?.fullSize"
    loading="lazy"
    [title]="layoutImageData?.caption ?? ''"
    class="block-big-image"
    [ngClass]="{ 'no-margin': layoutImageData?.cancelMargin }"
    [alt]="layoutImageData?.altText ?? ''"
  />
</ng-container>

<ng-template #link>
  <a [href]="layoutImageData?.url" target="_blank">
    <img
      [src]="layoutImageData?.fullSize"
      loading="lazy"
      [title]="layoutImageData?.caption ?? ''"
      class="block-big-image"
      [ngClass]="{ 'no-margin': layoutImageData?.cancelMargin }"
      [alt]="layoutImageData?.altText ?? ''"
    />
  </a>
</ng-template>
