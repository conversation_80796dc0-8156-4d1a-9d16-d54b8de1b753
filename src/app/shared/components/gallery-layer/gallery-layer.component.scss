@use 'shared' as *;

$arrowInBreakpoint: 1606px;

.gallery-layer {
  background: $black;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 50;
  color: $white;
  overflow: hidden;
  transform: scale(1.5);
  opacity: 0;
  pointer-events: none;
  transition: 0.5s ease all;
  transform-origin: center center;
  border-top-left-radius: 50%;

  &.opened {
    transform: scale(1);
    opacity: 1;
    pointer-events: all;
    overflow: auto;
    border-radius: 0;
    @include media-breakpoint-up(md) {
      padding-bottom: 20px;
    }
  }

  .layer-close-button {
    width: 40px;
    height: 40px;
    @include icon('icons/close.svg');
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .fullscreen-button {
    width: 40px;
    height: 40px;
    @include icon('icons/fullscreen.svg');
    position: absolute;
    top: 20px;
    right: 70px;
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;

    .button.button-fairy {
      min-width: 137px;
      @include media-breakpoint-down(sm) {
        width: calc(50% - 10px);
        min-width: 0;
      }

      &.white {
        background-color: $white;
        color: $red;
        margin-right: 20px;
      }

      .icon-heart {
        width: 22px;
        height: 22px;
        @include icon('icons/heart-red.svg');
        margin-right: 10px;
        margin-bottom: -6px;
        display: inline-block;
      }
    }
  }

  .gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-top: 20px;
    padding-bottom: 20px;
    flex-wrap: wrap;
    @include media-breakpoint-down(sm) {
      margin-bottom: 0;
      height: auto;
    }

    &.hidden {
      visibility: hidden;
    }

    &.gallery-recommendation-header {
      margin-bottom: 70px;

      .section-header-block {
        min-height: 60px;
        display: flex;
        align-items: center;
        position: relative;
        margin-bottom: 20px;
        margin-top: 5px;
        @include article-before-line();
        @include media-breakpoint-down(sm) {
          margin-top: 15px;
        }

        &:before {
          background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
        }

        .section-header-title {
          @include article-before-title-style;
          font-size: 30px;
          @include media-breakpoint-down(sm) {
            font-size: 20px;
          }
        }
      }
    }

    .gallery-header-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      min-height: 50px;
      flex-wrap: wrap;

      .back-button {
        width: 50px;
        height: 50px;
        min-width: 50px;
        background: $white;
        margin-right: 30px;
        @include icon('icons/arrow-left.svg');
        background-size: 45%;
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }

      .name {
        font-weight: 500;
        font-size: 26px;
        line-height: 26px;
        @include media-breakpoint-down(sm) {
          display: block;
          padding-right: 65px;
          font-size: 30px;
          line-height: 40px;
        }
      }
    }

    .gallery-header-right {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-wrap: wrap;
      order: 2;
      @media screen and (max-width: $arrowInBreakpoint) {
        margin-right: 135px;
      }
      @include media-breakpoint-down(sm) {
        width: 100%;
        margin-right: 0;
        justify-content: flex-start;
        padding-top: 5px;
        order: 3;
      }

      .age {
        display: inline-block;
        margin-right: 66px;
      }

      .location {
        display: inline-block;
        margin-right: 30px;
        @include media-breakpoint-down(sm) {
          margin-right: 0;
        }
      }

      .infor-row-right {
        position: relative;
        @include media-breakpoint-down(sm) {
          width: 100%;
          padding-top: 20px;
        }

        .fairy-vote-layer-wrapper {
          position: absolute;
          top: 100%;
          right: 0;
          padding-top: 5px;
          z-index: 10;
          transform: scale(1);
          transition: 0.3s ease-out all;
          transform-origin: calc(100% - 60px) top;

          &.closed {
            pointer-events: none;
            transform: scale(0);
            opacity: 0;
          }

          @include media-breakpoint-down(sm) {
            position: fixed;
            bottom: 0;
            top: initial;
            right: 0;
            width: 100%;
            transform-origin: bottom center;
            &.closed {
              transform: scale(1) translateY(100%);
            }
          }

          .fairy-vote-layer-box {
            background: $white;
            border: 1px solid $grey-19;
            box-shadow: 0px 20px 50px rgba(0, 0, 0, 0.25);
            border-radius: 6px;
            padding: 30px;
            position: relative;
            min-width: 300px;
            @include media-breakpoint-down(sm) {
              padding: 20px;
            }
            @media screen and (max-width: 350px) {
              padding: 10px;
            }

            &:after {
              content: ' ';
              width: 16px;
              height: 16px;
              position: absolute;
              top: 0;
              right: 60px;
              background: $white;
              border-top: 1px solid $grey-19;
              border-left: 1px solid $grey-19;
              display: block;
              transform: translateY(-50%) rotate(45deg);
              @include media-breakpoint-down(sm) {
                display: none;
              }
            }

            @include media-breakpoint-down(sm) {
              border-radius: 0;
              border-bottom-width: 0;
              border-left-width: 0;
              border-right-width: 0;
            }

            .fairy-vote-close-button {
              @include icon('icons/x-small.svg');
              position: absolute;
              width: 16px;
              height: 16px;
              top: 10px;
              right: 10px;
            }
          }
        }
      }

      &.gallery-header-info {
        color: $white;
        font-size: 20px;
        line-height: 21px;
        margin-left: auto;
        display: flex;
        flex-direction: row;
        align-items: center;

        @include media-breakpoint-down(sm) {
          font-size: 14px;
          margin-left: 0;
          color: $white;
          width: 100%;
          margin-top: 100px;
        }

        .modal-credit {
          line-height: 30px;
          margin-left: 16px;
          @include media-breakpoint-down(sm) {
            font-size: 14px;
            order: 1;
            margin: 0;
          }
        }

        .modal-date {
          margin-left: 16px;
          @include media-breakpoint-down(sm) {
            order: 3;
            margin-left: 10px;
          }
        }

        .modal-count {
          @include media-breakpoint-down(sm) {
            order: 2;
            margin-left: auto;
          }
        }
      }
    }
  }

  .gallery-header-middle {
    width: 100%;
    margin: 0 auto;
    text-align: center;
    margin-top: 5px;
    order: 3;
    @include media-breakpoint-down(sm) {
      order: 2;
    }

    .d-flex {
      display: flex;
      justify-content: center;
      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }

      .left-column {
        margin-right: 3px;

        .disable {
          opacity: 0;
        }
      }

      .right-column {
        align-items: center;
        display: flex;
      }
    }

    .selected-caption {
      margin-bottom: 20px;
      margin-top: 3px;
      @include media-breakpoint-down(sm) {
        margin-bottom: 5px;
      }
    }

    .arrow {
      color: white;
      padding-left: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      @include media-breakpoint-down(sm) {
        height: 40px;
        width: 30px;
        margin: 0 auto;
        padding-left: 0px;
      }

      .arrow-icon {
        width: 18px;
        height: 18px;
        color: white;
        @include icon('icons/arrow-right-white.svg');

        &.arrow-icon-down {
          transform: rotate(90deg);
        }

        &.arrow-icon-up {
          transform: rotate(-90deg);
        }
      }
    }

    app-social-row {
      justify-content: center;
      display: flex;
    }
  }

  .big-image-carousel {
    position: relative;
    margin-bottom: 20px;
    @include media-breakpoint-down(sm) {
      width: calc(100% + 30px);
      padding-top: 45px;
      margin: 10px -15px;
      &.normal-gallery {
        padding-top: 145px;
        @include media-breakpoint-down(sm) {
          padding-top: 100px;
          height: 75vh;
        }
        margin-top: -90px;
      }
      &.gallery-page {
        padding-top: 80px;
      }
    }

    @include media-breakpoint-up(md) {
      height: 70vh;
    }

    &.fullscreen {
      height: 100vh;
    }

    &.gallery-page {
      height: 100vh;
    }

    .arrow {
      position: absolute;
      top: 0;
      height: 100%;
      width: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      @include media-breakpoint-down(sm) {
        height: 40px;
        width: 30px;
      }

      .arrow-icon {
        width: 30px;
        height: 30px;
      }

      &.arrow-prev {
        left: -1px;
        background: linear-gradient(270deg, rgba($black, 0) 0%, rgba($black, 0.7) 100%);

        .arrow-icon {
          @include icon('icons/arrow-left-narrow-white.svg');
        }

        @media screen and (min-width: $arrowInBreakpoint) {
          left: -100px;
        }
        @include media-breakpoint-down(sm) {
          right: 70px;
          left: initial;
        }
      }

      &.arrow-next {
        right: -1px;
        background: linear-gradient(90deg, rgba($black, 0) 0%, rgba($black, 0.7) 100%);

        .arrow-icon {
          @include icon('icons/arrow-right-narrow-white.svg');
        }

        @media screen and (min-width: $arrowInBreakpoint) {
          right: -100px;
        }
        @include media-breakpoint-down(sm) {
          right: 20px;
        }
      }
    }

    .slide {
      .card-wrapper {
        padding: 0;
        height: calc(100vh - 300px);
        width: 100%;
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
        background-color: rgba($white, 0.1);
        @include media-breakpoint-down(sm) {
          height: calc(100vh - 250px);
        }

        &.fullscreen {
          height: calc(100vh - 100px);
        }
      }
    }

    .recommendation {
      .recommendations {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        @include media-breakpoint-down(sm) {
          margin-top: 0;
        }

        .gallery-card-component {
          width: 48%;
          margin-bottom: 40px;
          height: 315px;
          @include media-breakpoint-down(sm) {
            margin-bottom: 20px;
            width: 100%;
            background-size: cover;
            height: 56vw;
          }
        }
      }
    }
  }

  .thumbnail-carousel {
    position: relative;
    width: calc(100% + 40px);
    margin-left: -20px;
    margin-right: -20px;
    // margin-top: -150px;
    transition: all 300ms;
    opacity: 1;
    @include media-breakpoint-down(sm) {
      margin-bottom: 30px;
      margin-top: 0px;
    }

    &.disable {
      // display: none;
      opacity: 0;
    }

    .arrow {
      position: absolute;
      top: 0;
      height: 100%;
      width: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      @include media-breakpoint-down(sm) {
        width: 60px;
      }

      .arrow-icon {
        width: 15px;
        height: 15px;
        display: block;
      }

      &.arrow-prev {
        left: 0;
        background: linear-gradient(270deg, rgba($black, 0) 0%, rgba($black, 0.7) 100%);
        padding-right: 20px;
        padding-left: 28px;

        .arrow-icon {
          @include icon('icons/arrow-left-white.svg');
        }

        @media screen and (min-width: $arrowInBreakpoint) {
          left: -50px;
        }
        @include media-breakpoint-down(sm) {
          background: linear-gradient(270deg, rgba($white, 0) 0%, rgba($white, 0.7) 100%);
          padding-left: 0;
          .arrow-icon {
            @include icon('icons/arrow-left.svg');
          }
        }
      }

      &.arrow-next {
        right: 0;
        background: linear-gradient(90deg, rgba($black, 0) 0%, rgba($black, 0.7) 100%);
        padding-right: 28px;
        padding-left: 20px;

        .arrow-icon {
          @include icon('icons/arrow-right-white.svg');
        }

        @media screen and (min-width: $arrowInBreakpoint) {
          right: -50px;
        }

        @include media-breakpoint-down(sm) {
          background: linear-gradient(90deg, rgba($white, 0) 0%, rgba($white, 0.7) 100%);
          padding-right: 0;
          .arrow-icon {
            @include icon('icons/arrow-right.svg');
          }
        }
      }
    }

    .slide {
      padding: 0 20px;
      cursor: pointer;
      @include media-breakpoint-down(sm) {
        padding: 0 10px 0 0;
      }

      .card-wrapper {
        width: 100%;
        //@include imgRatio(200%, 112%);
        background-position: center 40%;
        background-size: cover;
        height: 122px;
        @include media-breakpoint-down(sm) {
          width: 150px;
          height: 198px;
        }
      }
    }
  }
}

.recommendation {
  padding: 30px;
  position: relative;

  @include media-breakpoint-down(sm) {
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0;
  }

  @include media-breakpoint-up(md) {
    padding: 0;
    height: 100%;
  }

  .recommendation-top {
    width: calc(100% - 330px);
    // position: absolute;
    // left: 100px;
    margin: auto;

    @include media-breakpoint-down(md) {
      width: calc(100% - 20px);
    }
    @include media-breakpoint-up(md) {
      margin-bottom: 30px;
    }

    .recommendation-title {
      color: $white;
      text-transform: uppercase;
      font-size: 30px;
      line-height: 36px;
      font-family: $font-secondary;
      position: relative;
      z-index: 9999;
      @include article-before-line();
      padding-left: 20px;
    }
  }

  .recommendations-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 100%;

    @media (max-width: 1300px) {
      padding-top: 50px;
      height: 100%;
      display: flex;
    }
    @include media-breakpoint-down(sm) {
      // height: 100%;
      overflow-y: auto;
    }
    @media only screen and (min-device-width: 280px) and (max-device-width: 840px) and (orientation: landscape) {
      height: 100%;
    }
  }

  .recommendations {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    margin: 0 auto;
    flex-wrap: wrap;
    // width: 850px;
    width: calc(100% - 330px);
    margin: auto;

    @include media-breakpoint-down(md) {
      width: 100%;
      flex-direction: column;
    }

    .gallery-recommendation-card {
      margin-top: 12px;
      margin-bottom: 12px;

      position: relative;
      overflow: hidden;
      background-repeat: no-repeat;
      background-size: cover;

      @include media-breakpoint-up(md) {
        // width: 400px;

        width: 450px;
        height: 253px;
        // height: 225px;
        // padding-top: 0;
        // max-width: calc(50% - 91px);
      }

      @include media-breakpoint-down(md) {
        max-width: 390px;
        width: 100vw;
        height: 197px;
      }

      // @include media-breakpoint-down(xs) {
      //   width: calc(100% - 30px);
      //   max-width: none;
      //   //height: calc(56vw - 30px);
      //   height: 153px;
      //   margin-bottom: 5px;
      // }

      @media only screen and (min-device-width: 280px) and (max-device-width: 840px) and (orientation: landscape) {
        height: 100px;
      }

      .recommendation-card-overlay {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: $black;
        opacity: 0.55;
      }

      .recommendation-card-content {
        padding: 20px;
        height: 100%;
        width: 100%;
        z-index: 2;
        position: relative;

        @media (max-width: 1300px) {
          padding: 10px;
        }
        @include media-breakpoint-down(sm) {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }

        .grc-date {
          color: $white;
          font-size: 12px;
          font-weight: 500;
          line-height: 35px;
        }

        .grc-title {
          color: $white;
          font-size: 24px;
          line-height: 32px;
          font-family: $font-secondary;

          @media (max-width: 1300px) {
            font-size: 18px;
            line-height: 24px;
          }
        }

        .grc-text {
          color: $white;
          font-size: 16px;
          font-weight: 300;
          font-style: italic;
          letter-spacing: normal;
          line-height: 150%;
          text-align: left;
          @include media-breakpoint-down(sm) {
            font-size: 12px;
            margin-top: 4px;
          }
        }
      }
    }
  }
}
