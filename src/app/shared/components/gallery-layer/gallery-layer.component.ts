import { Location, NgClass, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { of, Subject } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';
import { FullscreenDirective, ImageLazyLoadDirective, PublishDatePipe, UtilService } from '@trendency/kesma-core';
import { GalleryCardComponent } from '../gallery-card/gallery-card.component';
import { FairyVoteLayersComponent } from '../fairy-vote-layers/fairy-vote-layers.component';
import { SocialRowComponent } from '../social-row/social-row.component';
import { ApiResult, GalleryData, GalleryImage, GalleryRecommendationData } from '@trendency/kesma-ui';
import { List, MainFairyInterface } from '../../definitions';
import { GalleryApiService } from '../../services';
import { ApiResponseMeta } from '@trendency/kesma-ui/lib/definitions/api-result';
import { DEFAULT_PUBLISH_DATE_FORMAT } from '../../constants';

@Component({
  selector: 'app-gallery-layer',
  templateUrl: './gallery-layer.component.html',
  styleUrls: ['./gallery-layer.component.scss'],
  imports: [
    NgIf,
    NgClass,
    SocialRowComponent,
    FairyVoteLayersComponent,
    NgFor,
    NgStyle,
    GalleryCardComponent,
    PublishDatePipe,
    PublishDatePipe,
    FullscreenDirective,
    ImageLazyLoadDirective,
    FullscreenDirective,
  ],
})
export class GalleryLayerComponent implements AfterViewInit, OnDestroy, OnInit {
  selectedItem: GalleryImage;
  actualElemId = 0;
  tnActualElemId = 0;
  tnStep = 5;
  isVoteLayerOpened = false;
  isSocialOpen = false;
  isFullscreen = false;
  @Input() galleryItems: List<GalleryImage> = [];
  @Input() personData: MainFairyInterface;
  @Input() gallery: GalleryData;
  @Input() isFairy = false;
  @Input() isOpened = false;
  @Input() recommendations: GalleryRecommendationData[] = [];
  @Input() showBack = true;
  @Output() galleryClose = new EventEmitter<boolean>();
  private readonly unsubscribe$ = new Subject<void>();

  constructor(
    private readonly location: Location,
    private readonly router: Router,
    private readonly utilsService: UtilService,
    private readonly cd: ChangeDetectorRef,
    private readonly galleryApiService: GalleryApiService
  ) {}

  @Input() set itemIndex(value: number) {
    this.actualElemId = value ?? 0;
    this.tnActualElemId = value ?? 0;
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    this.handleEvent(event);
  }

  ngOnInit(): void {
    if (!this.recommendations?.length) {
      this.galleryApiService
        .getGalleryRecommendations(this.gallery?.slug)
        .pipe(
          catchError(() =>
            of({
              meta: { dataCount: 0 } as ApiResponseMeta,
              data: [],
            })
          )
        )
        .pipe(takeUntil(this.unsubscribe$))

        .subscribe((recommendedData: ApiResult<GalleryRecommendationData[]>): void => {
          this.recommendations = recommendedData?.data;
        });
    }
  }

  ngAfterViewInit(): void {
    this.setCurrentItem();
    if (this.utilsService.isBrowser()) {
      this.initSlick();
    }

    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onCloseClick(): void {
    this.isFullscreen = false;
    this.galleryClose.emit(true);
  }

  onClickGalleryRecommendation(): void {
    this.onCloseClick();
  }

  onMainSlickClick(event: MouseEvent): void {
    if (this.isFairy) {
      return;
    }
    const srcElement: HTMLImageElement = event.target as HTMLImageElement;
    const slickIndex = parseInt(srcElement.dataset?.['index'] ?? '0', 10);

    if (!isNaN(slickIndex)) {
      // $(this.carousel.nativeElement).slick('slickGoTo', slickIndex);
    } else if (srcElement?.dataset?.['routerlink']) {
      const newRoute = srcElement.getAttribute('data-routerlink')?.split(',');
      this.router.navigate(newRoute ?? []);
    }
  }

  onThumbnailSlickClick(): void {
    // $(this.carousel.nativeElement).slick('slickGoTo', index);
  }

  onNextClick(): void {
    // $(this.carousel.nativeElement).slick('slickNext');
  }

  onPervClick(): void {
    // $(this.carousel.nativeElement).slick('slickPrev');
  }

  goto(itemIndex: number): void {
    this.actualElemId = itemIndex;
    // $(this.carousel.nativeElement).slick('slickGoTo', this.actualElemId);
  }

  onThumbnailNextClick(): void {
    // $(this.thubmnailCarousel.nativeElement).slick('slickNext');
  }

  onThumbnailPrevClick(): void {
    // $(this.thubmnailCarousel.nativeElement).slick('slickPrev');
  }

  tnGoto(itemIndex: number): void {
    this.tnActualElemId = itemIndex;
    // $(this.thubmnailCarousel.nativeElement).slick('slickGoTo', this.tnActualElemId);
  }

  onToggleVoteLayer(): void {
    this.isVoteLayerOpened = !this.isVoteLayerOpened;
  }

  onCloseVoteLayer(): void {
    this.isVoteLayerOpened = false;
  }

  onSocialOpen(): void {
    this.isSocialOpen = !this.isSocialOpen;
  }

  onBackButtonClick(): void {
    this.location.back();
  }

  onToggleFullscreen(fullScreenDirective: FullscreenDirective): void {
    this.isFullscreen = !this.isFullscreen;
    fullScreenDirective.toggleFullScreen();
  }

  private handleEvent(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowLeft':
        this.onPervClick();
        break;
      case 'ArrowRight':
        this.onNextClick();
        break;
      case 'Escape':
        this.onCloseClick();
        break;
      default:
        return;
    }
    event.preventDefault();
    event.stopPropagation();
  }

  private setCurrentItem(): void {
    this.selectedItem = this.galleryItems[this.actualElemId];
  }

  private initSlick(): void {
    // $(this.carousel.nativeElement)
    //   .slick({
    //     dots: false,
    //     autoplay: false,
    //     lazyLoad: 'ondemand',
    //     centerMode: false,
    //     slidesToShow: 1,
    //     slidesToScroll: 1, // not working with centermode true!
    //     speed: 1500,
    //     autoplaySpeed: 3000,
    //     variableWidth: false,
    //     arrows: false,
    //     infinite: true,
    //     responsive: [
    //       {
    //         variableWidth: false,
    //         breakpoint: 1024,
    //         settings: {
    //           slidesToScroll: 1,
    //           slidesToShow: 1,
    //         },
    //       },
    //     ],
    //   })
    //   .on('afterChange', (event: any, slick: any, currentSlide: number, nextSlide: number) => {
    //     this.actualElemId = currentSlide;
    //     this.setCurrentItem();
    //   });
    // $(this.thubmnailCarousel.nativeElement)
    //   .slick({
    //     dots: false,
    //     autoplay: false,
    //     lazyLoad: 'ondemand',
    //     centerMode: false,
    //     slidesToShow: this.tnStep,
    //     slidesToScroll: this.tnStep, // not working with centermode true!
    //     speed: 1500,
    //     autoplaySpeed: 3000,
    //     variableWidth: false,
    //     arrows: false,
    //     infinite: true,
    //     responsive: [
    //       {
    //         breakpoint: 1024,
    //         settings: {
    //           slidesToScroll: 4,
    //           slidesToShow: 4,
    //         },
    //       },
    //       {
    //         breakpoint: 768,
    //         settings: {
    //           slidesToScroll: 3,
    //           slidesToShow: 3,
    //           variableWidth: true,
    //         },
    //       },
    //     ],
    //   })
    //   .on('afterChange', (event: any, slick: any, currentSlide: number, nextSlide: number) => {
    //     this.tnActualElemId = currentSlide;
    //     this.setCurrentItem();
    //   });
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
