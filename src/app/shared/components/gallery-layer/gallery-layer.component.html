<section class="gallery-layer" [ngClass]="{ opened: isOpened }" #mainWrapper *ngIf="galleryItems" trFullscreen #fullscreen="trFullscreen">
  <button class="fullscreen-button" [ngClass]="{ 'min-icon': isFullscreen, 'full-icon': !isFullscreen }" (click)="onToggleFullscreen(fullscreen)"></button>
  <button class="layer-close-button" (click)="onCloseClick()"></button>
  <div class="wrapper">
    <div class="gallery-header" *ngIf="actualElemId + 1 <= galleryItems?.length; else galleryRecommendationHeader">
      <div class="gallery-header-left">
        <button type="button" class="button back-button" *ngIf="showBack" (click)="onBackButtonClick()"></button>
        <h1 class="name" *ngIf="isFairy && personData; else imageTitle">{{ personData?.name }}</h1>
        <ng-template #imageTitle>
          <h1 class="name">{{ gallery?.title || selectedItem?.caption }}</h1>
        </ng-template>
      </div>
      <div class="gallery-header-middle" *ngIf="!isFullscreen && actualElemId + 1 <= galleryItems?.length">
        <div class="d-flex">
          <div class="left-column center">
            <div class="selected-image-count">{{ actualElemId + 1 }} / {{ galleryItems?.length }}</div>
            <div class="selected-caption">{{ selectedItem?.caption }}</div>
          </div>
          <div class="right-column">
            <button class="arrow arrow-next" (click)="onSocialOpen()">
              <i class="arrow-icon" [ngClass]="{ 'arrow-icon-down': !isSocialOpen, 'arrow-icon-up': isSocialOpen }"></i>
            </button>
          </div>
        </div>
        <app-social-row *ngIf="isSocialOpen"></app-social-row>
      </div>
      <div class="gallery-header-right" *ngIf="isFairy">
        <span class="age">{{ personData?.name }}</span>
        <span class="location">{{ personData?.city }}</span>
        <div class="infor-row-right">
          <button class="button button-fairy white" (click)="onToggleVoteLayer()">
            <i class="icon-heart"></i>
            {{ personData?.vote_value }} pont
          </button>
          <button class="button button-fairy" (click)="onToggleVoteLayer()">Értékelem</button>
          <div class="fairy-vote-layer-wrapper" [ngClass]="{ closed: !isVoteLayerOpened }">
            <div class="fairy-vote-layer-box">
              <app-fairy-vote-layers></app-fairy-vote-layers>
              <button class="fairy-vote-close-button" (click)="onCloseVoteLayer()"></button>
            </div>
          </div>
        </div>
      </div>

      <div class="gallery-header-right gallery-header-info" *ngIf="!isFairy">
        <div class="modal-count">{{ galleryItems?.length }} fotó</div>
        <div class="modal-date">{{ selectedItem?.creationDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</div>
        <div class="modal-date" *ngIf="selectedItem?.source">Forrás: {{ selectedItem?.source }}</div>
        <div class="modal-credit" *ngIf="selectedItem?.photographer; else galleryPhotographer">Fotó: {{ selectedItem?.photographer }}</div>
        <ng-template #galleryPhotographer>
          <div class="modal-credit" *ngIf="gallery?.photographer">Fotók: {{ gallery.photographer }}</div>
        </ng-template>
      </div>
    </div>

    <div
      class="big-image-carousel"
      [ngClass]="{ 'normal-gallery': !isFairy, 'gallery-page': actualElemId + 1 > galleryItems?.length, fullscreen: isFullscreen }"
    >
      <div #carousel class="wrapper centered-carousel-obj" (click)="onMainSlickClick($event)">
        <div *ngFor="let item of galleryItems; let i = index">
          <div>
            <div class="slide">
              <!-- use the main click event from wrapper element for routing (only this solution works with clones) -->
              <!-- <div class="card-wrapper" appImageLazyLoad
                [ngStyle]="{ 'background-image': item?.url?.fullSize ? 'url(\'' + item?.url?.fullSize + '\')' : 'none' }"
                [attr.data-routerlink]="['/', 'example']"
                >
                <div class="link-card"></div>
                <button class="position-button" [attr.data-index]="i"></button>
              </div> -->
              <div
                class="card-wrapper"
                trImageLazyLoad
                [ngStyle]="{ 'background-image': item?.url?.fullSize ? 'url(\'' + item?.url?.fullSize + '\')' : 'none' }"
                [ngClass]="{ fullscreen: isFullscreen }"
              >
                <div class="link-card"></div>
                <button class="position-button"></button>
              </div>
            </div>
          </div>
        </div>
        <div class="slide recommendation" *ngIf="recommendations.length > 0">
          <div class="recommendations-container">
            <div class="recommendations">
              <ng-container *ngIf="recommendations?.length">
                <app-gallery-card
                  *ngFor="let galleryCard of recommendations"
                  [recommended]="galleryCard"
                  class="gallery-card-component"
                  (click)="onClickGalleryRecommendation()"
                  [cardStyle]="2"
                ></app-gallery-card>
              </ng-container>
            </div>
          </div>
        </div>
      </div>

      <div class="arrows-container">
        <!-- custom arrows: workaround of infinite step bug (diff animation) -->
        <button class="arrow arrow-prev" (click)="onPervClick()"><i class="arrow-icon"></i></button>
        <button class="arrow arrow-next" (click)="onNextClick()"><i class="arrow-icon"></i></button>
      </div>
    </div>

    <div class="thumbnail-carousel" [ngClass]="{ disable: isFullscreen || actualElemId + 1 > galleryItems?.length }">
      <div #thubmnailCarousel class="wrapper centered-carousel-obj">
        <div *ngFor="let item of galleryItems; let i = index" (click)="onThumbnailSlickClick()">
          <div>
            <div class="slide">
              <!-- use the main click event from wrapper element for routing (only this solution works with clones) -->
              <!-- <div appImageLazyLoad
                [ngStyle]="{ 'background-image': item?.url?.thumbnail ? 'url(\'' + item?.url?.fullSize + '\')' : 'none' }"
                class="card-wrapper" [attr.data-index]="i">
                <div class="link-card" [attr.data-index]="i"></div>
                <button class="position-button" [attr.data-index]="i"></button>
              </div> -->
              <div
                trImageLazyLoad
                [ngStyle]="{ 'background-image': item?.url?.thumbnail ? 'url(\'' + item?.url?.fullSize + '\')' : 'none' }"
                class="card-wrapper"
              >
                <div class="link-card"></div>
                <button class="position-button"></button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="arrows-container">
        <!-- custom arrows: workaround of infinite step bug (diff animation) -->
        <button class="arrow arrow-prev" (click)="onThumbnailPrevClick()"><i class="arrow-icon"></i></button>
        <button class="arrow arrow-next" (click)="onThumbnailNextClick()"><i class="arrow-icon"></i></button>
      </div>
    </div>
  </div>
</section>

<ng-template #galleryRecommendationHeader>
  <div class="gallery-header gallery-recommendation-header">
    <div class="section-header-block">
      <h2 class="section-header-title">Galériák</h2>
    </div>
  </div>
</ng-template>
