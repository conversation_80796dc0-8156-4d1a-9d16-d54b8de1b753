import { Component } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Api<PERSON><PERSON>ult, BackendExternalFeedData, BrandingBoxArticle, NewsBrandingBoxComponent as KesmaNewsBrandingBoxComponent } from '@trendency/kesma-ui';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { map } from 'rxjs/operators';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-news-branding-box',
  imports: [NgIf, AsyncPipe, KesmaNewsBrandingBoxComponent],
  templateUrl: './news-branding-box.component.html',
  styleUrl: './news-branding-box.component.scss',
})
export class NewsBrandingBoxComponent {
  private readonly dataSubject = new BehaviorSubject<BrandingBoxArticle[]>([]);
  private dataRequested = false;

  constructor(private readonly reqService: ReqService) {}

  public get data$(): Observable<BrandingBoxArticle[]> {
    if (!this.dataRequested) {
      this.dataRequested = true;
      this.fetchExternalFeedData('https://hirado.hu/mobil/export/hirado/varmegyeihirek.xml');
    }
    return this.dataSubject;
  }

  private fetchExternalFeedData(externalUrl: string): void {
    this.reqService
      .get<ApiResult<BackendExternalFeedData>>('external-rss-feed', {
        params: {
          url: externalUrl,
        },
        headers: {
          portal: 'magyar_nemzet',
        },
      } as IHttpOptions)
      .pipe(
        map(({ data: { items } }): BrandingBoxArticle[] =>
          items
            ? items
                .map(
                  (item) =>
                    ({
                      ...item,
                      lead: item?.description,
                      url: item?.link,
                    }) as unknown as BrandingBoxArticle
                )
                .slice(0, 7)
            : []
        )
      )
      .subscribe((data) => this.dataSubject.next(data));
  }
}
