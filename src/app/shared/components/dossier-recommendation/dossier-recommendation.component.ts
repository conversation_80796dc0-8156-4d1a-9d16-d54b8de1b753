import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { RouterLink } from '@angular/router';
import { As<PERSON><PERSON>ip<PERSON>, NgFor, NgIf } from '@angular/common';
import { ArticleLinkPipe, BasicDossier, BasicDossierArticle, DossierArticle, SubsequentDossier, subsequentDossierToBasicDossier } from '@trendency/kesma-ui';
import { DossierService } from '../../services';

const ARTICLES_IN_DOSSIER = 5;

@Component({
  selector: 'app-dossier-recommendation',
  templateUrl: './dossier-recommendation.component.html',
  styleUrls: ['./dossier-recommendation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink, AsyncPipe, ArticleLinkPipe],
})
export class DossierRecommendationComponent {
  /**
   * The dossier to display
   * @param value The BasicDossier to display
   */
  @Input()
  set dossier(value: BasicDossier) {
    this.dossierData$.next(value);
  }

  /**
   * The subsequent dossier to display
   * @param value The SubsequentDossier to display. This will be converted to a BasicDossier.
   */
  @Input()
  set subsequentDossier(value: SubsequentDossier) {
    const dossier = subsequentDossierToBasicDossier(value);
    if (dossier) {
      this.dossierData$.next(dossier as any);
    }
  }

  /**
   * The ID of the article to exclude from the dossier. Usually this is the current article.
   */
  @Input() excludedArticleId?: string;

  readonly dossierData$ = new BehaviorSubject<BasicDossier>({
    title: 'Dosszié cím',
    slug: 'dosszie-slug',
    thumbnailUrl: undefined,
    articles: [
      {
        title: 'Cikk 1',
        slug: 'cikk-1',
        columnSlug: 'rovat-1',
        publishDate: '',
      },
      {
        title: 'Cikk 2',
        slug: 'cikk-2',
        columnSlug: 'rovat-2',
        publishDate: '',
      },
    ],
  });

  dossierArticles$: Observable<any[]> = this.dossierData$.asObservable().pipe(
    switchMap((dossier) =>
      dossier.articles?.length
        ? of(dossier.articles) // If the dossier already has articles, use them
        : this.service
            .getDossierArticles(dossier.slug, ARTICLES_IN_DOSSIER + 1) // +1 Because IF the current article is in the dossier, we need to exclude it
            .pipe(map((response) => response.data))
    ),
    map(
      (response) =>
        this.excludeArticle<BasicDossierArticle | DossierArticle>(response) // Exclude the current article
          .slice(0, ARTICLES_IN_DOSSIER) // If the current article was not in the dossier, we have one extra article here.
    )
  );

  constructor(private readonly service: DossierService) {}

  private excludeArticle<T>(articles: T[]): T[] {
    return articles.filter(
      (article) =>
        (
          article as BasicDossierArticle & {
            id: string;
          }
        ).id !== this.excludedArticleId
    );
  }
}
