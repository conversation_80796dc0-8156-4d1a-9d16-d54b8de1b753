<div class="dossier-recommendation" *ngIf="dossierData$ | async as dossierData">
  <h4 class="rec-title">{{ dossierData.title }}</h4>
  <ng-container *ngIf="dossierArticles$ | async as dossierArticles">
    <ul class="rec-list" *ngIf="dossierArticles.length">
      <li *ngFor="let article of dossierArticles">
        <a class="rec-link" [routerLink]="article | articleLink"> {{ article?.title }} </a>
      </li>
    </ul>
  </ng-container>
  <a class="rec-more" [routerLink]="['/', 'dosszie', dossierData.slug]">A dosszié további cikkei</a>
</div>
