@use 'shared' as *;

.dossier-recommendation {
  background: $grey-22;
  padding: 23px;
  @include media-breakpoint-down(sm) {
    width: calc(100% + 30px);
    margin-left: -15px;
    margin-right: -15px;
  }

  .rec-title {
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    color: $black;
    padding-left: 40px;
    position: relative;

    &:before {
      content: ' ';
      display: inline-block;
      @include icon('icons/dossier.svg');
      width: 27px;
      height: 21px;
      margin-right: 13px;
      position: absolute;
      top: 3px;
      left: 0;
    }
  }

  .rec-list {
    padding: 0;
    margin: 20px 0;

    li {
      list-style-type: none;
      position: relative;
      margin: 5px 0;
      padding-left: 20px;

      &:before {
        position: absolute;
        width: 6px;
        height: 6px;
        top: 12px;
        left: 0;
        content: ' ';
        background-color: $blue;
        border-radius: 50%;
      }

      .rec-link {
        font-size: 20px;
        line-height: 30px;
        color: $black;
      }
    }
  }

  .rec-more {
    color: $blue;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    @include media-breakpoint-down(md) {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    &:after {
      content: ' ';
      display: inline-block;
      @include icon('icons/link-arrow.svg');
      width: 14px;
      height: 14px;
      margin-bottom: -2px;
      margin-left: 15px;
    }
  }
}
