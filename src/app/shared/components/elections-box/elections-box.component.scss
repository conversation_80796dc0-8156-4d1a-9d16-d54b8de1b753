@use 'shared' as *;

$elections-box-breakpoint-xxl: 1399.98px;
$elections-box-breakpoint-xl: 1199.98px;
$elections-box-breakpoint-lg: 991.98px;
$elections-box-breakpoint-md: 767.98px;

@mixin elections-xl() {
  flex-direction: column;
  align-items: flex-start;

  &.HEADER {
    padding: 20px;
    gap: 4px;

    .elections-title {
      letter-spacing: initial;
      padding: 0;
      font-size: 18px;
      line-height: 22px;
    }

    .elections-description {
      font-size: 10px;
      border-left: 0;
      padding: 0;
    }
  }
}

@mixin elections-lg() {
  flex-direction: column;
  align-items: flex-start;

  .elections-button {
    font-size: 9px;
    line-height: 16px;

    .icon {
      display: none;
    }
  }

  .elections-countdown {
    margin-top: 30px;

    &-divider {
      padding-top: 10px;

      &:first-child {
        display: none;
      }
    }

    &-circle {
      width: 60px;
      height: 60px;

      &-content {
        gap: 1px;

        &-unit {
          font-size: 14px;
          line-height: 14px;
        }

        &-value {
          font-size: 24px;
          line-height: 24px;
        }
      }
    }
  }

  &.DIVERTER,
  &.COUNTDOWN {
    background-image: url('/assets/images/elections-flag-v2-mobile.svg'), $brand-elections-bg-radial;

    .elections-title {
      max-width: 65%;
      font-size: 20px;
    }

    .elections-description {
      font-size: 12px;
      line-height: 20px;
      padding: 0;

      br:first-child {
        display: initial;
      }
    }
  }

  &.COUNTDOWN.counting {
    background-image: url('/assets/images/elections-flag-v3-mobile.svg'), $brand-elections-bg-radial;
  }
}

@mixin elections-md() {
  background-image: url('/assets/images/elections-flag-mobile.svg'), $brand-elections-bg-radial;

  &.DIVERTER {
    padding: 28px 0 0 25px;
  }

  &.COUNTDOWN {
    padding: 28px 0 0 25px;

    &.counting {
      padding: 48px 0 0 46px;

      .elections-description {
        display: none;
      }

      .elections-countdown {
        gap: 7px;
      }
    }
  }
}

:host {
  display: block;
  width: 100%;
}

.elections {
  display: flex;
  align-items: center;
  flex-direction: row;
  color: $white;
  background-image: url('/assets/images/elections-flag.svg'), $brand-elections-bg-radial;
  background-position:
    100% 0,
    0 0;
  background-repeat: no-repeat;
  font-family: $font-inter;

  &-title {
    font-size: 30px;
    font-weight: 700;
    line-height: normal;
    text-transform: uppercase;
    color: $white;
  }

  &-description {
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
    color: $white;
  }

  &-button {
    border-radius: 200px;
    border: 1px solid $brand-elections-blue-600;
    padding: 5px 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    transition: 0.3s border-color ease;
    color: $white;

    .icon {
      width: 24px;
      height: 24px;
      @include icon('icons/icon-elections-arrow.svg');
    }
  }

  &:hover .elections-button {
    border-color: $brand-elections-blue-400;
  }

  &-countdown {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 22px;

    &-divider {
      font-size: 32px;
      font-weight: 700;
      line-height: 32px;
      text-transform: uppercase;
      padding-top: 22px;
      color: $white;
    }

    &-circle {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-image: $brand-elections-bg-linear;
      padding: 1px;

      &-content {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: $brand-elections-blue-900;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 6px;

        &-unit {
          color: $brand-elections-blue-50;
          font-size: 16px;
          line-height: 16px;
          font-weight: 400;
          text-transform: uppercase;
        }

        &-value {
          font-size: 32px;
          line-height: 32px;
          font-weight: 700;
          color: $white;
        }
      }
    }
  }

  &.HEADER {
    height: 100px;

    .elections-title {
      letter-spacing: 1.2px;
      padding: 0 16px 0 64px;
    }

    .elections-description {
      border-left: 2px solid $white;
      padding: 0 0 0 16px;
      color: $brand-elections-blue-50;
    }
  }

  &.DIVERTER,
  &.COUNTDOWN {
    height: 250px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 2vw 0 0 3vw;
    background-image: url('/assets/images/elections-flag-v2.svg'), $brand-elections-bg-radial;
    background-size: cover;

    .elections-title {
      font-size: 36px;
    }

    .elections-description {
      font-size: 16px;
      line-height: 23px;
      padding: 0 0 0 8px;

      br:first-child {
        display: none;
      }
    }
  }

  &.COUNTDOWN.counting {
    gap: 7px;

    .elections-description {
      font-size: 12px;
      line-height: normal;
      padding: 0;

      br {
        display: none;
      }
    }

    .elections-button {
      margin-top: 32px;
    }
  }

  @media (max-width: $elections-box-breakpoint-xl) {
    @include elections-xl();
  }

  @media (max-width: $elections-box-breakpoint-lg) {
    @include elections-lg();
  }

  @media (max-width: $elections-box-breakpoint-md) {
    @include elections-md();
  }

  &.xl {
    @include elections-xl();

    @media (max-width: $elections-box-breakpoint-xl) {
      @include elections-lg();
    }
  }

  &.lg {
    @include elections-lg();

    @media (min-width: $elections-box-breakpoint-lg) and (max-width: $elections-box-breakpoint-xl) {
      @include elections-md();
    }
  }

  &.md {
    @include elections-md();

    &.HEADER {
      .elections-description {
        display: none;
      }
    }

    @media (min-width: $elections-box-breakpoint-xl) and (max-width: $elections-box-breakpoint-xxl) {
      .elections-countdown {
        gap: 5px;

        &-divider {
          display: none;
        }
      }
    }

    @media (min-width: $elections-box-breakpoint-lg) and (max-width: $elections-box-breakpoint-xl) {
      padding: 10px !important;

      .elections-title {
        padding: 10px 0 0 10px;
      }

      .elections-description {
        display: none;
      }

      .elections-countdown {
        gap: 5px;

        &-divider {
          display: none;
        }
      }
    }
  }
}
