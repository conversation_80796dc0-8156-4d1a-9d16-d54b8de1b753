import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { Observable, shareReplay } from 'rxjs';
import { RouterLink } from '@angular/router';
import { AsyncPipe, NgClass, NgIf, NgTemplateOutlet } from '@angular/common';
import { CountdownService, ELECTIONS_2024_START_DATE, ElectionsBoxStyle, FormattedCountdown } from '@trendency/kesma-ui';

@Component({
  selector: 'app-elections-box',
  templateUrl: './elections-box.component.html',
  styleUrls: ['./elections-box.component.scss'],
  providers: [CountdownService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, NgTemplateOutlet, RouterLink, AsyncPipe],
})
export class ElectionsBoxComponent implements OnInit {
  @Input() styleID: ElectionsBoxStyle = ElectionsBoxStyle.HEADER;
  @Input() link = '/valasztasok-2024';
  @Input() desktopWidth = 12;

  countdown$: Observable<FormattedCountdown | undefined> = this.countdownService.countdown$.pipe(
    shareReplay({
      bufferSize: 1,
      refCount: true,
    })
  );

  ElectionsBoxStyle = ElectionsBoxStyle;

  constructor(private readonly countdownService: CountdownService) {}

  isLinkExternal(link: string): boolean {
    return link.startsWith('http://') || link.startsWith('https://');
  }

  ngOnInit(): void {
    this.setCountdown();
  }

  private setCountdown(): void {
    if (this.styleID !== ElectionsBoxStyle.COUNTDOWN) {
      return;
    }

    this.countdownService.setCountdown(ELECTIONS_2024_START_DATE);
  }
}
