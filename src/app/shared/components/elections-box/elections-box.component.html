<ng-container *ngIf="styleID === ElectionsBoxStyle.COUNTDOWN && (countdown$ | async) as countdown; else linkTemplate">
  <div
    *ngIf="countdown.value; else linkTemplate"
    [ngClass]="{ xl: desktopWidth < 12, lg: desktopWidth < 9, md: desktopWidth < 6 }"
    class="elections {{ ElectionsBoxStyle[styleID] }} counting"
  >
    <ng-container *ngTemplateOutlet="electionsBox"></ng-container>
  </div>
</ng-container>

<ng-template #linkTemplate>
  <a
    *ngIf="isLinkExternal(link); else internalLink"
    [href]="link"
    [ngClass]="{ xl: desktopWidth < 12, lg: desktopWidth < 9, md: desktopWidth < 6 }"
    class="elections {{ ElectionsBoxStyle[styleID] }}"
    target="_blank"
  >
    <ng-container *ngTemplateOutlet="electionsBox"></ng-container>
  </a>
</ng-template>
<ng-template #internalLink>
  <a [ngClass]="{ xl: desktopWidth < 12, lg: desktopWidth < 9, md: desktopWidth < 6 }" [routerLink]="link" class="elections {{ ElectionsBoxStyle[styleID] }}">
    <ng-container *ngTemplateOutlet="electionsBox"></ng-container>
  </a>
</ng-template>

<ng-template #electionsBox>
  <span class="elections-title">Választások - 2024. június 9.</span>
  <span class="elections-description">
    Helyi önkormányzati választások, <br />Nemzetiségi önkormányzati választások <br />és Európai parlamenti választás
  </span>
  <ng-container *ngIf="styleID === ElectionsBoxStyle.DIVERTER" [ngTemplateOutlet]="electionsButton"></ng-container>
  <ng-container *ngIf="styleID === ElectionsBoxStyle.COUNTDOWN && (countdown$ | async) as countdown">
    <ng-container *ngIf="countdown.value; else electionsButton">
      <span class="elections-countdown">
        <span class="elections-countdown-divider">Még</span>
        <span class="elections-countdown-circle">
          <span class="elections-countdown-circle-content">
            <span class="elections-countdown-circle-content-unit">Nap</span>
            <span class="elections-countdown-circle-content-value">{{ countdown.duration?.days ?? 0 }}</span>
          </span>
        </span>
        <span class="elections-countdown-divider">:</span>
        <span class="elections-countdown-circle">
          <span class="elections-countdown-circle-content">
            <span class="elections-countdown-circle-content-unit">Óra</span>
            <span class="elections-countdown-circle-content-value">{{ countdown.duration?.hours ?? 0 }}</span>
          </span>
        </span>
        <span class="elections-countdown-divider">:</span>
        <span class="elections-countdown-circle">
          <span class="elections-countdown-circle-content">
            <span class="elections-countdown-circle-content-unit">Perc</span>
            <span class="elections-countdown-circle-content-value">{{ (countdown.duration?.minutes ?? 0) + 1 }}</span>
          </span>
        </span>
      </span>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #electionsButton>
  <span class="elections-button">
    Tovább a választási hírekhez
    <i class="icon icon-elections-arrow"></i>
  </span>
</ng-template>
