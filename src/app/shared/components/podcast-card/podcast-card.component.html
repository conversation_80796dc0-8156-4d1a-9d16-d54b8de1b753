<a class="article-card podcast-card" [routerLink]="buildArticleUrl(data)">
  <div class="podcast-card-thumbnail">
    <img
      withFocusPoint
      [data]="data?.thumbnailFocusedImages"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [displayedUrl]="thumbnailUrl"
      alt="Cikk képe"
      loading="lazy"
    />
  </div>
  <div *ngIf="data.columnTitle" class="podcast-card-column">
    {{ data.columnTitle }}
    <span class="publish-date">{{ data.publishDate | publishDate: DEFAULT_PUBLISH_DATE_FORMAT }}</span>
  </div>
  <div class="podcast-card-title-link">
    <h3 class="title">{{ data.title }}</h3>
  </div>
</a>
