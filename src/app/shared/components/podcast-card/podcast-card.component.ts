import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { ArticleCard, buildArticleUrl, FocusPointDirective } from '@trendency/kesma-ui';
import { PortalConfigService } from '../../services';
import { NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';
import { DEFAULT_PUBLISH_DATE_FORMAT } from '../../constants';
import { PublishDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-podcast-card',
  templateUrl: './podcast-card.component.html',
  styleUrls: ['./podcast-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, FocusPointDirective, NgIf, PublishDatePipe],
})
export class PodcastCardComponent {
  @Input() data: ArticleCard;
  readonly buildArticleUrl = buildArticleUrl;

  private readonly portalConfig = inject(PortalConfigService);

  get thumbnailUrl(): string {
    if (this.data?.thumbnail?.url) {
      return this.data?.thumbnail?.url;
    }
    if (typeof this.data?.thumbnail === 'string' && !!this.data?.thumbnail) {
      return this.data?.thumbnail;
    }
    return `/assets/images/placeholders/${this.portalConfig.portalName.toLowerCase()}_placeholder.jpg`;
  }

  protected readonly DEFAULT_PUBLISH_DATE_FORMAT = DEFAULT_PUBLISH_DATE_FORMAT;
}
