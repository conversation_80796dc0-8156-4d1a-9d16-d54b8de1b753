<a [href]="gameDetails?.url" class="game-tn" [ngStyle]="{ background: gameDetails.game?.backgroundColor }">
  <div
    class="game-left-image"
    trImageLazyLoad
    [ngStyle]="{ 'background-image': gameDetails.game?.logo ? 'url(' + gameDetails.game?.logo + ')' : 'none' }"
  ></div>
  <div class="game-right">
    <div class="game-text">
      <div class="inline" [innerHTML]="gameDetails.description | bypass: 'html'"></div>
      <i class="icon icon-arrow-right"></i>
    </div>
  </div>
</a>
