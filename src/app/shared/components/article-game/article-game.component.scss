@use 'shared' as *;

.game-tn {
  &:hover {
    .game-right {
      .game-text {
        .icon-arrow-right {
          @include icon('icons/right_arrow_circle_wh_no_bg.svg');
        }
      }
    }
  }

  margin: 20px auto;
  position: relative;
  overflow: hidden;
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 24px;
  height: 90px;

  @include media-breakpoint-down(md) {
    height: unset;
    flex-direction: column;
  }

  .game-left-image {
    height: 100%;
    width: 100%;
    max-width: 110px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center top;
    pointer-events: none;
    margin: 15px;

    @include media-breakpoint-down(md) {
      margin: unset;
      min-height: 76px;
    }
  }

  .game-right {
    align-items: center;
    color: white;

    .game-text {
      display: flex;
      align-items: center;
      font-size: 17px;

      @include media-breakpoint-down(md) {
        height: unset;
        flex-direction: column;
        align-items: center;
      }

      .inline {
        display: inline-block;
        margin-right: 20px;
        width: 92%;

        @include media-breakpoint-down(md) {
          margin: auto 10px;
          text-align: center;
          width: 100%;
        }
      }

      .icon-arrow-right {
        @include icon('icons/right_arrow_circle_wh.svg');
        color: white;
        margin-left: 10px;
        width: 24px;
        min-width: 24px;
        height: 24px;
        min-height: 24px;
        margin: -2px;
        margin-right: 15px;

        @include media-breakpoint-down(md) {
          margin: unset;
          margin-top: 10px;
          margin-bottom: 15px;
        }
      }
    }
  }
}
