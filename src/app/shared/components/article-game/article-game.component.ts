import { Component, Input } from '@angular/core';
import { NgStyle } from '@angular/common';
import { GameDetails } from '@trendency/kesma-ui';
import { BypassPipe, ImageLazyLoadDirective } from '@trendency/kesma-core';

@Component({
  selector: 'app-article-game',
  templateUrl: './article-game.component.html',
  styleUrls: ['./article-game.component.scss'],
  imports: [NgStyle, BypassPipe, ImageLazyLoadDirective],
})
export class ArticleGameComponent {
  @Input() gameDetails: GameDetails;
}
