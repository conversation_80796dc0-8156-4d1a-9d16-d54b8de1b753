@use 'shared' as *;

.quiz {
  @include media-breakpoint-down(md) {
    width: calc(100% + 30px);
    margin-left: -15px;
    margin-right: -15px;
  }

  .question {
    padding-bottom: 40px;

    .q-head {
      font-weight: bold;
      font-size: 30px;
      line-height: 50px;
      color: $black;
      padding-bottom: 26px;

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 30px;
        padding-left: 20px;
        padding-right: 20px;
        padding-bottom: 10px;
      }
    }

    .image-question {
      height: 518px;
      position: relative;
      overflow: hidden;
      @include media-breakpoint-down(md) {
        height: 73vw;
      }

      .bg,
      .image,
      .question {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-position: center;
        background-repeat: no-repeat;
      }

      .bg {
        z-index: 1;
        left: -5%;
        top: -5%;
        width: 110%;
        height: 110%;
        background-size: 1000%;
        background-position: left top;
      }

      .image {
        z-index: 2;
        background-size: contain;
        height: calc(100% + 1px);
      }

      .question {
        z-index: 3;
        background: linear-gradient(180deg, rgba(29, 29, 27, 0) 15.62%, rgba(29, 29, 27, 0.7) 100%);
        height: 224px;
        max-height: 100%;
        display: flex;
        align-items: flex-end;
        font-weight: bold;
        font-size: 30px;
        line-height: 50px;
        color: $white;
        padding: 0 36px 30px 36px;
        @include media-breakpoint-down(md) {
          font-size: 24px;
          line-height: 40px;
          padding: 0 22px 10px 22px;
        }
      }
    }

    .image-answer {
      position: relative;
      overflow: hidden;

      &.fix-height {
        height: 518px;
        width: 100%;
        height: 100%;

        @include media-breakpoint-down(md) {
          width: 100%;
          height: 100%;
        }
      }

      .image {
        z-index: 2;
        height: calc(100% + 1px);
        position: relative;
        display: block;
        width: 100%;
      }
    }

    .answer-list {
      .radio-input {
        position: absolute;
        pointer-events: none;
        opacity: 0;
      }

      .radio-input:checked + label {
        &:after {
          opacity: 1;
          transform: scale(1);
        }
      }

      .radio-label {
        display: block;
        width: 100%;
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;
        padding: 28px 196px 28px 94px;
        border-bottom: 1px solid $grey-16;
        position: relative;

        @include media-breakpoint-down(md) {
          padding: 28px 28px 28px 94px;
        }

        &:not(.wrong):not(.correct-a):not(.correct-b) {
          .extra-label {
            opacity: 0;
          }
        }

        &.wrong {
          background: #fff3f3;

          .extra-label {
            &.correct {
              opacity: 0;
              transition-duration: 0.3s;
            }

            &.wrong {
              opacity: 1;
              transition-duration: 0.3s;
            }
          }

          @include media-breakpoint-down(md) {
            &:before,
            &:after {
              opacity: 0 !important;
              transition-duration: 0.3s;
            }
          }
        }

        &.correct-a,
        &.correct-b {
          background: #e1f8ec;

          .extra-label {
            &.correct {
              opacity: 1;
              transition-duration: 0.3s;
            }

            &.wrong {
              opacity: 0;
              transition-duration: 0.3s;
            }
          }

          @include media-breakpoint-down(md) {
            &:before,
            &:after {
              opacity: 0 !important;
              transition-duration: 0.3s;
            }
          }
        }

        .extra-label {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          font-weight: bold;
          font-size: 12px;
          line-height: 24px;
          text-transform: uppercase;
          padding-right: 30px;
          @include media-breakpoint-down(md) {
            right: initial;
            left: 20px;
            padding-right: 0;
            .label-text {
              display: none;
            }
          }

          &.correct {
            color: #00be5b;
          }

          &.wrong {
            color: #ff0000;
          }

          &.correct:after {
            @include icon('icons/quiz-correct.png');
          }

          &.wrong:after {
            @include icon('icons/quiz-wrong.png');
          }

          &:after {
            width: 25px;
            height: 25px;
            display: inline-block;
            border-radius: 50%;
            content: ' ';
            margin-left: 15px;
            margin-bottom: -8px;
          }
        }

        &:before,
        &:after {
          content: ' ';
          position: absolute;
          display: block;
        }

        &:before {
          background: $white;
          border: 1px solid $black;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          left: 32px;
          top: calc(50% - 15px);
        }

        &:after {
          width: 14px;
          height: 14px;
          background: $black;
          border-radius: 50%;
          left: 40px;
          top: calc(50% - 7px);
          transform: scale(0);
          opacity: 0;
          transition: 0.3s ease all;
        }
      }
    }
  }

  .result {
    color: $white;
    display: block;
    text-align: center;
    position: relative;
    background: $blue;

    .result-text {
      width: 100%;
      background: $blue;
      z-index: 2;
      padding: 20px;

      @include media-breakpoint-down(md) {
        padding: 10px;
      }

      .correct-count {
        font-weight: bold;
        font-size: 38px;
        line-height: 40px;
        margin-bottom: 10px;
        text-transform: uppercase;
        @include media-breakpoint-down(md) {
          margin-bottom: 5px;
          font-size: 28px;
        }
      }

      .congrats-text {
        font-weight: bold;
        font-size: 30px;
        line-height: 100%;
        @include media-breakpoint-down(md) {
          font-size: 20px;
        }
      }
    }

    .shared {
      .facebook {
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        color: $white;
        padding: 14px 19px 14px 43px;
        background-color: $socialcolor-facebook;
        border-radius: 6px;
        position: relative;

        &:before {
          content: ' ';
          width: 20px;
          height: 20px;
          position: absolute;
          top: calc(50% - 10px);
          left: 14px;
          @include icon('icons/facebook.svg');
        }
      }
    }
  }
}
