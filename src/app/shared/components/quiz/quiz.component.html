<div class="quiz">
  <ng-container *ngFor="let qData of quiz.questions; let questionIndex = index">
    <div class="question">
      <div class="q-head">{{ questionIndex + 1 }}.</div>
      <div class="image-question">
        <div class="bg" trImageLazyLoad [ngStyle]="{ 'background-image': qData.image ? 'url(' + qData.image + ')' : 'none' }"></div>
        <div trImageLazyLoad class="image" [ngStyle]="{ 'background-image': qData.image ? 'url(' + qData.image + ')' : 'none' }"></div>
        <div class="question">
          {{ qData.title }}
        </div>
      </div>
      <div class="answer-list">
        <ng-container *ngFor="let answer of qData.answers; let answerIndex = index">
          <input
            class="radio-input"
            type="radio"
            (change)="onSelectAnswer(questionIndex, answerIndex)"
            [name]="'answer_' + qData.id"
            [id]="'answer_' + qData.id + '_' + answerIndex"
            [disabled]="givenAnswers[questionIndex] !== undefined"
          />
          <label
            class="radio-label"
            [for]="'answer_' + qData.id + '_' + answerIndex"
            [ngClass]="{
              wrong: !answer.isCorrect && givenAnswers[questionIndex] !== undefined && givenAnswers[questionIndex] === answerIndex,
              'correct-a': answer.isCorrect && givenAnswers[questionIndex] === answerIndex,
              'correct-b': answer.isCorrect && givenAnswers[questionIndex] !== undefined && givenAnswers[questionIndex] !== answerIndex,
            }"
          >
            {{ answer.title }}
            <span class="extra-label correct"><span class="label-text">Helyes válasz</span></span>
            <span class="extra-label wrong"><span class="label-text">Rossz válasz</span></span>
          </label>
        </ng-container>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="answeredCount === quiz.questions?.length && this.currentQuestion === quiz.questions.length">
    <div class="question">
      <div class="result">
        <div class="image-answer" [ngClass]="{ 'fix-height': rating?.thumbnailUrl || rating?.image }">
          <img *ngIf="rating?.thumbnailUrl" class="image" [src]="rating.thumbnailUrl" alt="" />
          <img *ngIf="rating?.image" class="image" [src]="rating.image" alt="" />
        </div>
        <div class="result-text">
          <div class="correct-count">eredmény:</div>
          <div class="congrats-text">
            {{ rating?.text }}
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
