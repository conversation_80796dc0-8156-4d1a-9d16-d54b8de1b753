import { Component, Input } from '@angular/core';
import { ImageLazyLoadDirective, SeoService } from '@trendency/kesma-core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { Quiz, QuizRating } from '@trendency/kesma-ui';

@Component({
  selector: 'app-quiz',
  templateUrl: './quiz.component.html',
  styleUrls: ['./quiz.component.scss'],
  imports: [NgFor, NgStyle, NgClass, NgIf, ImageLazyLoadDirective],
})
export class QuizComponent {
  @Input() quiz: Quiz;

  correctCount = 0;
  answeredCount = 0;
  givenAnswers: number[] = [];
  rating: QuizRating;

  currentQuestion = 0;

  constructor(private readonly seoService: SeoService) {}

  onSelectAnswer(question: number, answer: number): void {
    this.givenAnswers[question] = answer;

    this.checkFinished();

    this.currentQuestion += 1;
  }

  onFBShareClick(): void {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${this.seoService.currentUrl}`, 'pop', 'width=600, height=400, scrollbars=no');
  }

  private checkFinished(): void {
    this.correctCount = this.givenAnswers.filter((answer, questionIndex) => this.quiz.questions[questionIndex].answers[answer].isCorrect).length;
    this.answeredCount = this.givenAnswers.filter((answerIndex) => answerIndex !== undefined).length;

    this.getResult(this.correctCount);
  }

  private getResult(correctCount: number): void {
    const fallbackData = {
      id: '',
      thumbnailUrl: '',
      text: `${this.correctCount}/${this.answeredCount}`,
      ratingFrom: '',
      ratingTo: '',
    };

    this.rating = this.quiz.ratings.find((rating) => Number(rating.ratingFrom) <= correctCount && Number(rating?.ratingTo) >= correctCount) ?? fallbackData;
  }
}
