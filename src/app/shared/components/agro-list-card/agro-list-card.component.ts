import { Component } from '@angular/core';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { As<PERSON><PERSON>ip<PERSON>, NgFor, NgIf } from '@angular/common';
import { AgroListService } from '../../services';
import { AgroData } from '../../definitions';
import { ImageLazyLoadDirective } from '@trendency/kesma-core';

@Component({
  selector: 'app-agro-list-card',
  templateUrl: './agro-list-card.component.html',
  styleUrls: ['./agro-list-card.component.scss'],
  imports: [NgIf, NgFor, AsyncPipe, ImageLazyLoadDirective],
})
export class AgroListCardComponent {
  constructor(private readonly agroListService: AgroListService) {}

  agro$: Observable<AgroData[]> = this.agroListService.agroList$.pipe(
    filter((agroResponse) => !!agroResponse?.data?.length),
    map((agroResponse): AgroData[] => agroResponse?.data as AgroData[])
  );
}
