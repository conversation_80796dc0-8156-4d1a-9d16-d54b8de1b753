<div *ngIf="agro$ | async as agro" class="wrapper">
  <div class="top-row">
    <a href="https://agrokep.vg.hu" target="_blank"> <img trImageLazyLoad src="assets/images/agro-logo.jpeg" alt="agro-kep-logo" /></a>
    <div class="top-border"></div>
  </div>
  <a *ngFor="let agron of agro.slice(0, 4); let i = index" [href]="agron?.url" target="_blank" class="content">
    <img *ngIf="i === 0" trImageLazyLoad [src]="agron.thumbnailUrl" alt="" />
    <h2 class="title">{{ agron?.title }}</h2>
    <p class="lead">{{ agron?.shortLead }}</p>
  </a>
</div>
