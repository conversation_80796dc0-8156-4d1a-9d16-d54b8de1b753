@use 'shared' as *;

.wrapper {
  padding-bottom: 13px;
  border-bottom: 3px solid #35ab6b;

  .top-row {
    display: flex;
    margin-bottom: 13px;
    width: 100%;

    img {
      display: block;
      aspect-ratio: 71/16;
      height: 21px;
    }

    .top-border {
      width: 100%;
      border-bottom: 3px solid #35ab6b;
    }
  }

  .content {
    color: $black;

    .title {
      margin: 7px 0px;
      @include media-breakpoint-up(md) {
        font-size: 18px;
        line-height: 116.6%;
      }
    }

    .lead {
      font-size: 14px;
      line-height: 24px;
    }
  }
}
