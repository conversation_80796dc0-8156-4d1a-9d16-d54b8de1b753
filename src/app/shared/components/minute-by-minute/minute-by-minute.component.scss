@use 'shared' as *;

.minute-by-minute {
  border-top: 1px solid #eeeeee;

  .mbm-head {
    padding: 20px 0 20px 75px;
    position: relative;
    @include media-breakpoint-down(md) {
      padding-left: 0;
    }

    .time {
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      padding: 20px 0;
      font-weight: 600;
      font-size: 18px;
      line-height: 160%;
      color: $blue;
      @include media-breakpoint-down(md) {
        position: relative;
        font-size: 18px;
        line-height: 36px;
        padding-bottom: 0;
        padding-top: 10px;
      }
    }

    h5 {
      font-weight: 500;
      font-size: 20px;
      line-height: 160%;
      @include media-breakpoint-down(md) {
        position: relative;
        font-size: 18px;
        line-height: 36px;
      }
    }
  }

  .mbm-content {
    ::ng-deep {
      :first-child {
        margin-top: 0;
      }
    }
  }
}
