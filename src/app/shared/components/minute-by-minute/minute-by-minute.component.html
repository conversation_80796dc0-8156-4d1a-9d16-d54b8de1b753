<div class="minute-by-minute">
  <div class="mbm-head">
    <div class="time">{{ stringToDate(block.date) | dfnsFormat: 'HH:mm' }}</div>
    <h5>{{ block.title }}</h5>
  </div>
  <div class="mbm-content">
    <ng-container *ngFor="let element of block.body">
      <ng-container [ngSwitch]="element.type">
        <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
          <ng-container *ngFor="let wysiwygDetail of element?.details">
            <app-wysiwyg-box [html]="wysiwygDetail?.value"></app-wysiwyg-box>
          </ng-container>
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
</div>
