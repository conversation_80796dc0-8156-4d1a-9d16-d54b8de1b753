import { Component, Input } from '@angular/core';
import { WysiwygBoxComponent } from '../wysiwyg-box/wysiwyg-box.component';
import { <PERSON><PERSON><PERSON>, NgSwitch, NgSwitchCase } from '@angular/common';
import { ArticleBodyType, backendDateToDate, MinuteToMinuteBlock } from '@trendency/kesma-ui';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'app-minute-by-minute',
  templateUrl: './minute-by-minute.component.html',
  styleUrls: ['./minute-by-minute.component.scss'],
  imports: [Ng<PERSON><PERSON>, NgS<PERSON>, NgSwitchCase, WysiwygBoxComponent, DateFnsModule],
})
export class MinuteByMinuteComponent {
  readonly ArticleBodyType = ArticleBodyType;
  block: MinuteToMinuteBlock;

  @Input()
  set data(data: MinuteToMinuteBlock) {
    this.block = { ...data, date: backendDateToDate(data.date as string) ?? '' };
  }

  stringToDate(date: string | Date | undefined): Date | null {
    return backendDateToDate(String(date));
  }
}
