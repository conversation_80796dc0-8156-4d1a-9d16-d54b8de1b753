@use 'shared' as *;

.subscription-box {
  display: flex;
  margin-bottom: 50px;
  margin-top: 10px;
  @include media-breakpoint-down(md) {
    flex-direction: column;
    margin-bottom: 30px;
  }

  .left-col {
    min-width: 275px !important;
    @include icon('icons/article-subscription-icon.svg');
    background-size: cover;

    @include media-breakpoint-down(md) {
      @include icon('icons/article-subscription-icon-mobile.svg');
      @include imgRatio(335px, 150px);
    }
  }

  .right-col {
    background-color: $grey-22;
    padding: 34px;
    @include media-breakpoint-down(md) {
      display: flex;
      flex-direction: column;
      padding: 14px 25px;
      padding-bottom: 35px;
    }

    .title {
      font-size: 24px;
      font-weight: 700;
      line-height: 30px;
      margin-bottom: 5px;
      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    .text {
      font-size: 20px;
      line-height: 30px;
      margin-bottom: 30px;
      @include media-breakpoint-down(md) {
        text-align: center;
      }
    }

    .subscribe-button {
      padding: 15px 18px;
      background: $blue;
      border-radius: 6px;
      color: $white;
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      @include media-breakpoint-down(md) {
        margin: 0 auto;
      }
    }
  }
}
