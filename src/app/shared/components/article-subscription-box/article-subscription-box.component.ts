import { Component, OnInit } from '@angular/core';
import { PortalConfigService } from '../../services';
import { AnalyticsService, NEWSLETTER_COMPONENT_TYPE } from '@trendency/kesma-ui';

@Component({
  selector: 'app-article-subscription-box',
  templateUrl: './article-subscription-box.component.html',
  styleUrls: ['./article-subscription-box.component.scss'],
})
export class ArticleSubscriptionBoxComponent implements OnInit {
  portalName: string;

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly analyticsService: AnalyticsService
  ) {}

  ngOnInit(): void {
    this.portalName = this.getPortalName(this.portalConfig.portalName);
  }

  onNewsletterClicked(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.ARTICLE_END);
  }

  getPortalName(portalName: string): string {
    switch (portalName) {
      case 'BAMA':
        return 'bama.hu';
      case 'BAON':
        return 'baon.hu';
      case 'BEOL':
        return 'beol.hu';
      case 'BOON':
        return 'boon.hu';
      case 'DELMAGYAR':
        return 'delmagyar.hu';
      case 'DUOL':
        return 'duol.hu';
      case 'ERDON':
        return 'erdon.ro';
      case 'FEOL':
        return 'feol.hu';
      case 'HAON':
        return 'haon.hu';
      case 'HEOL':
        return 'heol.hu';
      case 'KEMMA':
        return 'kemma.hu';
      case 'KISALFOLD':
        return 'kisalfold.hu';
      case 'KPI':
        return 'kpi.hu';
      case 'NOOL':
        return 'nool.hu';
      case 'SONLINE':
        return 'sonline.hu';
      case 'SZEGEDMA':
        return 'szegedma.hu';
      case 'SZOLJON':
        return 'szoljon.hu';
      case 'SZON':
        return 'szon.hu';
      case 'TEOL':
        return 'teol.hu';
      case 'VAOL':
        return 'vaol.hu';
      case 'VEOL':
        return 'veol.hu';
      case 'ZAOL':
        return 'zaol.hu';
      default:
        return '';
    }
  }
}
