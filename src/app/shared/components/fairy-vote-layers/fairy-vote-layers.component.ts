import { Component, EventEmitter, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'app-fairy-vote-layers',
  templateUrl: './fairy-vote-layers.component.html',
  styleUrls: ['./fairy-vote-layers.component.scss'],
  imports: [Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgClass],
})
export class FairyVoteLayersComponent {
  @Output() openLoginPopup = new EventEmitter<void>();
  @Output() openRegistrationPopup = new EventEmitter<void>();

  step = 0;
  votes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  selectedHearth = 0;
  loggedIn = false;

  placeholderStep(): void {
    this.step++;
    if (this.step > 2) {
      this.step = 0;
    }
  }

  overHearthEvent(index: number): void {
    this.selectedHearth = index;
  }
}
