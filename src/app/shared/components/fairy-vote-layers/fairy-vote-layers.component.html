<!-- login/register to vote -->
<div class="fairy-state-login" *ngIf="step === 0 && !loggedIn">
  <i class="icon-dashed-heart"></i>
  <p>A szavazáshoz jelentkezzen be vagy regisztrá<PERSON>jon az oldalon!</p>
  <button class="button" (click)="openLoginPopup.emit()">Belépés</button>
  <button class="button" (click)="openRegistrationPopup.emit()">Regisztráció</button>
</div>

<!-- voting hearts -->
<div class="fairy-state-vote" *ngIf="step === 0 && loggedIn">
  <div class="top-row">
    <div class="top-row-title">Versenyző értékelése</div>
    <div class="top-row-info">Bejelenkezve, mint <b>John <PERSON></b></div>
  </div>
  <div class="bottom-row">
    <div class="heart-list">
      <ng-container *ngFor="let vote of votes; let i = index">
        <div class="heart-button-wrapper">
          <button class="heart-button" [ngClass]="{ empty: selectedHearth < i }" (click)="overHearthEvent(i)"></button>
          <span>{{ vote }}</span>
        </div>
      </ng-container>
    </div>
    <button class="button" (click)="placeholderStep()">Küldés</button>
  </div>
</div>

<!-- success message -->
<div class="fairy-state-success" *ngIf="step === 1"><i class="icon-check"></i> Köszönjük az értékelésed <b>John Doe</b></div>
