@use 'shared' as *;

.fairy-state-login {
  text-align: center;
  color: $black;

  .icon-dashed-heart {
    display: inline-block;
    width: 40px;
    height: 40px;
    @include icon('icons/cursor-select-heart.png');
  }

  p {
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
    font-weight: normal;
    font-size: 16px;
    line-height: 24px;
  }

  .button {
    width: 200px;
    margin-top: 10px;
  }
}

.fairy-state-vote {
  .top-row {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .top-row-title {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }

    .top-row-info {
      font-weight: normal;
      font-size: 16px;
      line-height: 24px;
    }
  }

  .bottom-row {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    width: 100%;
    @include media-breakpoint-down(sm) {
      flex-wrap: wrap;
      //justify-content: center;
    }

    .heart-list {
      display: flex;
      width: calc(100% + 20px);
      margin-left: -10px;
      margin-right: -10px;
      justify-content: space-between;
      @include media-breakpoint-down(sm) {
        width: 100%;
        margin: 12px 0;
      }

      .heart-button-wrapper {
        width: 30px;
        margin: 0 10px;
        @include media-breakpoint-down(sm) {
          margin: 0 6px;
          width: 19px;
        }

        .heart-button {
          width: 100%;
          height: 30px;
          position: relative;
          @include media-breakpoint-down(sm) {
            height: 19px;
          }

          &:after,
          &:before {
            content: ' ';
            width: 100%;
            height: 30px;
            position: absolute;
            top: 0;
            left: 0;
            transition: 0.2s ease-out;
            @include media-breakpoint-down(sm) {
              height: 19px;
            }
          }

          &:before {
            @include icon('icons/heart-empty.png');
          }

          &:after {
            @include icon('icons/heart-red.svg');
          }

          &.empty:after {
            opacity: 0;
          }

          &:not(.empty):before {
            opacity: 0;
          }
        }

        span {
          font-weight: 300;
          font-size: 14px;
          line-height: 18px;
          display: block;
          text-align: center;
          width: 100%;
          margin-top: 4px;
        }
      }
    }

    .button {
      width: 100px;
      min-width: 100px;
      margin-left: 30px;
      @include media-breakpoint-down(sm) {
        margin-left: auto;
        margin-right: auto;
      }
    }
  }
}

.fairy-state-success {
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  margin: -12px;
  @include media-breakpoint-up(md) {
    white-space: nowrap;
  }
  @include media-breakpoint-down(sm) {
    margin: 0;
  }

  .icon-check {
    width: 24px;
    height: 24px;
    @include icon('icons/icon-check-green.svg');
    margin-right: 5px;
    margin-bottom: -7px;
  }
}
