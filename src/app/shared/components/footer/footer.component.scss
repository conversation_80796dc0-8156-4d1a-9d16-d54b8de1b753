@use 'shared' as *;

footer {
  background: $grey-22;
  padding-top: 37px;

  .wrapper {
    > .row > .col-12 {
      display: flex;
      flex-wrap: wrap;
      position: relative;
    }

    .footer-info {
      width: calc(#{$logo-width} + 30px);

      .logo-wrappper {
        margin-top: -10px;
        margin-bottom: 10px;
        padding-right: 30px;
        width: calc(#{$logo-width} + 30px);
        display: flex;
        justify-content: center;
        flex-wrap: nowrap;
        @include media-breakpoint-down(sm) {
          margin-bottom: 25px;
        }

        .logo {
          height: 55px;

          display: flex;
          align-items: center;
          flex-wrap: nowrap;

          @include media-breakpoint-down(md) {
            margin-right: 0;
          }

          .logo-figure {
            flex-shrink: 0;
            margin-right: 7px;
            height: 39px;

            @include media-breakpoint-down(md) {
              height: 30px;
            }

            svg {
              width: auto;
              height: 100%;
            }

            .primary-color {
              fill: $primary-color;
            }

            .secondary-color {
              fill: $secondary-color;
            }
          }

          .text {
            font-family: $font-secondary;
            text-transform: uppercase;

            .portal-name {
              font-weight: 500;
              font-size: 30px;
              @include media-breakpoint-down(md) {
                font-size: 22.5px;
                margin-top: -2px;
              }
            }
          }
        }

        .portal-subtitle {
          font-weight: 300;
          font-size: 12px;
          display: block;
          width: calc(#{$logo-width} - 10px);
          max-width: 100%;
          text-transform: uppercase;
          text-align: left;
          @include media-breakpoint-down(md) {
            font-size: 9px;
          }
        }
      }

      .social {
        display: flex;
        justify-content: center;
        width: calc(100% - 30px);
        margin-bottom: 147px;
        @include media-breakpoint-down(md) {
          margin-bottom: 20px;
          width: auto;
          justify-content: flex-start;
          margin-left: -10px;
        }
        @include media-breakpoint-down(sm) {
          position: absolute;
          top: 0;
          right: 5px;
          width: auto;
          margin: 0;
          height: 40px;
          align-items: center;
        }

        .social-button {
          width: 30px;
          height: 30px;
          background: $black;
          border-radius: 50%;
          margin: 5px 10px;

          &.facebook {
            background-color: $socialcolor-facebook;
            @include icon('icons/facebook.svg');
            background-size: 31%;
          }

          &.instagram {
            background-color: $socialcolor-instagram;
            @include icon('icons/instagram.svg');
            background-size: 60%;
          }
        }
      }
    }

    .categories {
      display: flex;
      justify-content: space-between;
      width: calc(100% - #{$logo-width} - 30px);
      @include media-breakpoint-down(md) {
        flex-wrap: wrap;
        justify-content: flex-start;
        margin-bottom: 45px;
      }
      @include media-breakpoint-down(sm) {
        width: 100%;
      }

      .category-column {
        &:not(:last-child) {
          width: 20%;
          @include media-breakpoint-down(md) {
            width: 33.333%;
          }
          @include media-breakpoint-down(sm) {
            width: 100%;
          }
        }

        @include media-breakpoint-down(lg) {
          width: auto;
        }

        .category-header {
          width: 100%;
          margin-bottom: 16px;

          .category-main {
            font-weight: 500;
            font-size: 12px;
            line-height: 16px;
            color: $black;
          }
        }

        .category-sublist {
          @include media-breakpoint-down(sm) {
            display: none;
          }

          .sublist-elem {
            font-weight: normal;
            font-size: 12px;
            line-height: 24px;
            color: $black;

            &:hover {
              color: $blue;
            }
          }
        }
      }
    }

    .info-text {
      font-size: 12px;
      line-height: 18px;
      margin-top: 10px;
      margin-bottom: 20px;

      &.erdon {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      &.desktop-only {
        @include media-breakpoint-down(xs) {
          display: none;
        }
      }

      &.mobile-only {
        @include media-breakpoint-up(sm) {
          display: none;
        }
        @include media-breakpoint-down(xs) {
          margin-top: 0px;
          margin-bottom: 30px;
          max-width: 260px;
        }
      }
    }

    .erdon-info {
      height: 150px;
    }
  }

  .footer-elements {
    width: 100%;
    background: black;
    padding: 18px 0px;
    position: relative;

    @include media-breakpoint-down(md) {
      display: flex;
      flex-direction: column-reverse;
      justify-content: center;
    }

    .sal-pictogram {
      position: absolute;
      right: 10px;
      top: 5px;
      width: 165px;

      @include media-breakpoint-down(md) {
        position: relative;
        margin-top: 5px;
        display: flex;
        width: 100%;
        justify-content: center;
      }
    }

    .inner-wrapper {
      width: $global-wrapper-width;
      margin: 0 auto;
      max-width: calc(100% - 30px);
      display: flex;
      justify-content: center;

      @include media-breakpoint-down(md) {
        flex-wrap: wrap;
      }

      .static-links {
        cursor: pointer;
        color: white;
        display: flex;
        font-size: 14px;

        &:hover {
          text-decoration: underline;
        }

        &:after {
          margin: auto 12px;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #1eabf1;
          display: block;
          content: ' ';
          position: relative;
        }

        @include media-breakpoint-down(md) {
          font-size: 12px;
          margin: 5px 0px;

          &:after {
            margin: auto 6px;
          }
        }
      }

      .static-links:last-child {
        &:after {
          display: none;
        }
      }
    }
  }
}
.with-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 5px;
  img {
    height: 1rem;
    width: 1rem;
  }
}
