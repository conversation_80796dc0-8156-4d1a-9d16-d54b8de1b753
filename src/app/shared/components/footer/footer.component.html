<footer>
  <div class="wrapper">
    <div class="row">
      <div class="col-12">
        <div class="footer-info">
          <div class="logo-wrappper">
            <a [routerLink]="['/']" class="logo">
              <div class="logo-figure">
                <svg viewBox="0 0 35 39" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M 0,2.3000044 V 12.900024 c 0,10.3 5.7,20 15.6,26.4 V 0 C 10.3,0.1000004 5,0.8999994 0,2.3000044 Z"
                    fill="#005ca2"
                    class="primary-color"
                  />
                  <path
                    d="m 18.7,0 v 39.400024 c 9.9,-6.4 15.6,-16.1 15.6,-26.4 V 2.3000044 C 29.3,0.8999994 24.1,0.1000004 18.7,0 Z"
                    fill="#006d34"
                    class="secondary-color"
                  />
                </svg>
              </div>
              <div class="text">
                <p class="portal-name">{{ portalName }}</p>
                <p class="portal-subtitle">{{ portalSubtitle }}</p>
              </div>
            </a>
          </div>
          <div class="social">
            <a class="social-button facebook" [href]="facebookUrl"></a>
            <a class="social-button instagram" [href]="instagramUrl"></a>
          </div>
        </div>
        <div class="info-text mobile-only" [ngClass]="{ erdon: isErdon }">
          <ng-container [ngTemplateOutlet]="infoText"></ng-container>
        </div>
        <div class="categories">
          <ng-container *ngFor="let category of menu">
            <div class="category-column">
              <div class="category-header">
                @if (category.relatedType === RelatedType.COLUMN && category?.related?.sponsorship?.logo; as logo) {
                  <a
                    class="category-main with-logo"
                    [routerLink]="getLink(category)"
                    *ngIf="category.relatedType !== RelatedType.CUSTOM_URL; else externalLink"
                  >
                    <img [src]="logo" loading="lazy" />
                    {{ category.title }}
                  </a>
                } @else {
                  <a class="category-main" [routerLink]="getLink(category)" *ngIf="category.relatedType !== RelatedType.CUSTOM_URL; else externalLink">
                    {{ category.title }}
                  </a>
                }

                <ng-template #externalLink>
                  <a class="category-main" [href]="category.customUrl | safe: 'url'" [target]="category.targetBlank ? '_blank' : '_top'">
                    {{ category.title }}
                  </a>
                </ng-template>
              </div>
              <div class="category-sublist">
                <ng-container *ngFor="let elem of category.children">
                  @if (category.relatedType === RelatedType.COLUMN && category?.related?.sponsorship?.logo; as logo) {
                    <a class="sublist-elem with-logo" [routerLink]="getLink(elem)" *ngIf="elem.relatedType !== RelatedType.CUSTOM_URL; else externalItemLink">
                      <img [src]="logo" loading="lazy" />
                      {{ elem.title }}
                    </a>
                  } @else {
                    <a class="sublist-elem" [routerLink]="getLink(elem)" *ngIf="elem.relatedType !== RelatedType.CUSTOM_URL; else externalItemLink">
                      {{ elem.title }}
                    </a>
                  }
                  <ng-template #externalItemLink>
                    <a class="sublist-elem" [href]="elem.customUrl | safe: 'url'" [target]="elem.targetBlank ? '_blank' : '_top'">
                      {{ elem.title }}
                    </a>
                  </ng-template>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </div>
        <div class="info-text desktop-only" [ngClass]="{ erdon: isErdon }">
          <ng-container [ngTemplateOutlet]="infoText"></ng-container>
        </div>
      </div>
    </div>
  </div>
  <div class="footer-elements">
    <a *ngIf="isErdon" class="sal-pictogram" href="https://anpc.ro/ce-este-sal/" target="_blank">
      <img loading="lazy" src="../../../../assets/images/sal-pictograma.png" alt="sal-pictograma" />
    </a>

    <div class="inner-wrapper">
      <ng-container *ngFor="let menuItem of bottomMenu">
        <ng-container [ngSwitch]="menuItem.relatedType">
          <ng-container *ngSwitchCase="RelatedType.DROPDOWN">
            <a href="#" class="static-links">{{ menuItem.title }}</a>
          </ng-container>
          <ng-container *ngSwitchCase="RelatedType.CUSTOM_URL">
            <a [href]="menuItem.customUrl | safe: 'url'" class="static-links">{{ menuItem.title }}</a>
          </ng-container>
          <ng-container *ngSwitchDefault>
            @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship?.logo; as logo) {
              <a [routerLink]="getLink(menuItem)" class="static-links with-logo">
                <img [src]="logo" loading="lazy" />
                {{ menuItem.title }}</a
              >
            } @else {
              <a [routerLink]="getLink(menuItem)" class="static-links">{{ menuItem.title }}</a>
            }
          </ng-container>
        </ng-container>
      </ng-container>
      <a class="static-links" (click)="openCookieSettings()">Süti beállítások</a>
    </div>
  </div>
</footer>

<ng-template #infoText>
  @if (!isErdon) {
    A {{ portalName }} kiadója a Mediaworks Hungary Zrt. © Minden jog fenntartva
  } @else {
    <img class="erdon-info" alt="Erdon Tamogato" src="/assets/images/tamogato-BGA.jpg" />
  }
</ng-template>
