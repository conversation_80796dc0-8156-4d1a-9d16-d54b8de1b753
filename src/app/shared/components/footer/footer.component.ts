import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, Ng<PERSON><PERSON>, Ng<PERSON><PERSON>, Ng<PERSON><PERSON><PERSON>ase, Ng<PERSON><PERSON>Default, NgTemplateOutlet } from '@angular/common';
import { SafePipe } from '@trendency/kesma-core';
import { MenuChild, RelatedType } from '../../definitions';
import { PortalConfigService, PortalUtilsService } from '../../services';
import { getLinkFromMenuItem } from '../../utils';

// eslint-disable-next-line @typescript-eslint/naming-convention
declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [Router<PERSON>ink, NgTemplateOutlet, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgSwitch, NgSwitchCase, NgSwitchDefault, SafePipe, Ng<PERSON>lass],
})
export class FooterComponent implements OnInit {
  @Input() menu: MenuChild[] = [];
  @Input() bottomMenu: MenuChild[] = [];

  facebookUrl: string;
  instagramUrl: string;
  isErdon = false;
  portalName: string;
  portalSubtitle: string;

  constructor(
    private readonly router: Router,
    private readonly portalConfig: PortalConfigService,
    private readonly utilsService: PortalUtilsService
  ) {
    this.portalName = portalConfig.portalName;
    this.portalSubtitle = portalConfig.portalSubtitle;
    this.facebookUrl = this.utilsService.getFacebooklink(portalConfig.portalName);
    this.instagramUrl = this.utilsService.getInstagramlink(portalConfig.portalName);
    this.router.routeReuseStrategy.shouldReuseRoute = function (): boolean {
      return false;
    };
  }

  ngOnInit(): void {
    this.isPortalErdon();
  }

  RelatedType = RelatedType;
  getLink = getLinkFromMenuItem;

  openCookieSettings(): void {
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }

  isPortalErdon(): void {
    const { portalName } = this.portalConfig;

    if (portalName === 'ERDON') {
      this.isErdon = true;
    }
  }
}
