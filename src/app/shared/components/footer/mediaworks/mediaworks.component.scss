@use 'shared' as *;
/* --------------------------------- */
/* MediaWorks Global Footer - common */
/* --------------------------------- */
/* version: 2.0                      */

$fontColor: #7c7c7c;

.clearfix:after {
  clear: both;
  content: '.';
  display: block;
  font-size: 0;
  height: 0;
  visibility: hidden;
}

div.mvg_footer_mediaworks {
  width: 100%;
  font-family: 'Open Sans', Arial, Helvetica, sans-serif;
  font-size: 14px;
  color: $fontColor;

  .mwg_footer_top {
    width: 100%;

    .outer_table {
      padding-left: 20px;
      padding-right: 20px;

      width: $global-wrapper-width;
      margin: 0 auto;
      max-width: calc(100% - 30px);

      @media (min-width: 360px) {
        padding-left: 45px;
        padding-right: 45px;
      }

      @media (min-width: 576px) {
        padding-left: 20px;
        padding-right: 20px;
      }

      @media (min-width: 768px) {
        display: flex;
        display: -ms-flex;
        flex-direction: row;
        -ms-flex-direction: row;
        justify-content: center;
        align-items: center;
        padding-top: 30px;
        padding-bottom: 30px;
      }

      @media (min-width: 993px) {
        padding-left: 23px;
        padding-right: 23px;
      }

      .inner_table {
        display: flex;
        display: -ms-flex;
        flex-direction: column;
        -ms-flex-direction: column;
        justify-content: center;
        align-items: center;

        &.mw_social {
          @media (min-width: 993px) {
            justify-content: flex-end;
          }
        }
      }

      .mw_logo {
        padding-top: 30px;

        @media (min-width: 360px) {
          padding-top: 25px;
        }

        @media (min-width: 768px) {
          padding-top: 0;
          padding-right: 29px;
          width: 175px;
        }

        @media (min-width: 993px) {
          padding-right: 85px;
          width: 23%;
        }
      }

      .mw_text {
        display: block;
        text-align: center;
        padding-top: 30px;

        @media (min-width: 360px) {
          text-align: left;
          padding-top: 25px;
        }

        @media (min-width: 768px) {
          padding-top: 0;
          width: calc(100% - 175px - 150px);
          font-size: 12px;
          line-height: 14px;
        }

        @media (min-width: 993px) {
          width: 60%;
        }
      }

      .mw_social {
        flex-direction: row;
        -ms-flex-direction: row;
        text-align: center;
        padding-top: 30px;
        padding-bottom: 30px;

        @media (min-width: 360px) {
          padding-top: 25px;
          padding-bottom: 25px;
        }

        @media (min-width: 768px) {
          padding-top: 0;
          padding-bottom: 0;
          padding-left: 30px;
          width: 150px;
        }

        @media (min-width: 993px) {
          padding-left: 85px;
          width: 21%;
          margin-right: 20px;
        }

        a {
          display: inline-block;
          width: 40px;
          height: 40px;
          transition-duration: 0.3s;
          -webkit-transition-duration: 0.3s;
          -moz-transition-duration: 0.3s;
          -ms-transition-duration: 0.3s;
          -o-transition-duration: 0.3s;

          &.icon_fb {
            background: url('/assets/images/mediaworks/mwf_icon_fb.png');
            margin-right: 5px;

            &:hover {
              background: url('/assets/images/mediaworks/mwf_icon_fb_hover.png');
            }

            @media (min-width: 576px) {
            }
          }

          &.icon_in {
            background: url('/assets/images/mediaworks/mwf_icon_in.png');
            margin-left: 5px;

            &:hover {
              background: url('/assets/images/mediaworks/mwf_icon_in_hover.png');
            }

            @media (min-width: 576px) {
            }
          }
        }
      }
    }
  }

  .mwg_footer_lower {
    background: #e1e1e1 url('/assets/images/mw-logo-2024-watermark.svg') no-repeat bottom center;
    background-size: 140%;
    width: 100%;
    padding: 76px 25px 53px;

    @media (min-width: 360px) {
      padding: 76px 25px 53px;
    }

    @media (min-width: 576px) {
      padding: 76px 30px 53px;
    }

    @media (min-width: 993px) {
      padding: 76px 40px 50px;
      background-size: contain;
    }

    @media (min-width: 1200px) {
      padding: 76px calc(((100vw - 1200px) / 2) + 40px) 87px;
    }

    .domainFrame {
      column-count: 4;
      column-gap: 40px;
      width: 100%;
      column-width: 210px;
      line-height: 1.7em;

      .domainTitle {
        font-size: 20px;
        font-family: 'Open Sans', sans-serif;
        font-weight: 600;
        padding-bottom: 13px;

        @media (min-width: 1200px) {
          padding-bottom: 8px;
        }

        &:first-child {
          padding-top: 0;
        }
      }

      .externalPageLink {
        font-size: 14px;
        font-family: 'Open Sans', sans-serif;

        &:hover {
          color: #9d9e9f;
        }

        & + .domainTitle {
          margin-top: 38px;

          @media (min-width: 1200px) {
            margin-top: 48px;
          }
        }
      }

      a {
        display: block;
        color: $fontColor;
      }
    }
  }
}
