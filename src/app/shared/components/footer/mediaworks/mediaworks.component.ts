import { ChangeDetectionStrategy, Component } from '@angular/core';
import { map } from 'rxjs/operators';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgSwitch, NgS<PERSON><PERSON><PERSON>, NgSwitchDefault } from '@angular/common';
import { ApiService } from '../../../services';

@Component({
  selector: 'app-mediaworks',
  templateUrl: './mediaworks.component.html',
  styleUrls: ['./mediaworks.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, NgSwitch, NgS<PERSON>Case, NgSwitchDefault, AsyncPipe],
})
export class MediaworksComponent {
  mediaworksPortfolio$ = this.apiService.getPortfolioFooter().pipe(map((data) => data.data));

  constructor(private readonly apiService: ApiService) {}
}
