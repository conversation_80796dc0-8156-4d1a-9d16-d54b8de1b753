@use 'shared' as *;

$submenu-element-width: 220px;
$submenu-screen-width-correction: 200px;
$submenu-open-anim: 0.3s ease-out all;

$left-menu-overflow-count: 5;
$right-menu-overflow-count: 7;

@mixin autoHideMenuItems($itemCount: 7) {
  .sub-nav-link {
    @for $i from 1 through $itemCount {
      &:nth-child(#{$i}) {
        @media screen and (min-width: calc(#{$i} * #{$submenu-element-width} + #{$submenu-screen-width-correction})) {
          @content;
        }
      }
    }
  }
}

@mixin moreButtonShow($itemCount: 7, $submenu-screen-width-correction: 0px) {
  @for $i from 1 through $itemCount {
    &.menu-item-count-#{$itemCount - $i + 1} {
      @media screen and (max-width: calc(#{$itemCount - $i + 1} * #{$submenu-element-width} + #{$submenu-screen-width-correction})) {
        display: flex !important;
      }
    }
  }
}

@mixin subLinksDropDown() {
  .sub-links {
    display: flex;
    flex-wrap: nowrap;
    position: absolute;
    top: 100%;
    right: -50%;
    background: $white;
    border: 1px solid $grey-19;
    box-shadow: 0 20px 50px rgba($black, 0.25);
    border-radius: 6px;
    width: auto;
    max-width: none;
    min-width: max-content;
    padding: 36px 40px 36px 0;
    z-index: 100;
    transform-origin: 75% 0;
    transition: #{$submenu-open-anim};

    @include media-breakpoint-up(md) {
      transform: scale(0);
      opacity: 0;
      pointer-events: none;
    }

    @include media-breakpoint-down(md) {
      position: relative;
      top: 0;
      right: 0;
      min-width: 0;
      box-shadow: 0 0 0 0 transparent;
      border: 0;
      padding: 15px 0 0 0;
    }

    @content;
  }

  .sub-link-arrow {
    width: 20px;
    position: absolute;
    top: calc(100% - 9px);
    right: calc(50% - 10px);
    height: 10px;
    z-index: 101;
    overflow: hidden;
    display: block;
    transform-origin: 0 100%;
    transition: 0.2s ease-out;

    @include media-breakpoint-up(md) {
      transform: scaleY(0);
    }

    @include media-breakpoint-down(md) {
      display: none !important;
    }

    &:before {
      background: $white;
      border: 1px solid $grey-19;
      display: block;
      content: ' ';
      position: relative;
      top: 3.5px;
      right: 0;
      width: 20px;
      height: 20px;
      z-index: 99;
      transform: rotate(45deg);
    }
  }

  /* li active & hover */
  &.active,
  &:hover {
    @include media-breakpoint-up(md) {
      .sub-links {
        transform: scale(1);
        opacity: 1;
        pointer-events: all;
      }
      .sub-link-arrow {
        transform: scaleY(1);
      }
    }

    .link-level-first:after {
      transform: scaleY(-1);
    }
  }
}

@mixin submenu() {
  position: relative;
  margin-left: 40px;
  padding: 10px 0;
  @include media-breakpoint-down(md) {
    display: block;
    width: 100%;
    margin: 0 20px 16px;
    padding: 0;
  }
  @include media-breakpoint-up(lg) {
    &:first-child {
      margin-left: 0;
    }
  }

  .link-level-first {
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;
    display: flex;
    align-items: center;
    cursor: default;

    &:not(.normal-link):after {
      width: 12px;
      height: 8px;
      @include icon('icons/menu-arrow.svg');
      content: ' ';
      display: block;
      margin-left: 7px;
      transition: #{$submenu-open-anim};

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    &.normal-link {
      cursor: pointer;
    }
  }

  @include subLinksDropDown() {
    .column {
      display: block;
      // width: calc(33.333% - 40px);
      margin-left: 40px;

      &.column-title {
        color: $grey-7;
      }

      .link-level-second {
        &:active,
        &:visited,
        &:link {
          color: $base-text-color;
        }

        &:hover {
          color: $blue;
        }

        display: block;
        width: auto;
        max-width: none;
        margin-top: 15px;
        min-width: 120px;
        font-family: $font-secondary;
        font-weight: 400;
        font-size: 16px;
        line-height: 30px;
      }
    }
  }
}

.header-top {
  // border-bottom: 1px solid $grey-19;
  min-height: 90px;
  display: flex;
  align-items: center;

  @include media-breakpoint-down(md) {
    min-height: 50px;
  }

  .wrapper {
    .col-12 {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      // justify-content: space-between;
      flex-wrap: wrap;

      @include media-breakpoint-down(md) {
        border-bottom: 1px solid $grey-19;
      }
    }

    @include media-breakpoint-down(md) {
      .social,
      .custom-buttons,
      .right-elements {
        display: none;
      }
    }
  }

  .today {
    .day {
      font-size: 14px;
      line-height: 21px;
      color: $black;

      &::first-letter {
        text-transform: uppercase;
      }
    }

    .nameday {
      &,
      & a {
        font-size: 12px;
        line-height: 18px;
        color: $grey-7;
      }
    }
  }

  .weather {
    display: flex;
    align-items: center;

    .actual {
      font-weight: 500;
      font-size: 20px;
      line-height: 30px;
      color: $black;
      margin-right: 6px;

      .icon {
        width: 23px;
        height: 23px;
        display: inline-block;
        margin-bottom: -4px;

        &.sunny {
          @include icon('weather/sunny.svg');
        }

        &.cloud-wind {
          @include icon('weather/cloud-wind.svg');
        }

        &.cloud-snow {
          @include icon('weather/cloud-snow.svg');
        }

        &.cloud-rain {
          @include icon('weather/cloud-rain.svg');
        }

        &.clouds {
          @include icon('weather/clouds.svg');
        }

        &.cloud-hail {
          @include icon('weather/cloud-hail.svg');
        }

        &.cloud-hurricane {
          @include icon('weather/cloud-hurricane.svg');
        }

        &.wind-sun {
          @include icon('weather/wind-sun.svg');
        }

        &.cloud-thunder-heavy {
          @include icon('weather/cloud-thunder-heavy.svg');
        }

        &.snowflake {
          @include icon('weather/snowflake.svg');
        }
      }
    }

    .minmax {
      font-weight: 500;
      font-size: 12px;
      line-height: 18px;

      .max {
        color: $red;
      }

      .min {
        color: $blue;
      }
    }
  }

  .social {
    flex-wrap: wrap;
    @include media-breakpoint-down(lg) {
      // width: 40px;
    }

    .social-button {
      width: 30px;
      height: 30px;
      background: $black;
      border-radius: 50%;
      margin: 5px 10px;

      &.facebook {
        background-color: $socialcolor-facebook;
        @include icon('icons/facebook.svg');
        background-size: 31%;
      }

      &.instagram {
        background-color: $socialcolor-instagram;
        @include icon('icons/instagram.svg');
        background-size: 60%;
      }
    }
  }

  .right-elements {
    display: flex;
    justify-content: flex-end;
    margin-left: auto;
    @include media-breakpoint-up(md) {
      ::ng-deep div:last-child .right-button {
        margin-right: 0 !important;
      }
    }

    .right-button {
      border-radius: 6px;
      height: 50px;
      padding: 14px 15px;
      margin-right: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      @include media-breakpoint-down(lg) {
        margin-right: 10px;
      }

      &:first-child {
        margin-left: 0;
      }

      &.radio {
        background: $blue;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        color: $white;
        width: 120px;
        max-width: 100%;
        cursor: pointer;

        img {
          margin-left: 5px;
          width: 60px;
        }

        &:before {
          width: 20px;
          height: 20px;
          display: inline-block;
          content: ' ';
          @include icon('icons/media.svg');
          margin-right: 5px;
          @include media-breakpoint-down(lg) {
            display: none;
          }
        }
      }

      &.retro-radio {
        background: #d2122d;
        color: #f7eedb;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        color: $white;
        width: 170px;
        max-width: 100%;
        cursor: pointer;
        padding-left: 5px;

        img {
          width: 42px;
          height: 35px;
          margin-right: 10px;
        }
      }

      &.video {
        background: $red;
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        color: $white;
        margin-right: 0px;
        cursor: pointer;

        .live {
          background: $black;
          border-radius: 6px;
          font-weight: 500;
          font-size: 10px;
          line-height: 15px;
          display: flex;
          text-transform: uppercase;
          height: 24px;
          align-items: center;
          padding: 0 9px;
          margin-left: 17px;
          @include media-breakpoint-down(lg) {
            display: none;
          }

          &:before {
            width: 14px;
            height: 14px;
            display: inline-block;
            content: ' ';
            @include icon('icons/media.svg');
            margin-right: 5px;
            @include media-breakpoint-down(lg) {
              display: none;
            }
          }
        }
      }

      &.login {
        width: 140px;
        max-width: 100%;
        font-weight: 500;
        font-size: 14px;
        color: $black;
        background: $grey-22;
        border: 1px solid $grey-16;
      }
    }
  }
}

.header {
  border-bottom: 1px solid $grey-19;
  min-height: 71px;
  display: flex;
  align-items: center;
  position: relative;

  .wrapper {
    > .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    .left {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      padding: 0 15px;
      @include media-breakpoint-down(md) {
        width: 100%;
        justify-content: space-between;
      }

      .logo-wrappper {
        .logo {
          width: calc(#{$logo-width} + 75px);
          height: 55px;
          display: flex;
          align-items: center;
          margin-right: 70px;
          cursor: pointer;
          @include media-breakpoint-down(lg) {
            margin-right: 0;
          }
          @include media-breakpoint-down(md) {
            width: calc(#{$logo-width} + 10px);
          }
          @media screen and (max-width: $mobile-old) {
            width: calc(#{$logo-width} - 25px);
          }

          .logo-figure {
            position: relative;
            margin-right: 7px;
            height: 39px;

            @include media-breakpoint-down(md) {
              height: 30px;
            }

            svg {
              width: auto;
              height: 100%;
            }

            .primary-color {
              fill: $primary-color;
            }

            .secondary-color {
              fill: $secondary-color;
            }
          }

          .text {
            font-family: $font-secondary;
            text-transform: uppercase;

            .portal-name {
              font-weight: 500;
              font-size: 30px;
              @include media-breakpoint-down(md) {
                font-size: 22.5px;
                margin-top: -2px;
              }
            }

            .portal-subtitle {
              font-weight: 300;
              font-size: 12px;
              max-width: $logo-width;
              @include media-breakpoint-down(md) {
                font-size: 9px;
              }
            }
          }

          &:link,
          &:visited,
          &:active {
            outline: none;

            .text {
              .portal-name,
              .portal-subtitle {
                color: $black;
              }
            }
          }
        }
      }

      .left-side {
        display: flex;
        align-items: center;

        .search-wrappper {
          display: flex;
          height: 26px;
          justify-content: flex-start;
          align-items: center;

          .search-button {
            .icon-search {
              width: 24px;
              height: 24px;
              display: block;
              @include icon('icons/search.svg');
              margin-right: 6px;
              @media screen and (max-width: $mobile-old) {
                margin-right: 3px;
                width: 20px;
                margin-top: 5px;
              }
            }
          }

          input {
            font-size: 14px;
            @include media-breakpoint-down(md) {
              display: none;
            }
          }
        }

        .hamburger {
          @include media-breakpoint-up(lg) {
            display: none;
          }
          @media screen and (max-width: $mobile-old) {
            padding-left: 5px;
          }
        }
      }
    }

    .right {
      @include media-breakpoint-down(md) {
        position: absolute;
        top: 100%;
        left: 0;
        max-width: 100%;
        background: $white;
        z-index: 100;
        border-bottom: 1px solid $grey-19;
        transform-origin: center top;
        transform: scaleY(0);
        opacity: 0;
        transition: #{$submenu-open-anim};
        pointer-events: none;

        &.opened {
          opacity: 1;
          transform: scaleY(1);
          pointer-events: all;
          margin-top: 1px;
        }
      }

      .nav {
        @include media-breakpoint-down(md) {
          padding-top: 15px;
        }

        ul {
          display: none;
          justify-content: flex-end;
          flex-wrap: wrap;

          li {
            @include submenu();
          }
        }

        .nav-desktop {
          @include media-breakpoint-up(lg) {
            display: flex;
            flex-wrap: wrap;
          }
        }

        .nav-mobile {
          @include media-breakpoint-down(md) {
            display: flex;
            border-bottom: 1px solid $grey-19;
          }

          li {
            .sub-links {
              .column {
                margin-left: 0;
                width: 100%;
                column-count: 2;

                a {
                  margin-top: 0;
                  margin-bottom: 3px;
                  font-size: 16px;
                  line-height: 30px;
                }
              }
            }
          }
        }

        .sub-nav-mobile {
          display: flex;
          margin-top: 20px;
          justify-content: space-between;
          padding: 0 20px;
          padding-bottom: 20px;

          @include media-breakpoint-up(lg) {
            display: none;
          }

          .column {
            width: 50%;
            display: block;

            .mobile-submenu {
              display: block;
              list-style-type: disc;
              padding-left: 20px;

              li {
                margin-left: 0;
              }
            }

            .sub-nav-link-mobile {
              display: block;
              font-weight: 500;
              width: calc(100% - 20px);
              font-size: 16px;
              line-height: 24px;
              margin: 8px 0;
              overflow: hidden;
              text-overflow: ellipsis;

              &,
              &:active,
              &:visited,
              &:link {
                color: $base-text-color;
              }
            }
          }
        }
      }
    }
  }
}

.sub-nav {
  border-bottom: 1px solid $grey-19;
  display: flex;
  align-items: center;
  @include media-breakpoint-down(md) {
    display: none;
  }

  .wrapper {
    > .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 71px;
    }

    .sub-nav-link {
      white-space: nowrap;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      margin: 0 10px;

      display: none;

      &,
      &:active,
      &:visited,
      &:link {
        color: $base-text-color;
      }

      &:hover {
        color: $blue;
      }
    }

    .more {
      display: none;
      align-items: center;
      justify-content: space-between;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      padding: 3px;
      margin-left: 10px;
      margin-right: 10px;
      flex-shrink: 0;
      cursor: pointer;

      &:hover {
        background: rgba($black, 0.1);
      }

      .dot {
        width: 4px;
        height: 4px;
        background: $black !important;
        border-radius: 50%;
        display: inline-block;
      }

      position: relative;

      .sub-nav-link {
        display: none;
      }

      &:hover {
        .sub-nav-link {
          display: flex;
          flex-direction: column;
        }
      }

      .overflow-menu {
        position: absolute;
        right: 50%;
        bottom: 0;

        .sub-nav-link {
          a {
            white-space: nowrap;
          }
        }
      }

      @include subLinksDropDown;
    }

    .places {
      margin-left: -10px;
      display: flex;
      align-items: center;
      width: 100%;
      flex-basis: 0;

      @include autoHideMenuItems($left-menu-overflow-count) {
        display: inline-block;
      }
    }

    .categories {
      margin-right: -10px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      flex-basis: 0;
      @include autoHideMenuItems($right-menu-overflow-count) {
        display: inline-block;
      }
    }

    .categories,
    .places {
      .dropdown-container {
        margin: 0 10px;

        @include submenu();

        .sub-links {
          padding: 20px 33px 20px 0;

          .column {
            margin-left: 33px;

            .link-level-second {
              margin-top: 5px;
            }
          }
        }

        .link-level-first {
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
        }
      }

      .more {
        .sub-links {
          right: -20px;
          min-width: 0;
          padding: 16px 10px;
          display: block;

          .submenu-title {
            white-space: nowrap;
            display: inline-flex;
          }

          .submenu-container {
            list-style-type: disc;
            padding: 0 0 0 25px;
          }
        }
      }
    }

    .places {
      .sub-links {
        @include autoHideMenuItems($left-menu-overflow-count) {
          display: none;
        }
      }

      .more {
        @include moreButtonShow(8, 14000px);
      }
    }

    .categories {
      .sub-links {
        @include autoHideMenuItems($right-menu-overflow-count) {
          display: none;
        }
      }

      .more {
        @include moreButtonShow(8, 10000px);
      }
    }
  }
}

.icon {
  width: 12px;
  height: 8px;
  @include icon('icons/menu-arrow.svg');
  display: block;
  margin-left: 7px;
  transition: #{$submenu-open-anim};
  transform: scaleY(1);

  &.open {
    transform: scaleY(-1);
  }
}

.mobile {
  display: none;
  @include media-breakpoint-down(md) {
    display: flex;
  }
}

.custom-buttons {
  @include media-breakpoint-down(md) {
    display: flex;
    border-top: 1px solid $grey-19;
    margin-top: 20px;
    padding-top: 20px;
    padding-bottom: 10px;
    justify-content: space-evenly;
  }

  .custom-button {
    font-weight: 300;
    font-size: 14px;
    line-height: 21px;
    color: $grey-7;
    margin: 10px 15px;
    display: inline-block;
    text-align: center;

    @include media-breakpoint-down(lg) {
      margin: 10px 9px;
      font-size: 12px;
      line-height: 18px;
    }

    .icon {
      width: 16px;
      height: 16px;
      //background: $grey-7;
      display: inline-block;

      &.icon-date {
        @include icon('icons/date.svg');
      }

      &.icon-plus {
        @include icon('icons/plus.svg');
        background-size: 150%;
      }

      &.icon-send {
        @include icon('icons/send.svg');
      }

      &.icon-subscribe {
        @include icon('icons/subscribe.svg');
      }
    }

    span {
      width: 100%;
      display: block;
    }
  }
}

.extra-margin {
  margin-left: auto;
}

.extra-margin-small {
  margin-left: 35px;
  @include media-breakpoint-down(md) {
    margin: unset;
  }
}

.sub-left-margin {
  margin-right: 37px;
}

app-elections-box.header-elections-diverter {
  margin: 30px 0;
}

.mobile-only {
  @include media-breakpoint-up(lg) {
    display: none;
  }
}

.with-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  &.sub-nav-link img {
    width: 1rem;
    height: 1rem;
  }
  img {
    height: 22px;
    width: 22px;
  }
}
