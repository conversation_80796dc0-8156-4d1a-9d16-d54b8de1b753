import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';
import { startWith, Subject } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import {
  AdvertisementAdoceanComponent,
  AdvertisementsByMedium,
  AnalyticsService,
  BreakingNews,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  NEWSLETTER_COMPONENT_TYPE,
} from '@trendency/kesma-ui';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { SafePipe, UtilService } from '@trendency/kesma-core';
import { AsyncPipe, NgClass, NgFor, NgIf, NgSwitch, Ng<PERSON><PERSON>Case, NgSwitchDefault, NgTemplateOutlet } from '@angular/common';
import { MediaStreamComponent } from '../media-stream/media-stream.component';
import { BreakingBlockComponent } from '../breaking-block/breaking-block.component';
import { BreakingBlockBasic, CityWeatherCurrent, InitWeather, MainMenuChild, MediaStreamApi, MenuChild, RelatedType, WeatherData } from '../../definitions';
import { getLinkFromMenuItem } from '../../utils';
import { ElectionsService, NamedaysService, PortalConfigService, PortalUtilsService, WeatherService } from '../../services';
import { DateFnsModule } from 'ngx-date-fns';

// How many items fit in a menu at most - independent of what do we want to display
const MAX_MENU_SIZE_LEFT = 8;
const MAX_MENU_SIZE_RIGHT = 8;

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AdvertisementAdoceanComponent,
    RouterLink,
    NgFor,
    MediaStreamComponent,
    FormsModule,
    ReactiveFormsModule,
    NgClass,
    NgTemplateOutlet,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    BreakingBlockComponent,
    AsyncPipe,
    SafePipe,
    ElectionsBoxComponent,
    DateFnsModule,
  ],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Input() mediaStream: MediaStreamApi;
  @Input() menuHeader: MenuChild[] = [];
  @Input() menuLeft: MenuChild[] = [];
  @Input() menuSecondary: MainMenuChild[] = [];
  @Input() menuRight: MenuChild[] = [];
  @Input() namedays = '';
  @Input() weatherInfo: InitWeather;
  @Input() isArticleUrl: boolean;
  @Input() breakingNews: BreakingNews;

  getLink = getLinkFromMenuItem;
  RelatedType = RelatedType;
  maxMenuSizeLeft = MAX_MENU_SIZE_LEFT;
  maxMenuSizeRight = MAX_MENU_SIZE_RIGHT;
  breakingBlockBasic: BreakingBlockBasic;
  portalName: string;
  portalSubtitle: string;
  facebookUrl: string;
  instagramUrl: string;
  isBurgerOpened = false;
  today: Date;
  weather: WeatherData;
  currentWeather: CityWeatherCurrent | undefined;
  city: string;
  mainNamedays: string[] = [];
  textSearchControl = new UntypedFormControl();
  pageType: string;
  submenuOpen: number;

  @Input() adverts: AdvertisementsByMedium;

  @Input() url: string;
  ElectionsBoxStyle = ElectionsBoxStyle;

  radioGagaUrl: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl('https://www.radiogaga.ro/wp-content/themes/sounder-child/frame/content4.html');

  private readonly destroySubject: Subject<boolean> = new Subject<boolean>();
  isHome$ = this.router.events.pipe(
    startWith(new NavigationEnd(0, '/', '/')),
    filter((event) => event instanceof NavigationEnd),
    map(() => this.router.url === '/' || this.router.url.includes('valasztas-2024-')),
    takeUntil(this.destroySubject)
  );

  constructor(
    private readonly namedayService: NamedaysService,
    private readonly weatherService: WeatherService,
    private readonly utilsService: PortalUtilsService,
    private readonly router: Router,
    private readonly portalConfig: PortalConfigService,
    private readonly utils: UtilService,
    private readonly analyticsService: AnalyticsService,
    private readonly changeRef: ChangeDetectorRef,
    public readonly electionsService: ElectionsService,
    public readonly activatedRoute: ActivatedRoute,
    private readonly sanitizer: DomSanitizer
  ) {
    this.portalName = this.portalConfig.portalName;
    this.portalSubtitle = this.portalConfig.portalSubtitle;
    this.city = this.portalConfig.city;
    this.facebookUrl = this.utilsService.getFacebooklink(this.portalConfig.portalName);
    this.instagramUrl = this.utilsService.getInstagramlink(this.portalConfig.portalName);
    this.router.routeReuseStrategy.shouldReuseRoute = function (): boolean {
      return false;
    };
  }

  ngOnInit(): void {
    this.today = this.utilsService.now();
    this.namedayService
      .getTodayNamedays()
      .pipe(takeUntil(this.destroySubject))
      .subscribe((namedays) => {
        this.mainNamedays = (namedays ?? [])
          .filter(({ isMain }) => isMain)
          .map(({ name }) => name)
          .sort()
          .slice(0, 2);
        this.changeRef.detectChanges();
      });
    this.weatherService
      .getWeather()
      .pipe(takeUntil(this.destroySubject))
      .subscribe((weather: WeatherData) => {
        this.currentWeather = weather.current.filter((item) => item.city === this.city).shift();
        this.changeRef.detectChanges();
      });

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        startWith(null)
      )
      .subscribe((): void => {
        this.isBurgerOpened = false;
      });
  }

  getYear(publishDate: string): string {
    return publishDate?.split('-')[0];
  }

  getMonth(publishDate: string): string {
    return publishDate?.split('-')[1];
  }

  ngOnDestroy(): void {
    this.destroySubject.next(true);
    this.destroySubject.complete();
  }

  toggleBurger(): void {
    this.submenuOpen = -1;
    this.isBurgerOpened = !this.isBurgerOpened;
    this.changeRef.detectChanges();
  }

  toggleSubmenu(id: number): void {
    if (id === this.submenuOpen) {
      this.submenuOpen = -1;
    } else {
      this.submenuOpen = id;
    }
    this.changeRef.detectChanges();
  }

  onCloseMenu(): void {
    this.isBurgerOpened = false;
    this.changeRef.detectChanges();
  }

  onSearch(): void {
    const queryParams = {
      queryParams: {
        text: this.textSearchControl.value,
        timestamp: new Date().getTime(),
      },
    };
    this.router.navigate(['/', 'kereses'], this.textSearchControl.value ? queryParams : {});
  }

  searchKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.onSearch();
    }
  }

  isAdblockerActive(): boolean {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the Adocean does not exist on SSR.
      return false;
    }
    return typeof (window as any).ado !== 'object';
  }

  onNewsletterClicked(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.HEADER);
  }

  isRadioGagaActive(): boolean {
    return (this.menuHeader?.map((menu) => menu.title) ?? []).includes('Rádió GaGa') || false;
  }
}
