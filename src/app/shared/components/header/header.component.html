<kesma-advertisement-adocean *ngIf="adverts?.desktop?.leaderboard_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.desktop?.layer as ad" [ad]="ad"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.desktop?.interstitial as ad" [ad]="ad"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="portalName !== 'BEOL' && adverts?.mobile?.layer as ad" [ad]="ad"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.interstitial as ad" [ad]="ad"></kesma-advertisement-adocean>

<section class="header-top">
  <div class="wrapper">
    <div class="row">
      <div class="col-12">
        <div class="today">
          <p class="day">{{ today | dfnsFormat: 'LLLL d., eeee' }}</p>
          <p (click)="onCloseMenu()" class="nameday">
            <a [routerLink]="['/', 'nevnapok']">
              <ng-container *ngFor="let name of mainNamedays; let isLast = last">
                <span>{{ name.trim() }}</span>
                <ng-container *ngIf="!isLast">,&nbsp;</ng-container>
              </ng-container>
              névnap
            </a>
          </p>
        </div>
        <a (click)="onCloseMenu()" *ngIf="currentWeather" [routerLink]="['/', 'idojaras']" class="weather extra-margin">
          <div class="actual">
            <i [class]="currentWeather.icon2" class="icon"></i>
            {{ currentWeather.temperature }}°
          </div>
          <div class="minmax">
            <div class="max">{{ currentWeather.maxTemperature ? '+' : '' }}{{ currentWeather.maxTemperature }}</div>
            <div class="min">{{ currentWeather.minTemperature ? '+' : '' }}{{ currentWeather.minTemperature }}</div>
          </div>
        </a>
        <div class="social extra-margin">
          <a [href]="facebookUrl" class="social-button facebook"></a>
          <a [href]="instagramUrl" class="social-button instagram"></a>
        </div>
        <div class="custom-buttons extra-margin-small">
          <a *ngIf="portalName !== 'ERDON'" [routerLink]="['/', 'evfordulok']" class="custom-button">
            <i class="icon icon-date"></i>
            <span>Mai évfordulók</span>
          </a>
          <!--<a class="custom-button" [routerLink]="['/']">
            <i class="icon icon-plus"></i>
            <span>Előfizetek</span>
          </a> -->
          <a *ngIf="portalName !== 'ERDON'" [routerLink]="['/', 'kuldjon-hirt']" class="custom-button">
            <i class="icon icon-send"></i>
            <span>Hírt küldök be</span>
          </a>
          <a
            (click)="onNewsletterClicked()"
            *ngIf="portalName !== 'ERDON'"
            [href]="['/hirlevel-feliratkozas']"
            class="custom-button"
            target="_blank"
            title="Hírlevél feliratkozás"
          >
            <i class="icon icon-subscribe"></i>
            <span>Hírlevél</span>
          </a>
        </div>
        <div class="right-elements">
          <app-media-stream [isRadioGagaActive]="isRadioGagaActive()" [mediaStream]="mediaStream" [radioGagaUrl]="radioGagaUrl">
            <a class="right-button retro-radio" media-retro-radio-button
              ><img loading="lazy" src="/assets/images/retro-radio-logo.png" alt="Retro Rádió" />Retro Rádió</a
            >
            <a class="right-button radio" media-karc-fm-radio-button><img alt="Hír FM logo" loading="lazy" src="/assets/images/hirFM_logo.png" /></a>
            <a class="right-button video" media-tv-button> Hír TV </a>
          </app-media-stream>
        </div>
      </div>
    </div>
  </div>
</section>
<header class="header">
  <div class="wrapper">
    <div class="row">
      <div class="left">
        <div (click)="onCloseMenu()" class="logo-wrappper">
          <a [routerLink]="['/']" class="logo">
            <div class="logo-figure">
              <svg fill="none" viewBox="0 0 35 39" xmlns="http://www.w3.org/2000/svg">
                <path
                  class="primary-color"
                  d="M 0,2.3000044 V 12.900024 c 0,10.3 5.7,20 15.6,26.4 V 0 C 10.3,0.1000004 5,0.8999994 0,2.3000044 Z"
                  fill="#005ca2"
                />
                <path
                  class="secondary-color"
                  d="m 18.7,0 v 39.400024 c 9.9,-6.4 15.6,-16.1 15.6,-26.4 V 2.3000044 C 29.3,0.8999994 24.1,0.1000004 18.7,0 Z"
                  fill="#006d34"
                />
              </svg>
            </div>
            <div class="text">
              <p class="portal-name">{{ portalName }}</p>
              <p clasS="portal-subtitle">{{ portalSubtitle }}</p>
            </div>
          </a>
        </div>
        <div class="left-side">
          <div class="search-wrappper">
            <button (click)="onSearch()" class="search-button" type="button">
              <i (click)="onCloseMenu()" class="icon-search"></i>
            </button>
            <input (keypress)="searchKeyPress($event)" [formControl]="textSearchControl" placeholder="Keresés a cikkek között" type="search" />
          </div>
          <button (click)="toggleBurger()" [ngClass]="{ 'is-active': isBurgerOpened }" class="hamburger hamburger--squeeze" type="button">
            <span class="hamburger-box">
              <span class="hamburger-inner"></span>
            </span>
          </button>
        </div>
      </div>
      <div [ngClass]="{ opened: isBurgerOpened }" class="col-12 col-lg-6 right">
        <nav class="nav">
          <ul class="nav-desktop">
            <ng-container *ngFor="let mainNavElem of menuSecondary; let i = index">
              <li>
                <ng-container *ngIf="mainNavElem.relatedType === RelatedType.DROPDOWN; else normalLink">
                  <a class="link-level-first">{{ mainNavElem.title }}</a>
                  <div class="sub-link-arrow"></div>
                  <div class="sub-links">
                    <div *ngFor="let menuCol of mainNavElem.columns" class="column">
                      <ng-container *ngFor="let menuItem of menuCol">
                        <ng-container
                          *ngTemplateOutlet="menuItem.relatedType !== RelatedType.CUSTOM_URL ? contentUrl : customUrl; context: { menuItem: menuItem }"
                        >
                        </ng-container>
                      </ng-container>
                      <ng-template #contentUrl let-menuItem="menuItem">
                        @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship?.logo; as logo) {
                          <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second with-logo">
                            <img [src]="logo" loading="lazy" />
                            {{ menuItem.title }}
                          </a>
                        } @else {
                          <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second">{{
                            menuItem.title
                          }}</a>
                        }
                      </ng-template>
                      <ng-template #customUrl let-menuItem="menuItem">
                        <a [href]="menuItem.customUrl | safe: 'url'" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second">{{
                          menuItem.title
                        }}</a>
                      </ng-template>
                    </div>
                  </div>
                </ng-container>
                <ng-template #normalLink>
                  @if (mainNavElem.relatedType !== RelatedType.CUSTOM_URL) {
                    @if (mainNavElem.relatedType === RelatedType.COLUMN && mainNavElem?.related?.sponsorship?.logo; as logo) {
                      <a
                        [routerLink]="getLink(mainNavElem)"
                        [target]="mainNavElem.targetBlank ? '_blank' : '_self'"
                        class="link-level-first normal-link with-logo"
                      >
                        <img [src]="logo" loading="lazy" />{{ mainNavElem.title }}</a
                      >
                    } @else {
                      <a [routerLink]="getLink(mainNavElem)" [target]="mainNavElem.targetBlank ? '_blank' : '_self'" class="link-level-first normal-link">{{
                        mainNavElem.title
                      }}</a>
                    }
                  } @else {
                    <a
                      [href]="mainNavElem.customUrl | safe: 'url'"
                      [target]="mainNavElem.targetBlank ? '_blank' : '_self'"
                      class="link-level-first normal-link"
                      >{{ mainNavElem.title }}</a
                    >
                  }
                </ng-template>
              </li>
            </ng-container>
          </ul>

          <ul class="nav-mobile">
            <ng-container *ngFor="let mainNavElem of menuSecondary; let i = index">
              <li>
                <ng-container *ngIf="mainNavElem.relatedType === RelatedType.DROPDOWN; else normalLink">
                  <a (click)="toggleSubmenu(i)" class="link-level-first"
                    >{{ mainNavElem.title }}
                    <span [ngClass]="submenuOpen === i ? 'open' : 'closed'" class="icon"></span>
                  </a>
                  <!-- <div class="sub-link-arrow"></div> -->
                  <div *ngIf="submenuOpen === i" class="sub-links">
                    <div (click)="toggleBurger()" class="column">
                      <ng-container *ngFor="let menuItem of mainNavElem.children">
                        <ng-container
                          *ngTemplateOutlet="menuItem.relatedType !== RelatedType.CUSTOM_URL ? contentUrl : customUrl; context: { menuItem: menuItem }"
                        >
                        </ng-container>
                      </ng-container>
                      <ng-template #contentUrl let-menuItem="menuItem">
                        @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship?.logo; as logo) {
                          <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second with-logo">
                            <img [src]="logo" loading="lazy" />
                            {{ menuItem.title }}</a
                          >
                        } @else {
                          <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second">{{
                            menuItem.title
                          }}</a>
                        }
                      </ng-template>
                      <ng-template #customUrl let-menuItem="menuItem">
                        <a [href]="menuItem.customUrl | safe: 'url'" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second">{{
                          menuItem.title
                        }}</a>
                      </ng-template>
                    </div>
                  </div>
                </ng-container>
                <ng-template #normalLink>
                  @if (mainNavElem.relatedType !== RelatedType.CUSTOM_URL) {
                    @if (mainNavElem.relatedType === RelatedType.COLUMN && mainNavElem?.related?.sponsorship?.logo; as logo) {
                      <a
                        [routerLink]="getLink(mainNavElem)"
                        [target]="mainNavElem.targetBlank ? '_blank' : '_self'"
                        class="link-level-first normal-link with-logo"
                        ><img [src]="logo" loading="lazy" />{{ mainNavElem.title }}</a
                      >
                    } @else {
                      <a [routerLink]="getLink(mainNavElem)" [target]="mainNavElem.targetBlank ? '_blank' : '_self'" class="link-level-first normal-link">{{
                        mainNavElem.title
                      }}</a>
                    }
                  } @else {
                    <a
                      [href]="mainNavElem.customUrl | safe: 'url'"
                      [target]="mainNavElem.targetBlank ? '_blank' : '_self'"
                      class="link-level-first normal-link"
                      >{{ mainNavElem.title }}</a
                    >
                  }
                </ng-template>
              </li>
            </ng-container>
          </ul>

          <div (click)="onCloseMenu()" class="sub-nav-mobile">
            <div class="column">
              <ng-container *ngFor="let menuItem of menuLeft">
                <ng-container [ngSwitch]="menuItem.relatedType">
                  <ng-container *ngSwitchCase="RelatedType.DROPDOWN">
                    <ng-container *ngTemplateOutlet="mobileNoLink; context: { menuItem: menuItem }"></ng-container>
                  </ng-container>
                  <ng-container *ngSwitchCase="RelatedType.CUSTOM_URL">
                    <ng-container *ngTemplateOutlet="mobileExternalLink; context: { menuItem: menuItem }"></ng-container>
                  </ng-container>
                  <ng-container *ngSwitchDefault>
                    <ng-container *ngTemplateOutlet="mobileInternalLink; context: { menuItem: menuItem }"></ng-container>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="!!menuItem.children.length">
                  <ul class="mobile-submenu">
                    <ng-container *ngFor="let subMenuItem of menuItem.children">
                      <li>
                        <ng-container [ngSwitch]="subMenuItem.relatedType">
                          <ng-container *ngSwitchCase="RelatedType.DROPDOWN">
                            <ng-container *ngTemplateOutlet="mobileNoLink; context: { menuItem: subMenuItem }"></ng-container>
                          </ng-container>
                          <ng-container *ngSwitchCase="RelatedType.CUSTOM_URL">
                            <ng-container *ngTemplateOutlet="mobileExternalLink; context: { menuItem: subMenuItem }"></ng-container>
                          </ng-container>
                          <ng-container *ngSwitchDefault>
                            <ng-container *ngTemplateOutlet="mobileInternalLink; context: { menuItem: subMenuItem }"></ng-container>
                          </ng-container>
                        </ng-container>
                      </li>
                    </ng-container>
                  </ul>
                </ng-container>
              </ng-container>
            </div>
            <div class="column">
              <ng-container *ngFor="let menuItem of menuRight">
                <ng-container [ngSwitch]="menuItem.relatedType">
                  <ng-container *ngSwitchCase="RelatedType.DROPDOWN">
                    <ng-container *ngTemplateOutlet="mobileNoLink; context: { menuItem: menuItem }"></ng-container>
                  </ng-container>
                  <ng-container *ngSwitchCase="RelatedType.CUSTOM_URL">
                    <ng-container *ngTemplateOutlet="mobileExternalLink; context: { menuItem: menuItem }"></ng-container>
                  </ng-container>
                  <ng-container *ngSwitchDefault>
                    <ng-container *ngTemplateOutlet="mobileInternalLink; context: { menuItem: menuItem }"></ng-container>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="!!menuItem.children.length">
                  <ul class="mobile-submenu">
                    <ng-container *ngFor="let subMenuItem of menuItem.children">
                      <li>
                        <ng-container [ngSwitch]="subMenuItem.relatedType">
                          <ng-container *ngSwitchCase="RelatedType.DROPDOWN">
                            <ng-container *ngTemplateOutlet="mobileNoLink; context: { menuItem: subMenuItem }"></ng-container>
                          </ng-container>
                          <ng-container *ngSwitchCase="RelatedType.CUSTOM_URL">
                            <ng-container *ngTemplateOutlet="mobileExternalLink; context: { menuItem: subMenuItem }"></ng-container>
                          </ng-container>
                          <ng-container *ngSwitchDefault>
                            <ng-container *ngTemplateOutlet="mobileInternalLink; context: { menuItem: subMenuItem }"></ng-container>
                          </ng-container>
                        </ng-container>
                      </li>
                    </ng-container>
                  </ul>
                </ng-container>
              </ng-container>
            </div>

            <ng-template #mobileInternalLink let-menuItem="menuItem">
              @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship?.logo; as logo) {
                <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="sub-nav-link-mobile with-logo">
                  <img [src]="logo" loading="lazy" />
                  {{ menuItem.title }}</a
                >
              } @else {
                <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="sub-nav-link-mobile">{{ menuItem.title }}</a>
              }
            </ng-template>
            <ng-template #mobileExternalLink let-menuItem="menuItem">
              <a [href]="menuItem.customUrl | safe: 'url'" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="sub-nav-link-mobile">{{
                menuItem.title
              }}</a>
            </ng-template>
            <ng-template #mobileNoLink let-menuItem="menuItem">
              <span class="sub-nav-link-mobile">{{ menuItem.title }}</span>
            </ng-template>
          </div>

          <div class="custom-buttons mobile extra-margin-small">
            <a [routerLink]="['/', 'evfordulok']" class="custom-button">
              <i class="icon icon-date"></i>
              <span>Mai évfordulók</span>
            </a>
            <a *ngIf="portalName !== 'ERDON'" [routerLink]="['/', 'kuldjon-hirt']" class="custom-button">
              <i class="icon icon-send"></i>
              <span>Hírt küldök be</span>
            </a>
          </div>
        </nav>
      </div>
    </div>
  </div>
</header>
<nav class="section sub-nav">
  <div class="wrapper container">
    <div class="row">
      <div class="places">
        <ng-container *ngFor="let menuItem of menuLeft">
          <ng-container
            *ngTemplateOutlet="menuItem.relatedType === RelatedType.DROPDOWN ? mainMenuSubMenu : mainMenuNormalLink; context: { menuItem: menuItem }"
          >
          </ng-container>
        </ng-container>

        <div class="more {{ 'menu-item-count-' + (menuLeft?.length > maxMenuSizeLeft ? maxMenuSizeLeft : menuLeft?.length)?.toString() }}">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
          <div class="overflow-menu">
            <div class="sub-link-arrow"></div>
            <div class="sub-links">
              <ng-container *ngFor="let menuItem of menuLeft">
                <div class="sub-nav-link">
                  <ng-container [ngSwitch]="menuItem.relatedType">
                    <ng-container *ngSwitchCase="RelatedType.DROPDOWN">
                      <ng-container *ngTemplateOutlet="overflowNoLink; context: { menuItem: menuItem }"></ng-container>
                    </ng-container>
                    <ng-container *ngSwitchCase="RelatedType.CUSTOM_URL">
                      <ng-container *ngTemplateOutlet="overflowExternalLink; context: { menuItem: menuItem }"></ng-container>
                    </ng-container>
                    <ng-container *ngSwitchDefault>
                      <ng-container *ngTemplateOutlet="overflowInternalLink; context: { menuItem: menuItem }"></ng-container>
                    </ng-container>
                  </ng-container>
                  <ul *ngIf="!!menuItem.children.length" class="submenu-container">
                    <ng-container *ngFor="let subMenuItem of menuItem.children">
                      <li>
                        <ng-container
                          *ngTemplateOutlet="
                            subMenuItem.relatedType !== RelatedType.CUSTOM_URL ? overflowInternalLink : overflowExternalLink;
                            context: { menuItem: subMenuItem, menuClass: 'submenu' }
                          "
                        ></ng-container>
                      </li>
                    </ng-container>
                  </ul>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>

      <div class="categories">
        <ng-container *ngFor="let menuItem of menuRight">
          <ng-container
            *ngTemplateOutlet="menuItem.relatedType === RelatedType.DROPDOWN ? mainMenuSubMenu : mainMenuNormalLink; context: { menuItem: menuItem }"
          >
          </ng-container>
        </ng-container>

        <div class="more {{ 'menu-item-count-' + (menuRight?.length > maxMenuSizeRight ? maxMenuSizeRight : menuRight?.length)?.toString() }}">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
          <div class="overflow-menu">
            <div class="sub-link-arrow"></div>
            <div class="sub-links">
              <ng-container *ngFor="let menuItem of menuRight">
                <div class="sub-nav-link">
                  <ng-container [ngSwitch]="menuItem.relatedType">
                    <ng-container *ngSwitchCase="RelatedType.DROPDOWN">
                      <ng-container *ngTemplateOutlet="overflowNoLink; context: { menuItem: menuItem }"></ng-container>
                    </ng-container>
                    <ng-container *ngSwitchCase="RelatedType.CUSTOM_URL">
                      <ng-container *ngTemplateOutlet="overflowExternalLink; context: { menuItem: menuItem }"></ng-container>
                    </ng-container>
                    <ng-container *ngSwitchDefault>
                      <ng-container *ngTemplateOutlet="overflowInternalLink; context: { menuItem: menuItem }"></ng-container>
                    </ng-container>
                  </ng-container>
                  <ul *ngIf="!!menuItem.children.length" class="submenu-container">
                    <ng-container *ngFor="let subMenuItem of menuItem.children">
                      <li>
                        <ng-container
                          *ngTemplateOutlet="
                            subMenuItem.relatedType !== RelatedType.CUSTOM_URL ? overflowInternalLink : overflowExternalLink;
                            context: { menuItem: subMenuItem, menuClass: 'submenu' }
                          "
                        ></ng-container>
                      </li>
                    </ng-container>
                  </ul>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>

<ng-template #mainMenuSubMenu let-menuItem="menuItem">
  <div class="dropdown-container sub-nav-link">
    <a class="link-level-first">{{ menuItem.title }}</a>
    <div class="sub-link-arrow"></div>
    <div class="sub-links">
      <div class="column">
        <ng-container *ngFor="let submenuItem of menuItem.children">
          <ng-container
            *ngTemplateOutlet="submenuItem.relatedType !== RelatedType.CUSTOM_URL ? mainMenuContentUrl : mainMenuCustomUrl; context: { menuItem: submenuItem }"
          >
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #mainMenuNormalLink let-menuItem="menuItem">
  @if (menuItem.relatedType !== RelatedType.CUSTOM_URL) {
    @if (menuItem.relatedType === RelatedType.COLUMN && menuItem?.related?.sponsorship?.logo; as logo) {
      <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="sub-nav-link with-logo">
        <img [src]="logo" loading="lazy" />
        {{ menuItem.title }}</a
      >
    } @else {
      <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="sub-nav-link">{{ menuItem.title }}</a>
    }
  } @else {
    <a [href]="menuItem.customUrl | safe: 'url'" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="sub-nav-link">{{ menuItem.title }}</a>
  }
</ng-template>

<ng-template #mainMenuContentUrl let-menuItem="menuItem">
  <a [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second">{{ menuItem.title }}</a>
</ng-template>
<ng-template #mainMenuCustomUrl let-menuItem="menuItem">
  <a [href]="menuItem.customUrl | safe: 'url'" [target]="menuItem.targetBlank ? '_blank' : '_self'" class="link-level-second">{{ menuItem.title }}</a>
</ng-template>

<ng-template #overflowInternalLink let-menuClass="menuClass" let-menuItem="menuItem">
  <a [class]="menuClass" [routerLink]="getLink(menuItem)" [target]="menuItem.targetBlank ? '_blank' : '_self'">
    {{ menuItem.title }}
  </a>
</ng-template>
<ng-template #overflowExternalLink let-menuClass="menuClass" let-menuItem="menuItem">
  <a [class]="menuClass" [href]="menuItem.customUrl | safe: 'url'" [target]="menuItem.targetBlank ? '_blank' : '_self'">
    {{ menuItem.title }}
  </a>
</ng-template>

<ng-template #overflowNoLink let-menuClass="menuClass" let-menuItem="menuItem">
  <span [class]="menuClass" class="submenu-title">{{ menuItem.title }}</span>
</ng-template>
<app-breaking-block *ngIf="breakingNews" [data]="breakingNews"></app-breaking-block>

<ng-container *ngIf="electionsService.isElections2024Enabled() && (isHome$ | async) === false">
  <section>
    <div class="wrapper">
      <kesma-elections-box
        [link]="electionsService.getElections2024Link()"
        [styleID]="ElectionsBoxStyle.HEADER"
        class="header-elections-diverter"
      ></kesma-elections-box>
    </div>
  </section>
</ng-container>

<kesma-advertisement-adocean *ngIf="isArticleUrl ? adverts?.desktop?.leaderboard_2 : undefined as ad" [ad]="ad"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<section *ngIf="portalName === 'ERDON' && isRadioGagaActive()">
  <div class="wrapper">
    <iframe [src]="radioGagaUrl" class="mobile-only" loading="lazy" style="width: 100%; height: 68px; border: 0"></iframe>
  </div>
</section>
