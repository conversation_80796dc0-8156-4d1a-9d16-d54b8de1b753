<div
  *ngIf="activePage && totalPages"
  class="pager"
  appPagination
  #pagination="appPagination"
  [totalPages]="totalPages"
  [pageNo]="activePage"
  (pageChange)="onPageChange($event)"
>
  <div class="pager">
    <button class="button-arrow prev" type="button" [disabled]="pagination.isFirst" (click)="pagination.prev()"></button>
    <div class="nums">
      <ng-container *ngIf="isOpeningEllipses">
        <button class="button-num more" (click)="onEllipsesLeftClicked()"></button>
      </ng-container>

      <button *ngFor="let page of pages" class="button-num" type="button" [ngClass]="{ active: page === activePage }" (click)="this.onPageChange(page)">
        {{ page }}
      </button>

      <ng-container *ngIf="isClosingEllipses">
        <button class="button-num more" (click)="onEllipsesRightClicked()"></button>
      </ng-container>
    </div>
    <button class="button-arrow next" type="button" [disabled]="pagination.isLast" (click)="pagination.next()"></button>
  </div>
</div>
