@use 'shared' as *;

.pager {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;

  .button-arrow {
    background: $grey-22;
    border: 1px solid $grey-16;
    box-sizing: border-box;
    border-radius: 6px;
    width: 50px;
    height: 50px;

    &.prev {
      margin-right: 10px;
      @include icon('icons/arrow-left.svg');
    }

    &.next {
      margin-left: 10px;
      @include icon('icons/arrow-right.svg');
    }

    &.prev,
    &.next {
      background-size: 45%;
    }

    &[disabled] {
      opacity: 0.3;
    }

    @include media-breakpoint-up(sm) {
      &.prev {
        margin-right: 50px;
      }
      &.next {
        margin-left: 50px;
      }
    }
  }

  .nums {
    display: flex;
    align-items: center;

    .button-num {
      font-size: 16px;
      line-height: 30px;
      margin: 0 12px;
      color: $black;

      &.active {
        font-weight: 800;
        font-size: 18px;
      }

      &.more {
        width: 17px;
        height: 17px;
        @include icon('more.svg');
        content: ' ';
        margin-top: 10px;
      }
    }
  }
}
