import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { PaginationDirective } from '../../directives';
import { Ng<PERSON><PERSON>, NgFor, NgIf } from '@angular/common';

const MAX_DISPLAYED_PAGES = 5;

@Component({
  selector: 'app-pager',
  templateUrl: './pager.component.html',
  styleUrls: ['./pager.component.scss'],
  imports: [NgIf, NgFor, NgClass, PaginationDirective],
})
export class PagerComponent implements OnInit, OnChanges {
  @Input() rowAllCount: number;
  @Input() rowOnPageCount: number;
  @Input() currentPage = 0;
  @Input() maxDisplayedPages = MAX_DISPLAYED_PAGES;
  @Output() pageChange = new EventEmitter<number>();
  @Input() allowAutoScrollToTop = true;
  @Input() pageMax: number;

  totalPages = 1;
  activePage = 1;
  pages: number[] = [];
  isOpeningEllipses = false;
  isClosingEllipses = false;
  numberOfClickablePages = 5;

  ngOnInit(): void {
    this.initState();
  }

  onPageChange(pageNo: number): void {
    this.activePage = pageNo;
    this.scrollTopAfterChange();
    this.calculationLogic(this.activePage);
    this.pageChange.emit(pageNo);
  }

  onEllipsesLeftClicked(): void {
    this.onPageChange(this.pages[0] - 1);
  }

  onEllipsesRightClicked(): void {
    this.onPageChange(this.pages[this.pages.length - 1] + 1);
  }

  ngOnChanges({ rowAllCount, rowOnPageCount, currentPage, pageMax }: SimpleChanges): void {
    if (rowAllCount || rowOnPageCount || currentPage || pageMax) {
      this.initState();
    }
  }

  private initState(): void {
    this.activePage = this.currentPage + 1;
    if (this.pageMax !== undefined) {
      this.totalPages = this.pageMax + 1;
    } else {
      this.totalPages = Math.ceil(this.rowAllCount / this.rowOnPageCount);
    }
    this.calculationLogic(this.activePage);
  }

  private calculationLogic(pageNo: number): void {
    this.pages = this.calculateVisiblePages(pageNo, this.totalPages, this.numberOfClickablePages);
    const [isOpeningEllipses, isClosingEllipses] = this.calculateIsEllipses(pageNo, this.totalPages, this.numberOfClickablePages);
    this.isOpeningEllipses = isOpeningEllipses;
    this.isClosingEllipses = isClosingEllipses;
  }

  private calculateVisiblePages(current: number, total: number, max: number): number[] {
    const pageNumbers = [];
    const half = Math.floor(max / 2);
    const to = current + half >= total ? total : current > half ? current + half : Math.min(max, total);

    let i = to - max >= 0 ? to - max : 0;
    while (i <= to - 1) {
      pageNumbers.push(i + 1);
      i += 1;
    }
    return pageNumbers;
  }

  private calculateIsEllipses(current: number, total: number, max: number): [boolean, boolean] {
    const halfWay = Math.ceil(max / 2);
    const isStart = current <= halfWay;

    const isEnd = total - halfWay < current;
    const isEllipses = max < total;
    const isMiddle = !isEnd && !isStart;

    let isOpeningEllipses = false;
    let isClosingEllipses = false;

    if (isEllipses) {
      isOpeningEllipses = isMiddle || isEnd;
      isClosingEllipses = isMiddle || isStart;
    }
    return [isOpeningEllipses, isClosingEllipses];
  }

  private scrollTopAfterChange(): void {
    if (this.allowAutoScrollToTop) {
      window.scrollTo(0, 0);
    }
  }
}
