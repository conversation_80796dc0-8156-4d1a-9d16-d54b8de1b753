import { Component, Input, OnInit } from '@angular/core';
import { VideoComponentObject } from '../../definitions';
import { BypassPipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-article-video',
  templateUrl: './article-video.component.html',
  styleUrls: ['./article-video.component.scss'],
  imports: [BypassPipe],
})
export class ArticleVideoComponent implements OnInit {
  @Input() componentObject: VideoComponentObject;

  videaUrl: string;

  ngOnInit(): void {
    this.videaUrl = this.componentObject?.videaUrl ?? '';
  }

  getVideaUrl(url: string): string {
    if (url.startsWith('//videa.hu/player?v=')) {
      return url;
    }
    const urlSplit = url.split('-');
    return `//videa.hu/player?v=${urlSplit[urlSplit.length - 1]}`;
  }
}
