@use 'shared' as *;

.container {
  display: flex;
  flex-direction: column;
  margin: auto;
  justify-content: center;
  align-content: center;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 40px;

  .top-text {
    font-size: 16px;
    margin-bottom: 20px;
    font-weight: 600;
  }

  h1 {
    font-size: 105px;
    font-weight: 300;
    margin-bottom: 20px;
  }

  .not-found {
    font-size: 21px;
    margin-bottom: 15px;
    font-weight: 300;
  }

  .message {
    margin-bottom: 15px;
    font-weight: 300;
  }

  a {
    button {
      border-radius: 6px;
      height: 50px;
      padding: 14px 15px;
      margin-top: 20px;
      max-width: 100%;
      font-weight: 500;
      font-size: 14px;
      color: $black;
      background: $grey-22;
      border: 1px solid $grey-16;
      height: 50px;
    }
  }
}
