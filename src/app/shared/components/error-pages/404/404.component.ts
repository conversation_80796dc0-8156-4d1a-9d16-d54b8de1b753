import { Component, Inject, OnInit, Optional } from '@angular/core';
import type { Response } from 'express';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';
import { PortalConfigService } from '../../../services';
import { defaultMetaInfo } from '../../../constants';

@Component({
  selector: 'app-404',
  templateUrl: './404.component.html',
  styleUrls: ['./404.component.scss'],
  imports: [RouterLink],
})
export class Error404Component implements OnInit {
  constructor(
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly portalConfig: PortalConfigService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  ngOnInit(): void {
    this.seo.setMetaData({
      ...defaultMetaInfo(this.portalConfig),
      ogTitle: `${this.portalConfig?.portalName} - 404`,
    });
    if (!this.utilsService.isBrowser() && this.response) {
      this.response.status(404);
    }
  }
}
