import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Observable, take } from 'rxjs';
import { SafeResourceUrl } from '@angular/platform-browser';
import { AsyncPipe, NgClass, NgIf } from '@angular/common';
import { BypassPipe, RadioStoreService } from '@trendency/kesma-ui';
import { MediaStreamApi } from '../../definitions';
import { PortalConfigService } from '../../services';

@Component({
  selector: 'app-media-stream',
  templateUrl: './media-stream.component.html',
  styleUrls: ['./media-stream.component.scss'],
  imports: [NgIf, NgClass, AsyncPipe, BypassPipe],
})
export class MediaStreamComponent implements OnInit {
  @Input() mediaStream: MediaStreamApi;
  @Input() isRadioGagaActive = false;
  @Input() radioGagaUrl: SafeResourceUrl;
  @Output() karcFmToggle = new EventEmitter();
  @Output() retroRadioToggle = new EventEmitter();
  karcPlaying: Observable<boolean>;
  retroPlaying: Observable<boolean>;
  hirtvPlaying = false;
  portalName: string;

  constructor(
    private readonly radioStore: RadioStoreService,
    private readonly portalConfig: PortalConfigService
  ) {
    this.portalName = this.portalConfig.portalName;
  }

  ngOnInit(): void {
    this.karcPlaying = this.radioStore.createPlayer('karc');
    this.retroPlaying = this.radioStore.createPlayer('retro');
  }

  onClickKarc(): void {
    this.karcFmToggle.emit(true);
    this.karcPlaying.pipe(take(1)).subscribe((isPlaying) => {
      if (isPlaying) {
        this.stopKarcFmAudio();
        return;
      }
      this.playKarcFmAudio();
      this.stopRetroRadioAudio();
      this.hirtvPlaying = false;
    });
  }

  onClickRetro(): void {
    this.retroRadioToggle.emit(true);
    this.retroPlaying.pipe(take(1)).subscribe((isPlaying) => {
      if (isPlaying) {
        this.stopRetroRadioAudio();
        return;
      }
      this.playRetroRadioAudio();
      this.hirtvPlaying = false;
      this.stopKarcFmAudio();
    });
  }

  onClickHirtv(): void {
    this.hirtvPlaying = !this.hirtvPlaying;
    this.stopKarcFmAudio();
    this.stopRetroRadioAudio();
  }

  playKarcFmAudio(): void {
    this.radioStore.playPlayer('karc', this.mediaStream.karcFm.stream);
  }

  stopKarcFmAudio(): void {
    this.radioStore.pausePlayer('karc');
  }

  playRetroRadioAudio(): void {
    this.radioStore.playPlayer('retro', this.mediaStream.retroRadio?.stream ?? 'https://icast.connectmedia.hu/5001/live.mp3');
  }

  stopRetroRadioAudio(): void {
    this.radioStore.pausePlayer('retro');
  }
}
