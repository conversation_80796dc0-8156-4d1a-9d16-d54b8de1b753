@use 'shared' as *;

:host {
  display: flex;
  align-items: center;
}

.desktop-only {
  @include media-breakpoint-down(md) {
    display: none;
  }
}

.mobile-only {
  @include media-breakpoint-up(lg) {
    display: none;
  }
}

.tv-widget {
  position: relative;

  .tv-overlay {
    position: absolute;
    z-index: 10;
    top: 0;
    right: 0px;
    box-shadow: 0 0 10px rgba($black, 0.2);
    border-radius: 1px;
    background-color: $white-4;
    padding: 10px;

    .tv-overlay-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 9px;

      .left {
        width: calc(100% - 25px);
        font-size: 16px;
        font-weight: 400;
        color: $grey-3;

        .title {
          color: $blue;
          text-transform: uppercase;
          font-weight: 500;
          margin-bottom: 2px;
        }

        .lead {
          font-family: $font-secondary;
        }
      }

      .right {
        .close-button {
          width: 15px;
          height: 15px;
          margin: 3px;
          @include icon('icons/x-lite.svg');
        }
      }
    }

    .video-frame {
      border: 0;
      width: 352px;
      height: 198px;
    }
  }
}
