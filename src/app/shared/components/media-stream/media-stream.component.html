<iframe
  *ngIf="portalName === 'ERDON' && isRadioGagaActive"
  [src]="radioGagaUrl"
  class="desktop-only"
  loading="lazy"
  style="width: 100%; height: 68px; border: 0; margin-right: 20px"
>
</iframe>
@if (portalName !== 'ERDON') {
  <div class="retro-radio-widget desktop-only">
    <button
      (click)="onClickRetro()"
      [ngClass]="{
        play: retroPlaying | async,
        pause: (retroPlaying | async) === false,
      }"
      class="retro-button"
    >
      <ng-content data-table-footer select="[media-retro-radio-button]"></ng-content>
    </button>
  </div>
  <div *ngIf="mediaStream?.karcFm?.stream" class="radio-widget desktop-only">
    <button (click)="onClickKarc()" [ngClass]="{ play: karcPlaying | async, pause: (karcPlaying | async) === false }" class="karc-button">
      <ng-content data-table-footer select="[media-karc-fm-radio-button]"></ng-content>
    </button>
  </div>
  <div *ngIf="mediaStream?.hirTv?.stream" class="tv-widget">
    <a (click)="onClickHirtv()" class="link-with-icon">
      <ng-content data-table-footer select="[media-tv-button]"></ng-content>
    </a>
    <div *ngIf="hirtvPlaying" class="tv-overlay">
      <div class="tv-overlay-header">
        <div class="left">
          <div class="title">{{ mediaStream.hirTv.title }}</div>
          <div class="lead">{{ mediaStream.hirTv.subTitle }}</div>
        </div>
        <div class="right">
          <button (click)="onClickHirtv()" class="close-button"></button>
        </div>
      </div>
      <iframe
        [src]="mediaStream.hirTv.stream | bypass: 'resourceUrl'"
        allowfullscreen="true"
        class="video-frame"
        frameborder="0"
        id="videoplayer_253275"
        mozallowfullscreen="true"
        webkitallowfullscreen="true"
      ></iframe>
    </div>
  </div>
}
