@use 'shared' as *;

.real-estate {
  margin: 0;
  @include media-breakpoint-down(sm) {
    margin: 40px -15px;
  }

  .real-estate-wrapper {
    border-top: 1px solid $red-2;
    border-bottom: 1px solid $grey-16;
    padding: 15px 0;
    @include media-breakpoint-down(sm) {
      padding: 15px;
    }
  }

  .real-estate-header {
    display: flex;
    align-items: flex-end;
    margin-bottom: 50px;

    .real-estate-logo {
      width: 192px;
      height: 34px;
      width: auto;
    }
  }

  .real-estate-more {
    display: inline-flex;
    margin-left: auto;
    font-weight: 600;
    font-size: 14px;
    border-radius: 6px;
    height: 30px;
    color: $white;
    justify-content: center;
    align-items: center;
    padding: 0 18px;
    background-color: $red-2;
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  .real-estate-more-icon {
    @include icon('icons/foreign-link.svg');
    width: 18px;
    height: 18px;
    margin-left: 9px;
  }

  .real-estate-tabs {
    display: flex;
    margin-bottom: 10px;
  }

  .real-estate-tabs-item {
    margin-right: 24px;

    &:last-child {
      margin-right: 0;
    }
  }

  .real-estate-tabs-button {
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    position: relative;
    color: $grey-7;

    &.is-active {
      color: $red-2;

      &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 3px;
        background-color: $red-2;
        bottom: -1px;
        left: 0;
      }
    }
  }

  .real-estate-more-wrapper {
    display: none;
    margin-top: 10px;

    @include media-breakpoint-down(sm) {
      display: block;

      .real-estate-more {
        display: flex;
        width: 100%;
        height: 50px;
      }
    }
  }

  .real-estate-item-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 -15px;
    @include media-breakpoint-down(xs) {
      flex-wrap: nowrap;
      overflow: auto;
      padding: 0 15px;
    }
  }

  .real-estate-item {
    width: 190px;
    flex-grow: 1;
    margin: 10px 15px 0 15px;

    @include media-breakpoint-down(lg) {
      width: calc(25% - 30px);
    }

    @include media-breakpoint-down(sm) {
      width: calc(33% - 30px);
    }
    @include media-breakpoint-down(xs) {
      width: auto;
      flex: 1 0 200px;
      margin: 0 15px 0 0;
      &:last-child {
        padding-right: 5px;
      }
    }
  }
}
