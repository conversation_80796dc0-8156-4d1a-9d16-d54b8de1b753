<section class="real-estate" *ngIf="realEstateBazaarApiDataArr?.length > 0">
  <div class="wrapper">
    <div class="real-estate-wrapper">
      <div class="real-estate-header">
        <img trImageLazyLoad class="real-estate-logo" src="./assets/images/ingatlanbazar-header.svg" alt="Ingatlanbazár" />
        <a href="https://www.ingatlanbazar.hu/" target="_blank" class="real-estate-more">Még több ingatlan <i class="real-estate-more-icon"></i></a>
      </div>
      <ul class="real-estate-tabs">
        <li class="real-estate-tabs-item">
          <button class="real-estate-tabs-button" [ngClass]="{ 'is-active': activeTabIsForSale }" (click)="handleTabClick('forSale')">Eladó</button>
        </li>
        <li class="real-estate-tabs-item">
          <button class="real-estate-tabs-button" [ngClass]="{ 'is-active': !activeTabIsForSale }" (click)="handleTabClick('forRent')">Kiad<PERSON></button>
        </li>
      </ul>
      <div class="real-estate-body">
        <div class="real-estate-item-container">
          <ng-container *ngFor="let item of filtered">
            <div class="real-estate-item">
              <app-real-estate-bazaar-card [data]="item"></app-real-estate-bazaar-card>
            </div>
          </ng-container>
        </div>
      </div>
      <div class="real-estate-more-wrapper">
        <a href="https://www.ingatlanbazar.hu/" target="_blank" class="real-estate-more">Még több ingatlan <i class="real-estate-more-icon"></i></a>
      </div>
    </div>
  </div>
</section>
