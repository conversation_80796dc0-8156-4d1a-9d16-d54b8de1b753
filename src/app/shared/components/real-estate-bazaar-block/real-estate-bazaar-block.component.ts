import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { catchError, takeUntil } from 'rxjs/operators';
import { of, Subject } from 'rxjs';
import { RealEstateBazaarCardComponent } from '../real-estate-bazaar-card/real-estate-bazaar-card.component';
import { NgClass, NgFor, NgIf } from '@angular/common';
import { ImageLazyLoadDirective } from '@trendency/kesma-core';
import { RealEstateBazaarApiService } from '../../services';
import { RealEstateBazaarApiData } from '@trendency/kesma-ui';

@Component({
  selector: 'app-real-estate-bazaar-block',
  templateUrl: './real-estate-bazaar-block.component.html',
  styleUrls: ['./real-estate-bazaar-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, Ng<PERSON><PERSON>, <PERSON>For, RealEstateBazaarCardComponent, ImageLazyLoadDirective],
})
export class RealEstateBazaarBlockComponent implements OnInit, OnDestroy {
  realEstateBazaarApiDataArr: RealEstateBazaarApiData[] = [];
  filtered: RealEstateBazaarApiData[] = [];
  activeTabIsForSale = true;
  private readonly destroySubject: Subject<boolean> = new Subject<boolean>();

  constructor(
    private readonly realEstateBazaarApiService: RealEstateBazaarApiService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.realEstateBazaarApiService
      .getRealEstateBazaarItems()
      .pipe(
        catchError(() => {
          return of([]);
        }),
        takeUntil(this.destroySubject)
      )
      .subscribe((apiResponse: RealEstateBazaarApiData[]) => {
        this.realEstateBazaarApiDataArr = apiResponse;
        this.filterByClassPropEndingWith('_elado');
        this.changeRef.detectChanges();
      });
  }

  handleTabClick(value: string): void {
    if (value === 'forSale') {
      this.activeTabIsForSale = true;
      this.filterByClassPropEndingWith('_elado');
    } else {
      this.activeTabIsForSale = false;
      this.filterByClassPropEndingWith('_kiado');
    }
    this.changeRef.detectChanges();
  }

  filterByClassPropEndingWith(type: string): void {
    this.filtered = (this.realEstateBazaarApiDataArr || []).filter((item: RealEstateBazaarApiData) =>
      (
        item as RealEstateBazaarApiData & {
          class: string;
        }
      ).class.endsWith(type)
    );
    this.changeRef.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroySubject.next(true);
    this.destroySubject.complete();
  }
}
