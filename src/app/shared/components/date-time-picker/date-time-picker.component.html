@if (formGroup && controlName) {
  <div [formGroup]="formGroup">
    @if (inline) {
      <input [formControlName]="controlName" [id]="id" class="flatpickr inline" type="text" />
    } @else {
      <!-- Not in use for now -->
      <kesma-form-control>
        <label *ngIf="label" for="{{ id }}">{{ label }}</label>
        <input [formControlName]="controlName" [id]="id" class="flatpickr" type="text" />
        <img class="icon" src="/assets/images/icons/calendar.svg" alt="" />
      </kesma-form-control>
    }
  </div>
}

<div #customContent class="flatpickr-custom-content">{{ currentYear }}. {{ currentMonth }}</div>

<div #customFooter class="flatpickr-custom-footer" [ngClass]="{ inline: inline }">
  <button (click)="close()"><PERSON>z<PERSON>rás</button>
  <button (click)="now()">Ma</button>
</div>
