@use 'shared' as *;

.flatpickr {
  img.icon {
    width: 24px;
    height: 24px;
  }

  &.inline {
    display: none;
  }

  &-custom-content {
    font-weight: bold;
    position: absolute;
    left: 0;
    right: 0;
    top: 7px;
  }

  &-custom-footer {
    display: flex;
    align-items: center;
    border-top: 1px solid $grey-16;
    padding: 10px;
    gap: 10px;

    button {
      padding: 7px 16px;
    }

    &.inline {
      display: none;
    }
  }
}

.flatpickr-calendar {
  border: 1px solid $grey-16;
  border-radius: 6px;

  &.inline {
    box-shadow: none;
    top: 0;
  }

  &:before,
  &:after {
    display: none;
  }

  .flatpickr-months {
    .flatpickr-month {
      height: 40px;
    }

    .flatpickr-prev-month,
    .flatpickr-next-month {
      top: 0;
      margin: 0 10px;
      padding: 5px;

      svg {
        width: 10px;
        height: 10px;

        &:hover {
          fill: var(--link-color);
        }
      }
    }
  }

  .flatpickr-innerContainer {
    margin-left: -1px;

    .flatpickr-rContainer {
      .flatpickr-weekdays {
        .flatpickr-weekdaycontainer {
          .flatpickr-weekday {
            color: var(--kui-black);
            font-family: var(--kui-font-primary);
          }
        }
      }

      .flatpickr-days {
        .dayContainer {
          .flatpickr-day {
            font-family: var(--kui-font-primary);

            &.selected {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
              border-radius: 50%;
            }

            &.today {
              border: none;
            }

            &:hover:not(.selected) {
              background-color: $grey-19;
              border-color: $grey-19;
              color: var(--kui-black);
            }

            &.flatpickr-disabled {
              color: $grey-19;
            }
          }
        }
      }
    }
  }

  .flatpickr-time {
    border-color: var(--kui-gray-300);

    .numInputWrapper {
      input {
        color: var(--kui-black);
        font-family: var(--kui-font-primary);

        &:hover,
        &:focus {
          background-color: var(--kui-blue-200);
        }
      }
    }
  }
}
