import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { DateTimePickerComponent as KesmaDateTimePickerComponent, KesmaFormControlComponent } from '@trendency/kesma-ui';
import { FormsModule, ReactiveFormsModule, UntypedFormGroup } from '@angular/forms';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-date-time-picker',
  templateUrl: 'date-time-picker.component.html',
  styleUrls: ['../../../../../node_modules/flatpickr/dist/flatpickr.min.css', './date-time-picker.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, KesmaFormControlComponent, NgClass],
})
export class DateTimePickerComponent extends KesmaDateTimePickerComponent {
  @Input() formGroup?: UntypedFormGroup;
  @Input() controlName?: string;

  @Input() label?: string;
}
