@use 'shared' as *;

.sensitive-content {
  background-color: $black;
  padding: 90px 0;

  @include media-breakpoint-down(md) {
    padding: 20px 0 0;
  }

  .small-wrapper {
    width: 680px;
    max-width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    @include media-breakpoint-down(md) {
      flex-direction: column;
      align-items: center;
      padding: 0 20px;
    }

    .sensitive-content-icon {
      font-size: 50px;
      line-height: 40px;
      color: $white;
      border-radius: 50%;
      height: 120px;
      width: 120px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      border: 10px solid $red;
      box-sizing: border-box;
      @include media-breakpoint-down(md) {
        margin-bottom: 10px;
      }
    }

    .sensitive-content-text-column {
      width: 480px;
      max-width: 100%;
      margin-bottom: 40px;
      color: $white;

      h1 {
        font-weight: 500;
        font-size: 30px;
        line-height: 45px;
      }

      h2 {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 24px;
      }

      p {
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: $grey-16;
      }
    }

    .btn {
      width: 320px;
      max-width: 100%;
      padding: 20px;
      text-align: center;
      display: block;
      font-size: 14px;
      line-height: 21px;
      font-weight: 500;
      @include media-breakpoint-down(md) {
        margin-bottom: 20px;
      }
    }
  }
}
