<app-header
  [breakingNews]="breakingNews"
  [hidden]="(showHeader$ | async) === false"
  [mediaStream]="mediaStream"
  [menuHeader]="header"
  [menuLeft]="headerLeft"
  [menuRight]="headerRight"
  [menuSecondary]="headerSecondary"
  [namedays]="namedays"
  [adverts]="adverts"
  [isArticleUrl]="isArticleUrl"
  [url]="url"
  [weatherInfo]="weather"
></app-header>
<router-outlet></router-outlet>

<kesma-advertisement-adocean *ngIf="isArticleUrl ? undefined : adverts?.desktop?.leaderboard_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.desktop?.leaderboard_footer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_footer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<app-footer [bottomMenu]="footerBottom" [menu]="footerTop"></app-footer>
<app-mediaworks *ngIf="!isErdon"></app-mediaworks>
