import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  BreakingNews,
  PAGE_TYPES,
  SecondaryFilterAdvertType,
} from '@trendency/kesma-ui';
import { chunk } from 'lodash-es';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { delay, filter, map, mergeMap, startWith } from 'rxjs/operators';
import { SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';
import { MediaworksComponent } from '../footer/mediaworks/mediaworks.component';
import { InitResolverData, InitWeather, MainMenuChild, MediaStreamApi, MenuChild } from '../../definitions';

import { PortalConfigService } from '../../services';

const SECONDARY_MENU_ITEMS_PER_COLUMN = 7;

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [HeaderComponent, RouterOutlet, NgIf, FooterComponent, MediaworksComponent, AsyncPipe, AdvertisementAdoceanComponent],
})
export class BaseComponent implements OnInit {
  breakingNews: BreakingNews;
  mediaStream: MediaStreamApi;
  namedays: string | undefined;
  header: MenuChild[] = [];
  headerLeft: MenuChild[] = [];
  headerRight: MenuChild[] = [];
  headerSecondary: MainMenuChild[] = [];
  footerTop: MenuChild[] = [];
  footerBottom: MenuChild[] = [];
  weather: InitWeather | undefined;
  adverts?: AdvertisementsByMedium;
  dontMissThisArticle:
    | {
        columnSlug: string;
        publishDate: string;
        slug: string;
        title: string;
      }
    | undefined;
  isArticleUrl: boolean;
  url: string;
  categorySlug: string;
  private readonly showHeaderSubject$ = new BehaviorSubject<boolean>(true);
  showHeader$: Observable<boolean> = this.showHeaderSubject$.asObservable();
  isErdon = this.portalConfig.portalName == 'ERDON';

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly storage: StorageService,
    private readonly utils: UtilService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly seoService: SeoService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly portalConfig: PortalConfigService,
    @Inject(DOCUMENT) private readonly _doc: Document
  ) {}

  ngOnInit(): void {
    if (!this.utils.isBrowser()) {
      return;
    }

    const responseData: InitResolverData = this.route.snapshot.data?.['data'] || {};
    const {
      menu: { header, header_0: headerLeft, header_1: headerRight, secondary_header: headerSecondary, footer_0: footerTop, footer_1: footerBottom },
      init: { menuSettings: mediaApiData, idojaras: weather, nameDays, dontMissThisArticle },
    } = { menu: { ...(responseData?.menu ?? {}) }, init: { ...(responseData?.init ?? {}) } } as InitResolverData;

    this.dontMissThisArticle = dontMissThisArticle;
    this.breakingNews = responseData?.init?.breakingNews;
    this.storage.setLocalStorageData('dontMissThisArticle', dontMissThisArticle);
    this.mediaStream = mediaApiData;
    this.header = header;
    this.headerLeft = headerLeft;
    this.headerRight = headerRight;
    this.headerSecondary = (headerSecondary ?? []).map((menu) => ({
      ...menu,
      columns: chunk(menu.children, SECONDARY_MENU_ITEMS_PER_COLUMN),
    }));
    this.footerTop = footerTop;
    this.footerBottom = footerBottom;
    this.weather = weather;
    this.namedays = nameDays;

    combineLatest([
      this.adStoreAdo.isArticleLoaded$,
      this.adStoreAdo.advertMeta$,
      this.adStoreAdo.articleParentCategory$,
      this.router.events.pipe(
        filter((event) => event instanceof NavigationEnd),
        startWith(this.router)
      ),
    ])
      .pipe(
        mergeMap(() => {
          if (!this.utils.isBrowser() || !this._doc?.location) {
            return of({} as AdvertisementsByMedium);
          }

          this.seoService.updateCanonicalUrl(this._doc?.location.href, { addHostUrl: false, skipSeoMetaCheck: true });
          this.modifyHeader(this._doc?.location.href);

          const [_, path1, path2] = this._doc?.location?.pathname.split('/') ?? ['', ''];
          this.isArticleUrl = !isNaN(parseInt(path2, 10));
          this.resetAds();

          const parentCategory = this.adStoreAdo.articleParentCategory$.getValue();

          this.categorySlug = parentCategory || `column_${path2}`;

          return this.isArticleUrl && !this.adStoreAdo.isArticleLoaded$.getValue()
            ? of(null)
            : this.adStoreAdo.advertisemenets$.pipe(
                // We need delay to reinitialize the header adverts, otherwise this.resetAds function will not have any effect.
                delay(0),
                map((ads) => {
                  const { pageType } = this.baseElementPageTypeSwitch(path1) || {};

                  return this.adStoreAdo.separateAdsByMedium(ads, pageType, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE);
                })
              );
        })
      )
      .subscribe((ads) => {
        if (!ads) {
          return;
        }

        this.adverts = ads;

        this.url = this._doc?.location?.pathname;
        this.changeRef.detectChanges();
      });
  }

  modifyHeader(url: string): void {
    if (!url) {
      return;
    }

    if (url.includes('/galeria/')) {
      this.showHeaderSubject$.next(false);
    } else {
      this.showHeaderSubject$.next(true);
    }
  }

  private baseElementPageTypeSwitch(path: string): { pageType: string } {
    if (this.isArticleUrl) {
      return { pageType: this.categorySlug };
    }

    switch (path) {
      case '':
        return { pageType: PAGE_TYPES.main_page };
      case 'galeriak':
      case 'galeria':
        return { pageType: 'gallery' };
      case 'rovat':
        return { pageType: this.categorySlug };
      case 'gyasz':
        return { pageType: `grief` };
      case 'PR':
        return { pageType: `PR` };
      default:
        return { pageType: PAGE_TYPES.all_articles_and_sub_pages };
    }
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.changeRef.detectChanges();
  }
}
