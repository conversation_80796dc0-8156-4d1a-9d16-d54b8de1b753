<section class="section-header">
  <div class="section-header-block">
    <h2 class="section-header-title">{{ sectionTitle }}</h2>
    <a *ngIf="link" class="section-header-link section-header-more" [routerLink]="link">
      {{ linkTitle }}

      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 8H15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M8 1L15 8L8 15" stroke="#005CA2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </a>
  </div>
</section>
