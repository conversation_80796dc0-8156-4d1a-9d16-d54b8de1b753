@use 'shared' as *;

.section-header {
  margin-bottom: 30px;
  background-color: $grey-22;

  .section-header-block {
    min-height: 60px;
    padding-right: 25px;
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 20px;
    @include article-before-line();

    &:before {
      background: linear-gradient(0deg, $primary-color 0%, $secondary-color 100%);
    }
  }

  .section-header-title {
    @include article-before-title-style;
  }

  .section-header-link {
    display: table;
  }

  .section-header-more {
    @include read-more-blue-arrow();
    margin-left: auto;
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }
}
