@use 'shared' as *;

.region-sidebar {
  border: 1px solid $grey-16;
  border-radius: 6px;
  padding: 40px;
  @include media-breakpoint-down(sm) {
    padding: 15px;
  }

  .map-wrapper {
    margin-bottom: 10px;

    .google-maps {
      display: flex;

      @include media-breakpoint-down(sm) {
        justify-content: center;
        align-content: center;
      }

      .google-maps-map {
        width: 361px;
        height: 342px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        @include media-breakpoint-down(sm) {
          width: 260px;
          height: 260px;
        }
      }
    }
  }

  ::ng-deep {
    .info {
      .label,
      strong {
        margin-top: 20px;
        margin-bottom: 3px;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 140%;
        text-transform: uppercase;
        color: $grey-7;
      }

      .information,
      p {
        font-weight: normal;
        font-size: 20px;
        line-height: 30px;
      }
    }
  }
}
