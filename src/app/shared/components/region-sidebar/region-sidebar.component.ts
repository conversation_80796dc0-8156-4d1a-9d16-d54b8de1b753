import { Component, Input } from '@angular/core';
import { GoogleMapsComponent } from '../google-maps/google-maps.component';
import { NgIf } from '@angular/common';
import { SafePipe } from '@trendency/kesma-core';
import { Region } from '../../../feature/region/region.definitions';

@Component({
  selector: 'app-region-sidebar',
  templateUrl: './region-sidebar.component.html',
  styleUrls: ['./region-sidebar.component.scss'],
  imports: [NgIf, GoogleMapsComponent, SafePipe],
})
export class RegionSidebarComponent {
  @Input() region: Region;
}
