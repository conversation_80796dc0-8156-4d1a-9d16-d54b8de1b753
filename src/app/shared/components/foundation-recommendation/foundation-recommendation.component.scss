@use 'shared' as *;

.foundation-recommendation {
  margin-top: 20px;

  ::ng-deep {
    .article-card.style-2 {
      @include media-breakpoint-down(sm) {
        margin-left: initial !important;
      }
    }
  }

  h4 {
    margin-bottom: 20px;

    &.side {
      @include media-breakpoint-down(md) {
        margin-top: 20px;
      }
    }
  }

  &-sub {
    padding-top: var(--bs-gutter-x);
  }

  &-side {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.more-recommendation {
  margin-bottom: 20px;
  text-align: center;
}
