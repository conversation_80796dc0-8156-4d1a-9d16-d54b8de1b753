<ng-container *ngIf="recommendations && recommendations.length > 0">
  <div class="row foundation-recommendation">
    <div class="col-12 {{ sideRecommendations.length > 0 ? 'col-md-8' : 'col-md-12' }}">
      <h4>Tov<PERSON><PERSON><PERSON> cikkek a témában</h4>
      <div class="row">
        <div class="col-12 foundation-recommendation-main">
          <app-article-card [article]="mainRecommendation" [styleID]="ArticleCardTypes.STYLE2"></app-article-card>
        </div>
        <div *ngFor="let recommendation of subRecommendations" class="col-12 col-md-6 foundation-recommendation-sub">
          <app-article-card [article]="recommendation" [styleID]="ArticleCardTypes.STYLE8"></app-article-card>
        </div>
      </div>
    </div>
    <ng-container *ngIf="sideRecommendations.length > 0">
      <div class="col-12 col-md-4">
        <h4 class="side"><PERSON><PERSON><PERSON><PERSON><PERSON> hírek a tém<PERSON>ban</h4>
        <div class="foundation-recommendation-side">
          <app-article-card
            *ngFor="let recommendation of sideRecommendations"
            [article]="recommendation"
            [styleID]="ArticleCardTypes.STYLE3"
          ></app-article-card>
        </div>
      </div>
    </ng-container>
  </div>

  <div class="more-recommendation">
    <a [routerLink]="['/', 'cimke', foundationTagSlug]"> Még több cikk </a>
  </div>
</ng-container>
