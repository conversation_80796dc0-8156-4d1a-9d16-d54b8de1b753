import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import uniqBy from 'lodash-es/uniqBy';
import { RouterLink } from '@angular/router';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { NgF<PERSON>, NgIf } from '@angular/common';
import { ArticleCard, Tag } from '@trendency/kesma-ui';
import { ArticleCardTypes } from '../../definitions';
import { ApiService } from '../../services';
import { mapSearchResultToArticleCard } from '../../utils';
import { ArticleSearchResult } from '../../../feature/article/article.definitions';

@Component({
  selector: 'app-foundation-recommendation',
  templateUrl: './foundation-recommendation.component.html',
  styleUrls: ['./foundation-recommendation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ArticleCardComponent, NgFor, RouterLink],
})
export class FoundationRecommendationComponent implements OnInit {
  @Input() foundationTagSlug: string;
  @Input() tags: Tag[];
  @Input() articleSlug: string;
  @Input() isSidebar = false;

  recommendations: ArticleCard[] = [];
  ArticleCardTypes = ArticleCardTypes;

  constructor(
    private readonly apiService: ApiService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  get mainRecommendation(): ArticleCard | undefined {
    return this.recommendations.length > 0 ? this.recommendations[0] : undefined;
  }

  get subRecommendations(): ArticleCard[] {
    return this.recommendations.length > 1 ? this.recommendations.slice(1, 5) : [];
  }

  get sideRecommendations(): ArticleCard[] {
    return this.recommendations.length > 5 ? this.recommendations.slice(5, 11) : [];
  }

  ngOnInit(): void {
    this.getArticlesByFoundationTagSlug();
  }

  getArticlesByFoundationTagSlug(): void {
    this.apiService.searchArticleByTags([this.foundationTagSlug], 0, 12).subscribe((res) => {
      this.recommendations = res.data
        .filter((a: ArticleSearchResult) => a.slug !== this.articleSlug)
        .map((searchResult) => mapSearchResultToArticleCard(searchResult));

      if (this.recommendations.length < 11) {
        this.getMoreArticlesByTags();
      } else {
        this.cdr.detectChanges();
      }
    });
  }

  getMoreArticlesByTags(): void {
    this.apiService
      .searchArticleByTags(
        this.tags.map((tag) => tag.slug),
        0,
        12
      )
      .subscribe((res2) => {
        this.recommendations = uniqBy(
          this.recommendations.concat(res2.data.filter((a) => a.slug !== this.articleSlug).map((searchResult) => mapSearchResultToArticleCard(searchResult))),
          'id'
        );
        this.cdr.detectChanges();
      });
  }
}
