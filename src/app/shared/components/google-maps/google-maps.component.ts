import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { environment } from 'src/environments/environment';

export type GoogleMapsOptions = Readonly<{
  lat?: number;
  lng?: number;
  zoom?: number;
  iconUrl?: string;
  iconWidth?: number;
  iconHeight?: number;
  disableDefaultUI: boolean;
}>;

@Component({
  selector: 'app-google-maps',
  templateUrl: './google-maps.component.html',
  styleUrls: ['./google-maps.component.scss'],
})
export class GoogleMapsComponent implements OnInit {
  // Adj hozzá nyugodtan bármit az interfacehez ha kell még plusz dolog
  @Input() googleMapsOptions: GoogleMapsOptions;

  mapsUrl: any;

  @Input() hasSearchIcon = false;

  constructor(private readonly sanitizer: DomSanitizer) {}

  ngOnInit(): void {
    const { lat, lng, zoom } = this.googleMapsOptions;

    this.mapsUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
      `https://www.google.com/maps/embed/v1/place?key=${environment.googleMapsKey}&q=+${lat},+${lng}&zoom=${zoom}&language=hu`
    );
  }
}
