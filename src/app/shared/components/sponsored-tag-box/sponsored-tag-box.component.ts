import { Component, computed, effect, HostBinding, inject, input, signal } from '@angular/core';
import { take } from 'rxjs';
import { ApiService } from '../../services';
import { ArticleCard, BackendArticleSearchResult, mapBackendArticleDataToArticleCard, SponsoredTag } from '@trendency/kesma-ui';
import { ArticleCardTypes } from '../../definitions';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { SlicePipe } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-sponsored-tag-box',
  imports: [ArticleCardComponent, SlicePipe, RouterLink],
  templateUrl: './sponsored-tag-box.component.html',
  styleUrl: './sponsored-tag-box.component.scss',
  host: {
    '[class.hidden]': '!articles()?.length',
  },
})
export class SponsoredTagBoxComponent {
  private readonly api = inject(ApiService);
  articles = signal<ArticleCard[]>([]);
  articlesCount = signal<number>(0);

  sponsoredTag = input.required<SponsoredTag>();
  excludedSlug = input.required<string>();

  slogan = computed(() => this.sponsoredTag().slogan);
  tagSlug = computed(() => this.sponsoredTag().tagSlug);
  textColor = computed(() => this.sponsoredTag().fontColor);
  backgroundColor = computed(() => this.sponsoredTag().backgroundColor);
  logoUrl = computed(() => this.sponsoredTag().logoUrl);
  logoWidth = computed(() => (this.sponsoredTag().logoWidth ? `${this.sponsoredTag().logoWidth}px` : 0));
  logoHeight = computed(() => (this.sponsoredTag().logoHeight ? `${this.sponsoredTag().logoHeight}px` : 0));
  urlHref = computed(() => this.sponsoredTag().url);

  @HostBinding('style.background-color')
  get componentBackgroundColorGetter(): string {
    return this.backgroundColor() || '';
  }
  @HostBinding('style.border-color')
  get componentBorderColorGetter(): string {
    return this.textColor() || '';
  }

  constructor() {
    effect(() => {
      const slug = this.tagSlug();
      if (!slug) {
        return;
      }
      this.api
        .searchArticleByTags([slug], 0, 5)
        .pipe(take(1))
        .subscribe((res) => {
          let totalCards: ArticleCard[] = [];
          if (this.excludedSlug() && res.data?.length) {
            totalCards = res.data
              .filter((a) => a?.slug !== this.excludedSlug())
              .map((a) => mapBackendArticleDataToArticleCard(a as unknown as BackendArticleSearchResult));
          }
          this.articles.set(totalCards);
          if (res.meta.limitable?.rowAllCount) {
            this.articlesCount.set(res.meta.limitable?.rowAllCount);
          }
        });
    });
  }

  protected readonly ArticleCardType = ArticleCardTypes;
}
