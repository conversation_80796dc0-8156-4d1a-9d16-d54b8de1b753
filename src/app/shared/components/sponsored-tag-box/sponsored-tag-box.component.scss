@use 'shared' as *;

:host {
  display: block;
  border-style: solid;
  border-width: 1px;
  padding: 15px;
  margin-bottom: 30px;
  position: relative;

  &.hidden {
    display: none;
  }

  .slogan {
    margin-bottom: 20px;
    display: block;

    h3 {
      display: inline;
      hyphens: auto;
      transition: all 0.3s;
      text-decoration-color: transparent;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .content {
    display: flex;
    flex-wrap: nowrap;
    gap: 15px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      gap: 20px;
    }

    .article-list {
      min-width: 40%;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .sponsor-container {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      height: fit-content;
      @include media-breakpoint-down(sm) {
        justify-content: center;
      }
      & > div {
        position: relative;
        overflow: hidden;
      }
    }

    .sponsor-logo {
      position: relative;

      img {
        transition: transform 0.3s;
        object-fit: cover;

        &.without-height-and-width {
          aspect-ratio: 16 / 9;
        }

        &:hover {
          transform: scale(1.1);
        }
      }
    }
    .article-count {
      position: absolute;
      bottom: 0;
      right: 0;
      color: var(--kui-white);
      background-color: rgba(0, 0, 0, 0.5);
      padding: 5px 10px;
      font-weight: 500;
    }
  }

  .ad-label {
    position: absolute;
    bottom: -25px;
    left: 0;
    display: block;
    width: 100%;
    text-align: center;
    font-size: 10px;
    opacity: 0.5;
    text-transform: uppercase;
  }
}
