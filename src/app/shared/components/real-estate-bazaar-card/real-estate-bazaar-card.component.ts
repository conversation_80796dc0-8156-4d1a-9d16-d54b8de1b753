import { Component, Input, OnInit } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';
import { RealEstateBazaarApiData } from '../../definitions';

@Component({
  selector: 'app-real-estate-bazaar-card',
  templateUrl: './real-estate-bazaar-card.component.html',
  styleUrls: ['./real-estate-bazaar-card.component.scss'],
  imports: [NgIf],
})
export class RealEstateBazaarCardComponent implements OnInit {
  @Input() data: RealEstateBazaarApiData;

  title: string;

  constructor(private readonly utilsService: UtilService) {}

  ngOnInit(): void {
    this.limitTitleLength(this.data?.title ?? '');
  }

  limitTitleLength(title: string): void {
    if (!this.utilsService.isBrowser() || !title) {
      return;
    }

    const isMobile = window?.innerWidth <= 768;

    const titleLengthOnDevices = isMobile ? [66, 63] : [76, 73];

    this.title = title?.length > titleLengthOnDevices[0] ? `${title.slice(0, titleLengthOnDevices[1])}...` : title;
  }
}
