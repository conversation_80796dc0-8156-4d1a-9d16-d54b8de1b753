<div (click)="onOpen()" [id]="id" class="flatpickr" [ngClass]="{ inline: isInline }">
  @if (fromDate || toDate) {
    <span>{{ fromDate | dfnsFormat: 'yyyy/MM/dd' }} - {{ toDate | dfnsFormat: 'yyyy/MM/dd' }}</span>
    <img class="icon" src="/assets/images/icons/x-lite.svg" alt="" (click)="clear(); $event.stopPropagation()" />
  } @else {
    <span>Dátum</span>
    <img class="icon" src="/assets/images/icons/calendar.svg" alt="" />
  }
</div>

<div #customHeader></div>

<div #customContent class="flatpickr-custom-content">{{ currentYear }} {{ currentMonth | titlecase }}</div>
