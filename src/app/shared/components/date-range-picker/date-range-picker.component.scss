@use 'shared' as *;

.flatpickr {
  border: 1px solid $grey-16;
  border-radius: 6px;
  padding: 14px;
  color: $black-2;
  cursor: pointer;
  min-width: 240px;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &.inline {
    display: none;
  }

  img.icon {
    width: 24px;
    height: 24px;
  }

  &-custom-content {
    font-weight: bold;
    position: absolute;
    left: 0;
    right: 0;
    top: 7px;
  }
}

.flatpickr-calendar {
  .flatpickr-months {
    height: 40px;

    .flatpickr-prev-month,
    .flatpickr-next-month {
      top: 0;
      margin: 0 10px;
      padding: 5px;

      svg {
        width: 10px;
        height: 10px;

        &:hover {
          fill: var(--link-color);
        }
      }
    }
  }

  .flatpickr-innerContainer {
    margin-left: -1px;

    .flatpickr-rContainer {
      .flatpickr-weekdays {
        .flatpickr-weekdaycontainer {
          .flatpickr-weekday {
            color: var(--kui-black);
            font-family: var(--kui-font-primary);
          }
        }
      }

      .flatpickr-days {
        .dayContainer {
          &:not(:has(.endRange)):not(:has(.inRange)) {
            .flatpickr-day.startRange::after {
              background-color: transparent;
            }
          }

          .flatpickr-day {
            font-family: var(--kui-font-primary);

            &.inRange {
              background-color: $grey-19;
              border: 1px solid $grey-19;
              color: var(--kui-black);
            }

            &.inRange:not(.startRange),
            &.inRange:not(.endRange) {
              box-shadow:
                -5px 0 0 $grey-19,
                5px 0 0 $grey-19;
            }

            &.endRange,
            &.startRange {
              background-color: var(--primary-color);
              border-radius: 50%;
              border: none;
              position: relative;

              &::after {
                content: '';
                background-color: $grey-19;
                display: block;
                position: absolute;
                top: 0;
                width: 50%;
                height: 100%;
                right: 25px;
                z-index: -1;
              }
            }

            &.startRange.endRange::after {
              background-color: transparent;
            }

            &.startRange::after {
              right: 0;
            }

            &.endRange:not(:nth-child(7n)):not(.startRange) {
              box-shadow: -5px 0 0 $grey-19;
            }

            &:nth-of-type(7n).endRange,
            &:nth-of-type(7n).inRange {
              box-shadow: -5px 0 0 $grey-19;
              //@include border-bottom-end-radius(50%);
              //@include border-top-end-radius(50%);
            }

            &:nth-of-type(7n + 1).inRange {
              box-shadow: 0 0 0 $grey-19;
              //@include border-bottom-start-radius(50%);
              //@include border-top-start-radius(50%);
            }

            &:nth-of-type(7n + 1).endRange:after {
              background-color: transparent;
            }

            &.endRange:not(:nth-child(7n)).endRange {
              box-shadow: none;
            }

            &.today {
              border: none;
            }

            &.flatpickr-disabled {
              color: $grey-19;
            }
          }
        }
      }
    }
  }
}
