import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { FormatPipeModule } from 'ngx-date-fns';
import { NgClass, TitleCasePipe } from '@angular/common';
import { DateRangePickerComponent as KesmaDateRangePickerComponent } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-date-range-picker',
  templateUrl: 'date-range-picker.component.html',
  styleUrls: ['../../../../../node_modules/flatpickr/dist/flatpickr.min.css', './date-range-picker.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormatPipeModule, TitleCasePipe, NgClass],
})
export class DateRange<PERSON>ickerComponent extends KesmaDateRangePickerComponent implements OnInit, OnD<PERSON>roy {
  updateDateRange: Subject<[Date, Date]> = new Subject();

  private readonly cdr = inject(ChangeDetectorRef);
  private readonly unsubscribe$: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    this.onPickerClose.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
      if (this.fromDate && !this.toDate) {
        this.fromDate = null;
        this.cdr.detectChanges();
      }
    });

    this.updateDateRange.pipe(takeUntil(this.unsubscribe$)).subscribe((dates) => {
      if (dates.length === 2) {
        this.flatpickrInstance?.setDate(dates, true);
      }
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
