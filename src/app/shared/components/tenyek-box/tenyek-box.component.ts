import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { <PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { TenyekBoxArticle } from '@trendency/kesma-ui';
import { ApiService } from '../../services';

@Component({
  selector: 'app-tenyek-box',
  templateUrl: './tenyek-box.component.html',
  styleUrls: ['./tenyek-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, NgFor, SlicePipe],
})
export class TenyekBoxComponent implements OnInit, OnDestroy {
  articles: TenyekBoxArticle[] = [];
  destroy$: Subject<boolean> = new Subject<boolean>();

  constructor(
    private readonly apiService: ApiService,
    private readonly cd: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.apiService
      .getTenyekBoxArticles()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.articles = data.data.slice(0, 6);
          this.cd.detectChanges();
        },
        error: (error) => {
          console.error('Tények cikkek nem tölthetők be!', error);
        },
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }
}
