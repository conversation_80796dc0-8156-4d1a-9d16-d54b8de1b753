<div *ngIf="articles.length >= 6" class="tenyek-box">
  <a class="tenyek-box-header" [href]="'https://tenyek.hu'" target="_blank">
    <img loading="lazy" src="../assets/images/tenyek-logo.png" alt="Tények logo" />
  </a>
  <hr class="divider full-width" />
  <div class="tenyek-box-content">
    <ng-container *ngTemplateOutlet="articleListTemplate; context: { $implicit: articles | slice: 0 : 3 }"></ng-container>
    <ng-container *ngTemplateOutlet="articleListTemplate; context: { $implicit: articles | slice: 3 : 6 }"></ng-container>
  </div>
</div>

<ng-template #articleListTemplate let-articles>
  <div class="tenyek-box-column">
    <a class="tenyek-box-column-lead" [href]="articles[0]?.link" target="_blank">
      <img src="{{ articles[0]?.image }}" alt="{{ articles[0]?.title || '' }}" loading="lazy" />
      <h2>{{ articles[0]?.title }}</h2>
    </a>
    <hr class="divider" />
    <ng-container *ngFor="let article of articles.slice(1, 3)">
      <a class="tenyek-box-column-article" [href]="article?.link" target="_blank">
        <h2>{{ article?.title }}</h2>
      </a>
      <hr class="divider" />
    </ng-container>
  </div>
</ng-template>
