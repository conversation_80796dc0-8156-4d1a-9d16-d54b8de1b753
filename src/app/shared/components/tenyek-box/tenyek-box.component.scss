@use 'shared' as *;

:host {
  display: block;
  background-color: $grey-22;
}

.tenyek-box {
  width: 100%;
  padding: 20px;
  color: $black;
  font-family: $font-family;

  &-header {
    display: flex;
    width: 100%;
    margin-bottom: 15px;

    img {
      @include media-breakpoint-up(sm) {
        height: 100px;
      }
    }
  }

  .divider {
    border: 0;
    border-top-style: solid;
    border-top-width: 3px;
    max-width: 60px;
    margin: 15px 0;
    border-top-color: rgba(243, 29, 49, 1);

    &.full-width {
      max-width: 100%;
      border-top-color: rgba(36, 56, 141, 1);
      margin-bottom: 30px;
    }
  }

  &-content {
    display: flex;
    gap: 35px;

    @include media-breakpoint-down(md) {
      flex-wrap: wrap;
    }
  }

  &-column {
    display: flex;
    row-gap: 15px;
    flex-direction: column;
    width: 50%;

    @include media-breakpoint-down(md) {
      width: 100%;
    }

    a {
      color: $black;
    }

    &-lead {
      img {
        width: 100%;
        aspect-ratio: 16/9;
        margin-bottom: 20px;
        backdrop-filter: invert(0.1);
        object-fit: cover;
      }

      h2 {
        font-weight: 500;
        font-size: 28px;
        line-height: 38px;
      }
    }

    &-article {
      h2 {
        font-weight: 500;
        font-size: 18px;
        line-height: 23px;
        margin-bottom: 15px;
      }
    }
  }
}
