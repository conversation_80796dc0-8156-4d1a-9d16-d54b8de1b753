<div *ngIf="this.tags?.length > 0" class="tags">
  <span class="label">Címkék</span>
  <a *ngIf="foundationTagSlug" [routerLink]="['/', 'cimke', foundationTagSlug]" [state]="{ tagTitle: foundationTagTitle }" class="tag"
    >#{{ foundationTagTitle }}</a
  >
  <ng-container *ngFor="let tag of tags">
    <a [routerLink]="['/', 'cimke', tag.slug]" [state]="{ tagTitle: tag.title }" class="tag">#{{ tag.title }}</a>
  </ng-container>
</div>
