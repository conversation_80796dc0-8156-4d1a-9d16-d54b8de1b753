import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf } from '@angular/common';
import { Tag } from '@trendency/kesma-ui';

@Component({
  selector: 'app-tag-row',
  templateUrl: './tag-row.component.html',
  styleUrls: ['./tag-row.component.scss'],
  imports: [NgIf, RouterLink, NgFor],
})
export class TagRowComponent {
  @Input() foundationTagSlug?: string;
  @Input() foundationTagTitle?: string;
  @Input() tags: Tag[];
}
