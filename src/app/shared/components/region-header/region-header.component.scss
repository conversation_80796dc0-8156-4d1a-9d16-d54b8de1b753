@use 'shared' as *;

.region-header {
  width: 100%;
  position: relative;
  background-size: cover;
  background-position: center;

  &:after {
    content: ' ';
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.7;
    background: linear-gradient(90deg, $primary-color 0%, $secondary-color 100%);
    position: absolute;
  }

  .wrapper {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    height: 300px;
    @include media-breakpoint-down(md) {
      padding-top: 170px;
      padding-bottom: 20px;
      height: auto;
      text-align: center;
    }

    .left {
      width: 100%;

      .region-name {
        color: $white;
        font-weight: 500;
        font-size: 60px;
        line-height: 91px;
        text-transform: uppercase;
      }

      .lead {
        color: $white;
        font-style: italic;
        font-weight: 600;
        font-size: 30px;
        line-height: 36px;
        font-family: $font-zilla;
      }
    }

    .badge {
      width: 150px;
      height: 150px;
      position: absolute;
      top: calc(50% - 75px);
      left: calc(50% - 75px);
      background-size: contain;
      @include media-breakpoint-down(md) {
        width: 130px;
        height: 130px;
        top: 20px;
      }
    }
  }
}
