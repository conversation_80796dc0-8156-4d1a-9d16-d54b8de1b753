import { Component, Input } from '@angular/core';
import { NgI<PERSON>, <PERSON><PERSON>ty<PERSON> } from '@angular/common';
import { Region } from '../../../feature/region/region.definitions';
import { ImageLazyLoadDirective } from '@trendency/kesma-core';

@Component({
  selector: 'app-region-header',
  templateUrl: './region-header.component.html',
  styleUrls: ['./region-header.component.scss'],
  imports: [NgStyle, NgIf, ImageLazyLoadDirective],
})
export class RegionHeaderComponent {
  @Input() region: Region = {
    title: 'Pécs',
    slug: 'pecs',
    description: 'A kultúra városa',
    blazon: {
      thumbnail: {
        url: '/assets/images/placeholder/pecsujcimere.png',
      },
      fullSize: {
        url: '/assets/images/placeholder/pecsujcimere.png',
      },
    },
    fullSize: {
      url: 'https://picsum.photos/2100/700',
    },
  } as any;
}
