<section class="region-header" trImageLazyLoad [ngStyle]="{ 'background-image': region?.fullSize?.url ? 'url(' + region?.fullSize?.url + ')' : 'none' }">
  <div class="wrapper">
    <div class="left">
      <h1 class="region-name">{{ region?.title }}</h1>
      <p class="lead" *ngIf="region?.description">{{ region?.description }}</p>
    </div>
    <div
      class="badge"
      trImageLazyLoad
      [ngStyle]="{ 'background-image': region?.blazon?.fullSize ? 'url(' + region?.blazon?.fullSize?.url + ')' : 'none' }"
    ></div>
  </div>
</section>
