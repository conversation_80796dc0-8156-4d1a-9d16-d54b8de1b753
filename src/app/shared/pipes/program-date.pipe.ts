import { Pipe, PipeTransform } from '@angular/core';
import { FormatDatePipe } from '@trendency/kesma-core';

@Pipe({
  name: 'programDate',
})
export class ProgramDatePipe implements PipeTransform {
  transform(initialDate: string, secondDate: string): string {
    const formatDatePipe = new FormatDatePipe();

    if (!initialDate) {
      return initialDate;
    }

    const startDate = formatDatePipe.transform(initialDate, 'date');
    const endDate = formatDatePipe.transform(secondDate, 'date');

    if (startDate === endDate) {
      return formatDatePipe.transform(initialDate, 'h-m');
    }
    if (startDate > endDate) {
      return formatDatePipe.transform(initialDate, 'l-d-h-m');
    }
    return formatDatePipe.transform(initialDate, 'y-l-d-h-m');
  }
}
