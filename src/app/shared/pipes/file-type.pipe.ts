import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'fileType',
})
export class FileTypePipe implements PipeTransform {
  enabledTypes = ['png', 'jpg', 'jpeg', 'mp4'];

  transform(fileName: string): string {
    let fileFormat: string;

    if (fileName.split('.').length > 1) {
      fileFormat = fileName.split('.')[1].toLowerCase();

      for (const type of this.enabledTypes) {
        if (type === fileFormat) {
          return 'correct-type';
        }
      }
    }

    return 'wrong-type';
  }
}
