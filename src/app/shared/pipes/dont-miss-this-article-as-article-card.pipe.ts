import { Pipe, PipeTransform } from '@angular/core';
import { Article, ArticleCard } from '@trendency/kesma-ui';
import { dontMissThisArticleToArticleCard } from 'src/app/feature/article/article.utils';

@Pipe({
  name: 'dontMissThisArticleToArticleCard',
})
export class DontMissThisArticleToArticleCardPipe implements PipeTransform {
  transform(value: Article['dontMissThisArticle']): ArticleCard | undefined {
    if (!value) {
      return undefined;
    }

    return dontMissThisArticleToArticleCard(value);
  }
}
