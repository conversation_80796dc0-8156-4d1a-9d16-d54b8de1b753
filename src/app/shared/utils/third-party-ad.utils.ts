import { ThirdPartyAdUrls } from '../definitions';
import { DomSanitizer } from '@angular/platform-browser';

export const generateThirdPartyAdUrls = (sanitizer: DomSanitizer): ThirdPartyAdUrls => {
  const randomNumber: number = Math.floor(Math.random() * 100000000) + 100000000;

  return {
    desktop: {
      frameUrl: sanitizer.bypassSecurityTrustResourceUrl(`https://wpmania.ro/ads/www/delivery/afr.php?zoneid=40&cb=${randomNumber}`),
      linkUrl: sanitizer.bypassSecurityTrustResourceUrl(`https://wpmania.ro/ads/www/delivery/ck.php?n=a9d5f92e&cb=${randomNumber}`),
      imgUrl: sanitizer.bypassSecurityTrustResourceUrl(`https://wpmania.ro/ads/www/delivery/avw.php?zoneid=40&cb=${randomNumber}&n=a9d5f92e`),
    },
    mobile: {
      frameUrl: sanitizer.bypassSecurityTrustResourceUrl(`https://wpmania.ro/ads/www/delivery/afr.php?zoneid=41&cb=${randomNumber}`),
      linkUrl: sanitizer.bypassSecurityTrustResourceUrl(`https://wpmania.ro/ads/www/delivery/ck.php?n=a811db4b&cb=${randomNumber}`),
      imgUrl: sanitizer.bypassSecurityTrustResourceUrl(`https://wpmania.ro/ads/www/delivery/avw.php?zoneid=41&cb=${randomNumber}&n=a811db4b`),
    },
  };
};
