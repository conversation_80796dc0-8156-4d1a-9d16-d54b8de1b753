import { ArticleCard, BrandingBoxArticle, PersonalizedRecommendationArticle } from '@trendency/kesma-ui';
import { ArticleSearchResult } from '../../feature/article/article.definitions';

export const mapSearchResultToArticleCard = ({
  id,
  title,
  slug,
  columnTitle,
  columnSlug,
  publishDate,
  tag: articleTag,
  lead,
  thumbnail: thumbnailUrl,
  author: authorName,
  year,
  month,
  contentType,
  preTitle,
  foundationTagSlug,
  foundationTagTitle,
  thumbnailFocusedImages,
}: ArticleSearchResult): ArticleCard => ({
  id,
  title,
  slug,
  category: { name: columnTitle, slug: columnSlug },
  publishDate,
  readingTime: undefined,
  label: articleTag && articleTag?.name ? { text: articleTag?.name } : undefined,
  tag: articleTag,
  lead,
  thumbnail: thumbnailUrl ? { url: thumbnailUrl } : undefined,
  author: { name: authorName },
  publishYear: year ?? publishDate.split('-')?.[0],
  publishMonth: month ?? publishDate.split('-')?.[1],
  contentType,
  columnTitle,
  columnSlug,
  preTitle,
  foundationTagSlug,
  foundationTagTitle,
  thumbnailFocusedImages,
});

export const mapBrandingBoxArticle = (personalizedRecommendationArticle: PersonalizedRecommendationArticle): BrandingBoxArticle => ({
  title: personalizedRecommendationArticle?.title,
  lead: personalizedRecommendationArticle?.head,
  thumbnail: personalizedRecommendationArticle?.image,
  imageUrl: personalizedRecommendationArticle?.image,
  url: personalizedRecommendationArticle?.url,
});
