import { format as dateFnsFormat } from 'date-fns';
import { isNil } from 'lodash-es';

const defaultFormat = 'yyyy-MM-dd';

export const dateToUtcDateString = (date: Date, format = defaultFormat): string | null => {
  if (isNil(date)) {
    return null;
  }

  return dateFnsFormat(date, format);
};

export const utcDateStringToDate = (utcDateString: string): Date | null => {
  if (isNil(utcDateString)) {
    return null;
  }

  return new Date(utcDateString);
};
