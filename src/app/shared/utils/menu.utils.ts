import { MenuChild, RelatedType } from '../definitions';

export const getLinkFromMenuItem = (menuItem: MenuChild): string[] | string => {
  switch (menuItem.relatedType) {
    case RelatedType.COLUMN:
      return ['/', 'rovat', menuItem.related?.slug ?? ''];
    case RelatedType.DOSSIER:
      return ['/', 'dosszie', menuItem.related?.slug ?? ''];
    case RelatedType.REGION:
      return ['/', 'regio', menuItem.related?.slug ?? ''];
    case RelatedType.ADS_PAGE:
      return ['/', 'aprohirdetes'];
    case RelatedType.GRIEF_PAGE:
      return ['/', 'gyasz'];
    case RelatedType.WEATHER_PAGE:
      return ['/', 'idojaras'];
    case RelatedType.TUNDERSZEPEK_PAGE:
      return ['/', 'tunderszepek'];
    case RelatedType.GALLERY_COLLECTION:
      return ['/', 'galeriak'];
    case RelatedType.STATIC_PAGE:
      return ['/', menuItem.related?.slug ?? ''];
    case RelatedType.HOME_PAGE:
      return ['/'];
    case RelatedType.SEARCH_PAGE:
      return ['/', 'kereses'];
    case RelatedType.ARTICLE:
    case RelatedType.NOTE: {
      const splitDate: string[] = menuItem.related?.publishDate?.date?.split('-') ?? [];
      const [year, month] = splitDate;
      return ['/', menuItem.related?.columnSlug ?? '', year, month, menuItem.related?.slug ?? ''];
    }
    case RelatedType.PROGRAM_RECOMMENDATION:
      return ['/', 'program-ajanlo', menuItem.related?.slug ?? ''];
    case RelatedType.PROGRAM_RECOMMENDATION_COLLECTION:
      return ['/', 'program-ajanlo'];
    case RelatedType.CUSTOM_URL:
      return menuItem.customUrl ?? '';
    case RelatedType.CUSTOM_BUILT_PAGE:
      return ['/', menuItem.related?.slug ?? ''];
    case RelatedType.DROPDOWN:
      return ['/'];
    default:
      return ['/', '404'];
  }
};
