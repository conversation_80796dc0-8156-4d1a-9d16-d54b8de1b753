import { Layout, LayoutApiData, LayoutElementContent, LayoutElementRow, LayoutElementType, LayoutStruct, RecursiveLayoutElement } from '@trendency/kesma-ui';

const napAjanlataLayout = {
  id: 'b89f3d83-6e18-4283-83c3-33920582a967',
  type: 'row',
  elements: [
    {
      id: 'bb2a39d1-c5d9-4e09-b550-1f9801feb956',
      type: 'column',
      elements: [
        {
          configurable: false,
          id: '8ab0f7a4-478a-4563-93cc-314904a403ed',
          type: 'content',
          contentLength: 1,
          previewImage: '/assets/images/layout-frames/vg/ad-mobile-nap-ajanlata_keret.png',
          contentType: 'ad',
          hideMobile: false,
          medium: 'mobile',
          bannerName: 'nap-ajanlata_keret',
          withBlockTitle: false,
        },
      ],
      widthDesktop: 12,
      withBlockTitle: false,
      hideMobile: false,
      blockTitle: null,
    },
  ],
  withBlockTitle: false,
  hideMobile: false,
  backgroundColor: '',
  blockTitle: null,
} as unknown as LayoutElementRow;

function addElementBeforeRowLayout(struct: LayoutElementRow[], blockTitleText: string, elementToAdd: LayoutElementRow): LayoutElementRow[] {
  return struct
    .map((row) => {
      if (row.blockTitle && row.blockTitle.text.toLowerCase() === blockTitleText.toLowerCase()) {
        return [elementToAdd, row];
      }
      return row;
    })
    .flat();
}

export const mapLayoutData = (layoutApiData: LayoutApiData): LayoutApiData =>
  mapLayoutSidebarDataFactory<LayoutApiData, LayoutElementRow[], LayoutElementRow>(layoutApiData, addElementBeforeRowLayout, napAjanlataLayout);

const napAjanlataSidebar = {
  id: 'ecc2539a-6d69-4390-a71d-412ed6b8cb9e',
  type: 'content',
  contentLength: 1,
  previewImage: '/assets/images/layout-frames/vg/ad-mobile.png',
  contentType: 'ad',
  configurable: false,
  hideMobile: false,
  medium: 'mobile',
  bannerName: 'nap-ajanlata_keret',
  withBlockTitle: false,
} as unknown as LayoutElementContent as any;

function addElementBeforeRowSidebar(struct: LayoutStruct[] | any[], blockTitleText: string, elementToAdd: LayoutElementContent): LayoutStruct[] {
  const map = struct.map((row: LayoutStruct) => {
    return {
      ...row,
      elements: row.elements.map((element1: RecursiveLayoutElement) => {
        return {
          ...element1,
          elements: element1.elements
            .map((element2: RecursiveLayoutElement) => {
              if (
                element2.type === LayoutElementType.Content &&
                element2.blockTitle &&
                element2.blockTitle.text.toLowerCase() === blockTitleText.toLowerCase()
              ) {
                return [elementToAdd, element2];
              }
              return element2;
            })
            .flat(),
        };
      }),
    };
  });

  return map as LayoutStruct[];
}

export const mapSidebarData = (layoutApiData: Layout): Layout =>
  mapLayoutSidebarDataFactory<Layout, LayoutStruct[], LayoutElementContent>(layoutApiData, addElementBeforeRowSidebar, napAjanlataSidebar);

export function mapLayoutSidebarDataFactory<TLayoutApiData, TStruct, TElement>(
  layoutApiData: TLayoutApiData,
  elementAdderFunc: (struct: TStruct, blockTitleText: string, elementToAdd: TElement) => TStruct,
  element: TElement
): TLayoutApiData {
  const advertInjectedStruct = elementAdderFunc((layoutApiData as Record<'struct' | 'content', any>)?.struct, 'Digitália', element);

  return {
    ...layoutApiData,
    struct: advertInjectedStruct,
  };
}
