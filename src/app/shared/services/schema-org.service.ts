import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { PortalConfigService } from './portal-config.service';
import { ArticleSchema, SeoService } from '@trendency/kesma-core';
import { schemaOrgUtils, schemaOrgWebpageDataTemplate } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class JsonLDService {
  private readonly renderer: Renderer2;
  private readonly schemaScriptTag: any;

  constructor(
    private readonly seo: SeoService,
    private readonly portalConfig: PortalConfigService,
    private readonly rendererFactory: RendererFactory2
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    this.schemaScriptTag = this.renderer.selectRootElement(schemaOrgUtils.className, true);
  }

  removeStructuredData(): void {
    this.renderer.setProperty(this.schemaScriptTag, 'innerHTML', '');
  }

  insertSchema(schemaArg?: ArticleSchema): void {
    schemaOrgWebpageDataTemplate.name = `${this.portalConfig.portalName} - ${this.portalConfig.portalSubtitle}`;
    schemaOrgWebpageDataTemplate.url = this.seo.hostUrl;
    const schema = schemaArg || schemaOrgWebpageDataTemplate;
    const scriptText = this.renderer.createText(JSON.stringify(schema));
    this.renderer.appendChild(this.schemaScriptTag, scriptText);
  }
}
