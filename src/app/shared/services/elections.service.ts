import { Injectable } from '@angular/core';
import { BackendPortalConfigService } from './backend-portal-config.service';
import { PortalConfigSetting } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class ElectionsService {
  readonly ELECTIONS_2024_LIVE_LINK = '/helyi-kozelet/2024/06/valasztas-2024-napkozben';
  readonly ELECTIONS_2024_RESULTS_LINK = '/helyi-kozelet/2024/06/valasztas-2024-eredmenyek';

  constructor(public readonly backendPortalConfigService: BackendPortalConfigService) {}

  isElections2024Enabled(): boolean {
    return this.backendPortalConfigService.isConfigSet(PortalConfigSetting.ENABLE_ELECTIONS_2024);
  }

  getElections2024Link(): string {
    return this.backendPortalConfigService.isConfigSet(PortalConfigSetting.ENABLE_ELECTIONS_2024_RESULTS)
      ? this.ELECTIONS_2024_RESULTS_LINK
      : this.ELECTIONS_2024_LIVE_LINK;
  }
}
