import { DOCUMENT } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { Observable, Subject, take } from 'rxjs';
import { differenceInMinutes } from 'date-fns';
import { PortalConfigService } from './portal-config.service';

/**
 * !!! Ensure that users see a maximum of one ad per hour !!!
 */
const VIGNETTE_TIMEOUT_MINUTES = 60;

@Injectable({
  providedIn: 'root',
})
export class VignetteAdService {
  private readonly document: Document = inject(DOCUMENT);

  constructor(private readonly portalConfig: PortalConfigService) {}

  private readonly LOCALSTORAGE_TIMEOUT_KEY = `${this.portalConfig.portalName}-vignette-last-activation`;

  /**
   * Indicates that the Vignette could be closed.
   * Used to provide a method for signaling back from the event handler.
   * @private
   */
  private readonly canClose$ = new Subject<void>();

  /**
   * Marks if the user has been already saw a vignette since the app has been initialized.
   * This has no added functionality, only used to not check the Vignette anymore after it has been shown.
   * @private
   */
  private hasVignetteDisplayedInCurrentSession = false;

  /**
   * Return the date of the last emission of the Vignette.
   * If the localstorage item does not existst or if it's invalid, the date will be the
   * smallest representable date in order to have the required difference.
   */
  get latestVignette(): Date | null {
    const lastString = localStorage.getItem(this.LOCALSTORAGE_TIMEOUT_KEY) ?? '';
    try {
      const d = new Date(parseInt(lastString) * 1000);
      if (isNaN(d.getTime())) {
        throw new Error('Invalid date');
      }
      return d;
    } catch (e) {
      return null;
    }
  }

  /**
   * Handles the removal of the vignette when it emits back the specified value.
   * You can change the closing conditions in this function!
   * @param vignette
   */
  vignetteMessageHandler(vignette: HTMLIFrameElement) {
    return (e: any): void => {
      try {
        const messageData = JSON.parse(e.data);
        console.log('VignetteAdService -> Message received: ', messageData);
        // The vignette ins element sends back very specific messages, maybe we have to change this in the future.
        if (messageData.msg_type === 'i-dismiss' || messageData.eventType === 'adClosed') {
          vignette.style.display = 'none';
          this.canClose$.next();
        }
      } catch (e) {
        /* empty */
      }
    };
  }

  canDeactivate(): Observable<boolean> | Promise<boolean> | boolean {
    return new Promise((resolve) => {
      // Short circuit to increase performance after the Vignette has been shown since the app initialized.
      // As Vignette could be shown once per hour it is not necessary to check it before every route change.
      // If there is no LocalStorage in the browser we should skip the Vignette logic.
      if (this.hasVignetteDisplayedInCurrentSession || !localStorage) {
        resolve(true);
        return;
      }
      const now = new Date();
      const latestVignetteDate = this.latestVignette;

      if (!latestVignetteDate || Math.abs(differenceInMinutes(latestVignetteDate, now)) > VIGNETTE_TIMEOUT_MINUTES) {
        const vignette: HTMLIFrameElement | undefined = this.document.querySelector('ins.adsbygoogle[data-vignette-loaded="true"]') as HTMLIFrameElement;
        if (!vignette) {
          resolve(true);
          return;
        }
        vignette.style.display = 'block';
        localStorage.setItem(this.LOCALSTORAGE_TIMEOUT_KEY, Math.floor(now.getTime() / 1000).toString());
        this.hasVignetteDisplayedInCurrentSession = true;
        const handler = this.vignetteMessageHandler(vignette);
        window.addEventListener('message', handler);
        //We should stop listening for window messages after the vignette has been closed.
        this.canClose$.pipe(take(1)).subscribe(() => {
          window.removeEventListener('message', handler);
          vignette?.remove();
          resolve(true);
        });
      } else {
        resolve(true);
      }
    });
  }
}
