import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import { ApiResult } from '@trendency/kesma-ui';
import { WeatherCity, WeatherData } from '../definitions';

export const WEATHER_REGIONS: WeatherCity[] = [
  'Budapest',
  'Debrecen',
  'Eger',
  'Győr',
  'Kaposv<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  'Miskolc',
  'Nyíregyháza',
  'Pécs',
  'Salgótarján',
  'Szeged',
  '<PERSON>z<PERSON>kesfehérvár',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  'Szolnok',
  'Szombathely',
  'Tatabánya',
  'Veszprém',
  'Zalaegerszeg',
];

@Injectable({
  providedIn: 'root',
})
export class WeatherService {
  constructor(private readonly reqService: ReqService) {}

  getWeather(): Observable<WeatherData> {
    return this.reqService.get<ApiResult<WeatherData>>('/mediaworks/weather').pipe(map((resp) => resp.data));
  }

  getFullWeatherData(): Observable<WeatherData> {
    return this.getWeather();
  }
}
