import { Injectable, OnDestroy } from '@angular/core';
import { NavigationStart, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { UtilService } from '@trendency/kesma-core';

@Injectable()
export class ScrollPositionService implements OnDestroy {
  routerSubscription$: Subscription;
  storageKeyPrefix = 'scroll_position_';
  url = this.router.routerState.snapshot.url;

  constructor(
    private readonly router: Router,
    private readonly utilsService: UtilService
  ) {}

  ngOnDestroy(): void {
    if (this.routerSubscription$) {
      this.routerSubscription$.unsubscribe();
    }
  }

  setupScrollPositionListener(): void {
    this.routerSubscription$ = this.router.events.subscribe((event): void => {
      if (event instanceof NavigationStart) {
        if (event.url && event.url !== this.url) {
          const scrolled = window.scrollY.toString();
          sessionStorage.setItem(this.storageKeyPrefix + this.url, scrolled);
        }
      }
    });
  }

  scrollToLastPosition(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        window.scrollTo(0, this.getLastScrollPosition());
        sessionStorage.setItem(this.storageKeyPrefix + this.url, '');
      }, 500);
    }
  }

  private getLastScrollPosition(): number {
    return +(sessionStorage.getItem(this.storageKeyPrefix + this.url) ?? 0);
  }
}
