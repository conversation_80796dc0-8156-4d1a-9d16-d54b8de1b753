import { DOCUMENT } from '@angular/common';
import { inject, Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class ThemeService {
  private readonly document: Document = inject(DOCUMENT);

  setTheme(
    primaryColor: string,
    secondaryColor: string,
    linkColor: string,
    activeColor: string,
    tagColor: string,
    tagTextColor: string,
    logoWidth: string
  ): void {
    this.document.documentElement.style.setProperty('--primary-color', primaryColor);
    this.document.documentElement.style.setProperty('--secondary-color', secondaryColor);
    this.document.documentElement.style.setProperty('--link-color', linkColor);
    this.document.documentElement.style.setProperty('--active-color', activeColor);
    this.document.documentElement.style.setProperty('--tag-color', tagColor);
    this.document.documentElement.style.setProperty('--tag-text-color', tagTextColor);
    this.document.documentElement.style.setProperty('--logo-width', logoWidth);
  }
}
