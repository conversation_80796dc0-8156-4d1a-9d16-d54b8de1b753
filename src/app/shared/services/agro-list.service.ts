import { Injectable } from '@angular/core';
import { BehaviorSubject, timer } from 'rxjs';
import { concatMap } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { ApiService } from './api.service';
import { AgroResponse } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class AgroListService {
  agroListSubject = new BehaviorSubject<AgroResponse | null>(null);
  agroList$ = this.agroListSubject.asObservable();
  tenMinutes = 60 * 1000;

  constructor(
    private readonly apiService: ApiService,
    private readonly utilsService: UtilService
  ) {
    if (this.utilsService.isBrowser()) {
      this.initPoll();
    } else {
      this.initNode();
    }
  }

  initPoll(): void {
    timer(0, this.tenMinutes)
      .pipe(concatMap(() => this.apiService.getAgro()))
      .subscribe((agroListResponse: AgroResponse) => {
        this.agroListSubject.next(agroListResponse);
      });
  }

  initNode(): void {
    this.apiService.getAgro().subscribe((agroListResponse) => {
      this.agroListSubject.next(agroListResponse);
      this.agroListSubject.complete();
    });
  }
}
