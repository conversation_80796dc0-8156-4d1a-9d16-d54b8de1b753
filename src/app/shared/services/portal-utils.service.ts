import { Injectable } from '@angular/core';
import { format } from 'date-fns';

@Injectable({
  providedIn: 'root',
})
export class PortalUtilsService {
  today(dateFormat = 'y-MM-DD HH:mm:ss'): string {
    return format(this.now(), dateFormat);
  }

  now(): Date {
    return new Date();
  }

  getFacebooklink(portalName: string): string {
    switch (portalName) {
      case 'BAMA':
        return `https://www.facebook.com/bama.hu/?fref=ts`;
      case 'BAON':
        return `https://www.facebook.com/face.baon.hu/?fref=ts`;
      case 'BEOL':
        return `https://www.facebook.com/beol.hu/?fref=ts`;
      case 'BOON':
        return `https://www.facebook.com/borsodihirek/?fref=ts`;
      case 'DELMAGYAR':
        return `https://www.facebook.com/delmagyar/?fref=ts`;
      case 'DUOL':
        return `https://www.facebook.com/dunaujvarosihirlap/?fref=ts`;
      case 'ERDON':
        return `https://www.facebook.com/erdon-1409586709363504/?fref=ts`;
      case 'FEOL':
        return `https://www.facebook.com/feol.hu/?fref=ts`;
      case 'HAON':
        return `https://www.facebook.com/HajduOnline/?fref=ts`;
      case 'HEOL':
        return `https://www.facebook.com/heol.hu/?fref=ts`;
      case 'KEMMA':
        return `https://www.facebook.com/kemmaponthu/?fref=ts`;
      case 'KISALFOLD':
        return `https://www.facebook.com/kisalfold.hu/?fref=ts`;
      case 'KPI':
        return `https://www.facebook.com/kpi.hu/?fref=ts`;
      case 'NOOL':
        return `https://www.facebook.com/nool.hu/?fref=ts`;
      case 'SONLINE':
        return `https://www.facebook.com/sonline.hu/?fref=ts`;
      case 'SZEGEDMA':
        return `https://www.facebook.com/szegedma/?fref=ts`;
      case 'SZOLJON':
        return `https://www.facebook.com/szoljon/?fref=ts`;
      case 'SZON':
        return `https://www.facebook.com/szabolcsonline/?fref=ts`;
      case 'TEOL':
        return `https://www.facebook.com/TeolhuUj/?fref=ts`;
      case 'VAOL':
        return `https://www.facebook.com/vasnepe.hu/?fref=ts`;
      case 'VEOL':
        return `https://www.facebook.com/veol.hu/?fref=ts`;
      case 'ZAOL':
        return `https://www.facebook.com/zalaihirlap.hu/?fref=ts`;
      default:
        return '';
    }
  }

  getInstagramlink(portalName: string): string {
    switch (portalName) {
      case 'BAMA':
        return `https://www.instagram.com/bama.hu/`;
      case 'BAON':
        return `https://www.instagram.com/baon.hu/`;
      case 'BEOL':
        return `https://www.instagram.com/beol.hu/`;
      case 'BOON':
        return `https://www.instagram.com/boon.hu/`;
      case 'DELMAGYAR':
        return `https://www.instagram.com/delmagyar/?hl=hu`;
      case 'DUOL':
        return `https://www.instagram.com/duol.hu/`;
      case 'ERDON':
        return `https://www.instagram.com/erdon.ro/`;
      case 'FEOL':
        return `https://www.instagram.com/feol.hu/`;
      case 'HAON':
        return `https://www.instagram.com/haon.hu/`;
      case 'HEOL':
        return `https://www.instagram.com/wwwheolhu/`;
      case 'KEMMA':
        return `https://www.instagram.com/kemmaszerk/`;
      case 'KISALFOLD':
        return `https://www.instagram.com/kisalfold_hu/?hl=hu`;
      case 'KPI':
        return `https://www.instagram.com/kpi.hu/`;
      case 'NOOL':
        return `https://www.instagram.com/nool.hu/`;
      case 'SONLINE':
        return `https://www.instagram.com/sonline.hu/`;
      case 'SZEGEDMA':
        return `https://www.instagram.com/szegedma.hu/`;
      case 'SZOLJON':
        return `https://www.instagram.com/szoljon.hu/`;
      case 'SZON':
        return `https://www.instagram.com/szon.hu/`;
      case 'TEOL':
        return `https://www.instagram.com/teol.hu/`;
      case 'VAOL':
        return `https://www.instagram.com/vaol.hu/?hl=hu`;
      case 'VEOL':
        return `https://www.instagram.com/veol.hu/`;
      case 'ZAOL':
        return `https://www.instagram.com/zaol.hu/`;
      default:
        return '';
    }
  }
}
