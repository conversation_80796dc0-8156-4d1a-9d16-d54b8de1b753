import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

/**
 * A service that holds a view model typed to a specific view state.
 * View state type should be defined in the component that uses this service.
 */
@Injectable()
export class ViewModelService<TViewState> {
  readonly #vm$: BehaviorSubject<TViewState> = new BehaviorSubject<TViewState>({} as TViewState);

  /**
   * An observable that emits the current view state
   * @remarks This observable is debounced by 300ms. If you need the value immediately, use `value` instead.
   */
  readonly state$: Observable<TViewState> = this.#vm$.pipe(debounceTime(300)); // Layout change triggers a lot of changes, so we debounce it

  /** The current view state. */
  get value(): TViewState {
    return this.#vm$.getValue();
  }

  /**
   * Sets the current view state. Overwrites the previous values!
   * @param value The new view state.
   */
  set value(value: TViewState) {
    this.#vm$.next(value);
  }

  /**
   * Updates the current view state with the given values.
   * Properties that are not present in the given value will not be changed.
   * @param value The values to update the view state with.
   */
  next(value: Partial<TViewState>): void {
    this.#vm$.next({
      ...this.value,
      ...value,
    });
  }
}
