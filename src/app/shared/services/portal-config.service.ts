import { Inject, Injectable, makeStateKey, Optional, TransferState } from '@angular/core';
import { environment } from '../../../environments/environment';
import { PortalConfig } from '../../../environments/environment.definitions';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { PORTAL_CONFIG } from '../definitions';
import defaultConfig from '../../../environments/portal-config.json';

export const PORTAL_CONFIG_KEY = makeStateKey<PortalConfig>('PortalConfig');

@Injectable({
  providedIn: 'root',
})
export class PortalConfigService implements PortalConfig {
  apiUrl: string | EnvironmentApiUrl;
  personalizedRecommendationApiUrl: string | EnvironmentApiUrl;
  siteUrl: string;
  portalName: string;
  portalSubtitle: string;
  primaryColor: string;
  secondaryColor: string;
  linkColor: string;
  activeColor: string;
  tagColor: string;
  tagTextColor: string;
  logoWidth: string;
  city: string;
  county: string;
  facebookAppId: string;
  googleAnalyticsId: string;
  gemiusId: string;
  twitterSiteName?: string;
  googleAnalyticsRegions?: { id: string; region: string };
  gemiusScript?: string;
  gemiusIframe?: { id: string; content: string };
  googleTagManager: string;
  googleSiteKey: string;

  constructor(
    private readonly transferState: TransferState,
    private readonly utilService: UtilService,
    @Optional() @Inject(PORTAL_CONFIG) private readonly loadedPortalConfig: PortalConfig
  ) {
    let portalConfig: PortalConfig | undefined;
    if (this.utilService.isBrowser()) {
      portalConfig = this.transferState.get<PortalConfig | undefined>(PORTAL_CONFIG_KEY, undefined);
      if (!portalConfig && environment.type === 'local') {
        portalConfig = defaultConfig;
      }

      this.setConfig(portalConfig as PortalConfig);
      return;
    }

    this.transferState.set<PortalConfig>(PORTAL_CONFIG_KEY, this.loadedPortalConfig);
    this.setConfig(this.loadedPortalConfig);
  }

  setConfig(config: PortalConfig): void {
    if (!config) {
      throw new Error('Unable to load portal config!');
    }

    Object.keys(config).forEach((k: string): void => {
      if (typeof (this as Record<string, any>)[k] !== 'function' && typeof (this as Record<string, any>)[k] !== 'symbol') {
        (this as Record<string, any>)[k] = (config as Record<string, any>)[k];
      }
    });
  }
}
