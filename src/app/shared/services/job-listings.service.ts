import { inject, Injectable } from '@angular/core';
import { CleanHttpService } from './clean-http.service';
import { Observable, of } from 'rxjs';
import { JobListing } from '../definitions';
import { PortalConfigService } from './portal-config.service';
import { UtilService } from '@trendency/kesma-core';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class JobListingsService {
  protected readonly httpService = inject(CleanHttpService);
  protected readonly portalConfigService = inject(PortalConfigService);
  protected readonly utilService = inject(UtilService);

  getJobListings(): Observable<JobListing[]> {
    if (!this.utilService.isBrowser()) {
      return of([]);
    }
    if (environment.type !== 'prod') {
      return this.httpService.get<JobListing[]>('https://www.feol.hu/allas/api/widget/');
    } else {
      return this.httpService.get<JobListing[]>(`${this.portalConfigService.siteUrl}/allas/api/widget/`);
    }
  }
}
