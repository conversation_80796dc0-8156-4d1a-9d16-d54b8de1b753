import { Inject, Injectable } from '@angular/core';
import { CleanHttpService } from './clean-http.service';
import { Observable, of } from 'rxjs';
import { DOCUMENT } from '@angular/common';
import { map, tap } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { ArticleCard, mapPersonalizedRecommendationToArticleCard, PersonalizedRecommendationApiResponse } from '@trendency/kesma-ui';
import { PortalConfigService } from './portal-config.service';

@Injectable({
  providedIn: 'root',
})
export class PersonalizedRecommendationService {
  private readonly DOMAIN: string = this.portalConfig.portalName;
  private readonly PLATFORM: string = this.portalConfig.portalName;

  private isPersonalizedContentFetched = false;

  constructor(
    private readonly portalConfig: PortalConfigService,
    private readonly httpService: CleanHttpService,
    private readonly utilsService: UtilService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get personalizedRecommendationApiUrl(): string {
    const { personalizedRecommendationApiUrl } = this.portalConfig;

    if (typeof personalizedRecommendationApiUrl === 'string') {
      return personalizedRecommendationApiUrl;
    }

    const { clientApiUrl, serverApiUrl } = personalizedRecommendationApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getPersonalizedRecommendations(limit = 12): Observable<ArticleCard[]> {
    const currentArticleUrl: string = this.document?.defaultView?.location.href || '';
    const vidCookie: string = this.document?.cookie?.match(/_vid=([^;]+)/)?.[1] || '';
    const { siteUrl } = this.portalConfig;
    const pureSiteUrl: string = siteUrl?.replace(/^https?:\/\/(www\.)?/, '') || '';

    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.personalizedRecommendationApiUrl}/recommendation`, {
        params: {
          'traffickingPlatforms[]': this.PLATFORM,
          fingerPrint: vidCookie,
          articleCurrent: encodeURIComponent(currentArticleUrl),
          utmSource: pureSiteUrl,
          withoutPos: '1',
        },
      })
      .pipe(
        map((res: PersonalizedRecommendationApiResponse) => res[this.PLATFORM].map(mapPersonalizedRecommendationToArticleCard).slice(0, limit)),
        tap(() => (this.isPersonalizedContentFetched = true))
      );
  }

  sendPersonalizedRecommendationAv(externalRecommendations: ArticleCard[]): Observable<void> {
    if (!this.isPersonalizedContentFetched) {
      return of();
    }

    const ids: string[] = externalRecommendations.map((article: ArticleCard) => article.id) as string[];
    const avs: Record<string, string> = ids.reduce((res, value, index) => ({ ...res, [`a[${index + 1}]`]: value }), {});

    return this.httpService.post<void>(`${this.personalizedRecommendationApiUrl}/av`, null, {
      params: {
        domain: this.DOMAIN,
        platform: this.PLATFORM,
        ...avs,
      },
    });
  }
}
