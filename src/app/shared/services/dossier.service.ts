import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, BasicDossier, DossierArticle } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class DossierService {
  constructor(private readonly reqService: ReqService) {}

  getDossierArticles(
    slug: string,
    rowCountLimit?: number,
    page?: number
  ): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`/content-group/dossiers/${slug}`, {
      params: {
        rowCount_limit: rowCountLimit?.toString(),
        page_limit: page?.toString(),
      },
    });
  }
}
