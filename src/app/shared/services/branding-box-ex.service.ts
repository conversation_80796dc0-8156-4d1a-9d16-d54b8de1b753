import { inject, Injectable } from '@angular/core';
import { CleanHttpService } from './clean-http.service';
import { EnvironmentType, UtilService } from '@trendency/kesma-core';
import { catchError, map, Observable, of } from 'rxjs';
import { BrandingBoxArticle, PersonalizedRecommendationApiResponse } from '@trendency/kesma-ui';
import { mapBrandingBoxArticle } from '../utils';
import { PortalConfigService } from './portal-config.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class BrandingBoxExService {
  private readonly httpService = inject(CleanHttpService);
  private readonly utilsService = inject(UtilService);
  private readonly portalConfig = inject(PortalConfigService);

  get brandingBoxDataUrl(): string {
    const { personalizedRecommendationApiUrl } = this.portalConfig;
    if (typeof personalizedRecommendationApiUrl === 'string') {
      return environment.type === ('teszt' as EnvironmentType) ? 'https://terelo.app.content.private/api' : personalizedRecommendationApiUrl;
    }

    const { clientApiUrl, serverApiUrl } = personalizedRecommendationApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getBrandingBoxRequest$(brand?: string, articleLimit: number = 3): Observable<BrandingBoxArticle[] | undefined> {
    switch (brand) {
      case 'szabadfold':
        return this.httpService
          .get<PersonalizedRecommendationApiResponse>(`${this.brandingBoxDataUrl}/recommendation`, {
            params: { traffickingPlatforms: 'Szabadföld for Megyei', utmSource: 'szabadföld.hu', withoutPos: '1' },
          })
          .pipe(
            map((data) => data?.['Szabadföld for Megyei']?.map(mapBrandingBoxArticle)?.slice(0, articleLimit)),
            catchError(() => {
              return of(undefined);
            })
          );
      default: {
        return of(undefined);
      }
    }
  }
}
