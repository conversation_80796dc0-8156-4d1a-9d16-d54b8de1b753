import { Injectable } from '@angular/core';
import { Observable, of, Subject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { UtilService } from '@trendency/kesma-core';

/**
 * <PERSON>les recaptcha execution and queueing.
 *
 * The queue is needed because only one recaptcha can be executed at a time, and it is not handled in ReCaptchaV3Service.
 */
@Injectable({
  providedIn: 'root',
})
export class RecaptchaService {
  readonly #queue: (() => void)[] = [];

  constructor(
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly utilService: UtilService
  ) {}

  /**
   * Executes the callback after a recaptcha token is acquired.
   * If there is a recaptcha currently running, the callback will be queued.
   * @param callback The callback to execute after the recaptcha token is acquired.
   * @returns An observable that completes when `callback` is finished. The only pushed value is the return value of `callback`.
   * @remarks If the callback throws an error, the observable will error too, but the queue will continue.
   */
  enqueue(callback: (token: string) => unknown): Observable<unknown> {
    if (!this.utilService.isBrowser()) {
      return of();
    }
    const isHalted = this.#queue.length === 0;
    const callbackFinished$ = new Subject<unknown>();
    this.#queue.push(this.getQueueCallback(callback, callbackFinished$));
    if (isHalted) {
      this.#queue[0]();
    }

    return callbackFinished$.pipe();
  }

  private getQueueCallback(callback: (token: string) => unknown, callbackFinished$: Subject<unknown>) {
    return (): void => {
      callbackFinished$.subscribe(() => this.callNext());
      this.withRecaptcha(callback, callbackFinished$);
    };
  }

  private callNext(): void {
    this.#queue.shift();
    if (!this.#queue.length) {
      return;
    }

    this.#queue[0]();
  }

  private withRecaptcha(callback: (token: string) => unknown, callbackFinished$: Subject<unknown>): void {
    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_voting_vote_vote',
      (token: string) => {
        try {
          callbackFinished$.next(callback(token));
          callbackFinished$.complete();
        } catch (e) {
          console.error('[RecaptchaService::enqueue] Task failed', e);
        }
      },
      {
        useGlobalDomain: false,
      },
      (err) => {
        console.error('reCaptchaV3Service.execute error', err);
        callbackFinished$.error(err);
      }
    );
  }
}
