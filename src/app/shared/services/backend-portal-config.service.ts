import { Injectable } from '@angular/core';
import { PortalConfigData, PortalConfigSetting } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class BackendPortalConfigService {
  private backendPortalConfigs: PortalConfigData;

  setConfig(portalConfigs: PortalConfigData): void {
    this.backendPortalConfigs = portalConfigs;
  }

  isConfigSet(setting: PortalConfigSetting): boolean {
    return this.backendPortalConfigs?.[setting] === '1';
  }
}
