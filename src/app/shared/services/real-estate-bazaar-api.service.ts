import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';
import { RealEstateBazaarApiData } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class RealEstateBazaarApiService {
  constructor(private readonly reqService: ReqService) {}

  getRealEstateBazaarItems(): Observable<RealEstateBazaarApiData[]> {
    return this.reqService.get('mediaworks/ingatlanbazar/kicsi');
  }
}
