import { Injectable } from '@angular/core';
import { format } from 'date-fns';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { ApiResult } from '@trendency/kesma-ui';
import { PortalUtilsService } from './portal-utils.service';
import { BackendNameday, Nameday } from '../definitions';
import { mapBackendNamedayToNameday } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class NamedaysService {
  constructor(
    private readonly reqService: ReqService,
    private readonly utilsService: PortalUtilsService
  ) {}

  getNamedays(params?: Record<string, string>): Observable<Nameday[]> {
    const options: IHttpOptions = { params };

    return this.reqService
      .get<ApiResult<BackendNameday[]>>('/mediaworks/nameday', options)
      .pipe(map(({ data }) => data.map((n) => mapBackendNamedayToNameday(n))));
  }

  getNamedaysByDate(date?: string | Date): Observable<Nameday[]> {
    const params: Record<string, string> = {};
    if (date) {
      params['date'] = typeof date === 'string' ? date : format(date, 'Y-MM-dd');
    }
    return this.getNamedays(params);
  }

  getTodayNamedays(): Observable<Nameday[]> {
    return this.getNamedaysByDate(this.utilsService.today('y-MM-dd'));
  }

  getNamedaysByName(name: string): Observable<Nameday[]> {
    return this.getNamedays({ name });
  }
}
