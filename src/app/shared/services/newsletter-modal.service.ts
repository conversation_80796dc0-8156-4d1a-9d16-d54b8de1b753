import { Injectable } from '@angular/core';
import { addMonths, addYears, isAfter } from 'date-fns';
import { StorageService } from '@trendency/kesma-core';

const NEWSLETTER_STORAGE_KEY = 'newsletter-notification';

@Injectable({
  providedIn: 'root',
})
export class NewsletterModalService {
  constructor(private readonly storageService: StorageService) {}

  get isPostponed(): boolean {
    const newsletterClosedExp = this.storageService.getLocalStorageData(NEWSLETTER_STORAGE_KEY);
    return newsletterClosedExp && isAfter(newsletterClosedExp, new Date());
  }

  postponeFormMonth(): void {
    const nextWeek = addMonths(new Date(), 1);
    this.storageService.setLocalStorageData(NEWSLETTER_STORAGE_KEY, nextWeek.getTime());
  }

  postponeForYear(): void {
    const nextYear = addYears(new Date(), 1);
    this.storageService.setLocalStorageData(NEWSLETTER_STORAGE_KEY, nextYear.getTime());
  }
}
