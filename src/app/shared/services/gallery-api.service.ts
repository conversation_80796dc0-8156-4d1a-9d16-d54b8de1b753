import { Injectable } from '@angular/core';
import { filter, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import {
  ApiResponseMetaList,
  ApiResult,
  BackendGallery,
  BackendGalleryDetails,
  GalleriesResult,
  GalleryDetails,
  GalleryRecommendationData,
  mapBackendGalleriesResultToGalleriesResult,
  mapBackendGalleryDetailsResultToGalleryDetails,
} from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class GalleryApiService {
  constructor(private readonly reqService: ReqService) {}

  getGalleries(from: number, count: number): Observable<GalleriesResult> {
    return this.reqService
      .get<ApiResult<BackendGallery[], ApiResponseMetaList>>('media/galleries', {
        params: {
          rowFrom_limit: from.toString(),
          rowCount_limit: count.toString(),
        },
      })
      .pipe(map(mapBackendGalleriesResultToGalleriesResult));
  }

  getGalleryDetails(slug: string): Observable<GalleryDetails> {
    return this.reqService.get<ApiResult<BackendGalleryDetails[], ApiResponseMetaList>>(`media/gallery/${slug}`).pipe(
      filter((result) => !!result.data),
      map(mapBackendGalleryDetailsResultToGalleryDetails)
    );
  }

  getGalleryRecommendations(slug: string): Observable<ApiResult<GalleryRecommendationData[]>> {
    return this.reqService.get(`media/gallery/${slug}/recommendation`);
  }
}
