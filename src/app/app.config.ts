import { environment } from '../environments/environment';
import { DateFnsConfigurationService } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import { APP_INITIALIZER, ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { AppEnvironment, FormatDatePipe, provideEncodedTransferState } from '@trendency/kesma-core';
import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { adDebugFactory, DEV_AD_DEBUG } from '@trendency/kesma-ui';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { GoogleTagManagerConfiguration, GoogleTagManagerModule } from 'angular-google-tag-manager';
import { routes } from './app.routing';
import { register as registerSwiperElement } from 'swiper/element/bundle';
import { elasticIndexWarningInterceptor, PortalConfigService, portalHeaderHttpInterceptor } from './shared';
import { Environment } from '@trendency/kesma-core/lib/definitions/environment.definitions';

const googleTagConfigInitializer =
  (portalConfig: PortalConfigService, googleTagManagerConfig: GoogleTagManagerConfiguration): (() => Promise<boolean>) =>
  () =>
    new Promise((resolve) => {
      googleTagManagerConfig.set({
        id: portalConfig.googleTagManager,
      });
      resolve(true);
    });

const huConfig = new DateFnsConfigurationService();
huConfig.setLocale(hu);

registerSwiperElement();

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),

    provideRouter(routes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({ scrollPositionRestoration: 'top' })),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor, elasticIndexWarningInterceptor])),
    importProvidersFrom(GoogleTagManagerModule),
    FormatDatePipe,
    {
      provide: AppEnvironment,
      useFactory: (portalConfig: PortalConfigService): Environment => {
        const env = environment;

        const { apiUrl, siteUrl } = ['prod', 'teszt'].indexOf(environment.type) !== -1 ? portalConfig : environment;

        return {
          ...env,
          apiUrl,
          siteUrl,
        };
      },
      deps: [PortalConfigService],
    },
    {
      provide: DateFnsConfigurationService,
      useValue: huConfig,
    },
    {
      provide: DEV_AD_DEBUG,
      useFactory: adDebugFactory,
      deps: [AppEnvironment],
    },
    {
      provide: APP_INITIALIZER,
      useFactory: googleTagConfigInitializer,
      deps: [PortalConfigService, GoogleTagManagerConfiguration],
      multi: true,
    },
  ],
};
