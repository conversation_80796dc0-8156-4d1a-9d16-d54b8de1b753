import { Route, Routes, UrlMatchResult, UrlSegment } from '@angular/router';
import { BaseComponent, Error404Component, InitResolver, VignetteAdService } from './shared';
import { exceptErdonGuard } from './shared/guards/except-erdon.guard';

export const routes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.LAYOUT_EDITOR_ROUTES),
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: InitResolver },
    providers: [InitResolver],
    children: (
      [
        {
          // Főoldal
          path: '',
          pathMatch: 'full',
          loadChildren: () => import('./feature/home/<USER>').then((m) => m.HOME_ROUTES),
        },
        // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
        {
          path: 'assets/:file',
          redirectTo: '404',
        },
        {
          path: 'assets/:dir/:file',
          redirectTo: '404',
        },
        {
          path: 'script/:file',
          redirectTo: '404',
        },
        {
          path: 'szerzo',
          loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHOR_ROUTES),
        },
        {
          path: 'szerzok',
          loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHOR_ROUTES),
        },
        {
          // Advertisements
          path: 'hirdetesek',
          loadChildren: () => import('./feature/advertisement-page/advertisement-page.routing').then((m) => m.ADVERTISEMENT_PAGE_ROUTES),
        },
        // Program ajánló
        {
          path: 'program-ajanlo',
          loadChildren: () => import('src/app/feature/programs/programs.routing').then((m) => m.PROGRAMS_ROUTES),
        },
        {
          path: 'program-ajanlo/:programSlug',
          loadChildren: () => import('src/app/feature/program-detail/program-detail.routing').then((m) => m.PROGRAM_DETAIL_ROUTES),
        },
        {
          // Jegyzetek
          path: 'jegyzetek/:author',
          loadChildren: () => import('./feature/notes/notes.routing').then((m) => m.NOTES_ROUTES),
        },
        {
          // Névnapok
          path: 'nevnapok',
          loadChildren: () => import('./feature/namedays/namedays.routing').then((m) => m.NAMEDAYS_ROUTES),
        },
        {
          // Evfordulok
          path: 'evfordulok',
          canActivate: [exceptErdonGuard],
          loadChildren: () => import('./feature/anniversaries/anniversaries.routing').then((m) => m.ANNIVERSARIES_ROUTES),
        },
        {
          // Gyász
          path: 'gyasz',
          loadChildren: () => import('./feature/grief/grief.routing').then((m) => m.GRIEF_ROUTES),
        },
        {
          // Gyász
          path: 'gyaszhirek',
          loadChildren: () => import('./feature/grief/grief.routing').then((m) => m.GRIEF_ROUTES),
        },
        {
          // Apróhírdetés
          path: 'aprohirdetes',
          loadChildren: () => import('./feature/ads/ads.routing').then((m) => m.ADS_ROUTES),
        },
        {
          // Kereses lista
          path: 'kereses',
          loadChildren: () => import('./feature/search-by-keyword-page/search-by-keyword-page.routing').then((m) => m.SEARCH_BY_KEYWORD_ROUTES),
        },
        {
          // Kereses lista
          path: 'cimke',
          loadChildren: () => import('./feature/search-by-tag-page/search-by-tag-page.routing').then((m) => m.SEARCH_BY_TAG_PAGE_ROUTES),
        },
        {
          // Galéria lista oldal
          path: 'galeriak',
          loadChildren: () => import('./feature/galleries/galleries.routing').then((m) => m.GALLERIES_ROUTES),
        },
        {
          // Galéria oldal
          path: 'galeria',
          loadChildren: () => import('./feature/gallery/gallery.routing').then((m) => m.GALLERY_ROUTES),
        },
        {
          // Galéria ablak
          path: 'galeria',
          loadChildren: () => import('./feature/gallery-screen/gallery-screen.routing').then((m) => m.GALLERY_ROUTES),
        },
        {
          // hír beküldése
          path: 'hir-bekuldes',
          loadChildren: () => import('./feature/send-news/send-news.routing').then((m) => m.SEND_NEWS_ROUTES),
        },
        {
          // Időjárás oldal
          path: 'idojaras',
          loadChildren: () => import('./feature/weather/weather.routing').then((m) => m.WEATHER_ROUTES),
        },
        {
          // Tunderszepek landing
          path: 'tunderszepek',
          loadChildren: () => import('./feature/fairy-landing/fairy-landing.routing').then((m) => m.FAIRY_LANDING_ROUTES),
        },
        {
          // Tunderszepek megye/dontosok
          path: 'tunderszepek/megye/:county',
          loadChildren: () => import('./feature/fairy-landing/fairy-landing.routing').then((m) => m.FAIRY_LANDING_ROUTES),
        },
        {
          // Tunderszepek detail
          path: 'tunderszepek/:personSlug',
          loadChildren: () => import('./feature/fairy-detail/fairy-detail.routing').then((m) => m.FAIRY_DETAIL_ROUTES),
        },
        {
          path: 'file/:fileId/:fileName',
          loadChildren: () => import('./feature/file/file.routing').then((m) => m.FILE_ROUTES),
        },
        {
          // Dosszie
          path: 'dosszie/:dossierSlug',
          loadChildren: () => import('./feature/dossier-list/dossier-list.routing').then((m) => m.DOSSIER_LIST_ROUTES),
        },
        {
          // Regio
          path: 'regio/:regionSlug',
          loadChildren: () => import('./feature/region/region.routing').then((m) => m.REGION_ROUTES),
        },
        {
          // Játékok oldal
          path: 'jatekok',
          loadChildren: () => import('./feature/games-page/games-page.routing').then((m) => m.GAMES_PAGE_ROUTES),
        },
        {
          path: 'elrendezes-elonezet/:layoutHash',
          loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.LAYOUT_PREVIEW_ROUTES),
        },
        {
          path: 'cikk-elonezet/:previewHash',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: { omitGlobalPageView: true },
        },
        {
          path: 'cikk-elonezet/:previewHash/:previewType',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: { omitGlobalPageView: true },
        },
        {
          // Rovat
          path: 'rovat/:categorySlug',
          loadChildren: () => import('./feature/category/category.routing').then((m) => m.CATEGORY_ROUTES),
          data: { categoryRouteType: 'category-layout', omitGlobalPageView: true },
        },
        {
          path: '404',
          component: Error404Component,
          data: { skipRouteLocalization: true },
        },
        {
          path: 'alapko-tartalom',
          loadChildren: () => import('./feature/foundation-content/foundation-content.routing').then((m) => m.FOUNDATION_CONTENT_ROUTES),
        },
        {
          // Hír
          path: ':categorySlug/:year/:month/:articleSlug',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          // Rovat hó
          // path: ':categorySlug/:year',
          matcher: rovatHoPathMatcher,
          loadChildren: () => import('./feature/category/category.routing').then((m) => m.CATEGORY_ROUTES),
          data: { categoryRouteType: 'category-year', omitGlobalPageView: true },
        },
        {
          // régi hír
          path: ':categorySlug/:articleSlug',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          // Statikus oldal
          path: ':slug',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          path: '**',
          data: { skipRouteLocalization: true },
          redirectTo: '',
        },
      ] as Routes
    ).map((route: Route) => ({ ...route, canDeactivate: [...(route.canDeactivate ?? []), VignetteAdService] })),
  },
];

function rovatHoPathMatcher(segments: UrlSegment[]): UrlMatchResult | null {
  return segments.length === 2 && segments[1].path.length === 4 && !isNaN(+segments[1].path)
    ? {
        consumed: segments,
        posParams: { categorySlug: segments[0], year: segments[1] },
      }
    : null;
}
