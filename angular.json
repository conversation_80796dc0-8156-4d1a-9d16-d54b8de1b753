{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"megyei-lapok": {"root": "", "sourceRoot": "src", "prefix": "app", "projectType": "application", "architect": {"build": {"builder": "@angular/build:application", "options": {"allowedCommonJsDependencies": ["date-fns", "flatpickr", "https-proxy-agent", "http-proxy-middleware"], "outputPath": {"base": "dist"}, "index": "src/index.html", "tsConfig": "tsconfig.app.json", "preserveSymlinks": true, "polyfills": ["src/polyfills.ts"], "stylePreprocessorOptions": {"includePaths": ["src/scss", "."]}, "assets": [{"glob": "**/*", "input": "src/assets", "ignore": ["**/*.scss"], "output": "/assets"}, {"glob": "index.min.js", "input": "src/assets/scripts/fingerprint", "output": "/script"}, {"glob": "favicon.ico", "input": "src", "output": "/"}, {"glob": "apple-touch-icon.png", "input": "src/assets/images/favicons", "output": "/"}, {"glob": "apple-touch-icon-precomposed.png", "input": "src/assets/images/favicons", "output": "/"}, {"glob": "robots.txt", "input": "src", "output": "/"}, {"glob": "sitemap.xml", "input": "src", "output": "/"}, {"glob": "ads.txt", "input": "src", "output": "/"}, {"glob": "**/*", "input": "node_modules/@trendency/kesma-ui/assets", "output": "/assets"}], "styles": ["src/scss/styles.scss"], "scripts": ["src/assets/vendors/jquery/jquery-3.5.1.min.js", "src/assets/vendors/embedly/platform.js"], "browser": "src/main.ts", "server": "src/main.server.ts", "prerender": false, "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "40kb", "maximumError": "50kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"outputHashing": "all", "sourceMap": true, "namedChunks": true, "optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "test": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}]}, "local-ssr": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "ssr": false}}, "defaultConfiguration": "local"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "megyei-lapok:build:production"}, "test": {"buildTarget": "megyei-lapok:build:test"}, "development": {"buildTarget": "megyei-lapok:build:development"}, "local": {"buildTarget": "megyei-lapok:build:local"}}, "defaultConfiguration": "local"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false, "cache": {"enabled": false}}, "schematics": {"@schematics/angular:application": {"strict": false}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:component": {"skipTests": true, "inlineStyle": false, "inlineTemplate": false, "prefix": "app", "style": "scss"}, "@schematics/angular:directive": {"skipTests": false, "prefix": "app"}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}, "@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}