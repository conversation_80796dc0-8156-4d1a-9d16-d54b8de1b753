#!/bin/node
const existsSync = require('fs').existsSync;
const writeFileSync = require('fs').writeFileSync;

const [, , filename, output] = process.argv;

const fail = (msg) => {
  console.error(msg);
  process.exit(1);
};

if (!filename) {
  fail('Input is mandatory!');
}
if (!existsSync(filename)) {
  fail(`Input "${filename}" does not exist!`);
}

const config = require(filename)?.portalConfig;
if (!config) {
  fail('Unable to load config!');
}

const json = JSON.stringify(config);
const outputFile = output ?? filename.replace(/\.js$/, '.json');
writeFileSync(outputFile, json);
