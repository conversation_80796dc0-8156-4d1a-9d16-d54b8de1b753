{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch",
      "type": "node",
      "request": "launch",
      "program": "${workspaceRoot}/dist/server.js",
      "stopOnEntry": false,
      "skipFiles": [
          "node_modules/**/*.js"
      ],
      "args": [],
      "cwd": "${workspaceRoot}",
      "preLaunchTask": "Build",
      "runtimeExecutable": null,
      "runtimeArgs": [
          "--nolazy"
      ],
      "env": {
          "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "sourceMaps": true,
      "outFiles": [
          "${workspaceRoot}/dist/**/*.js"
      ]
  },
  {
      "name": "Attach",
      "type": "node",
      "request": "attach",
      "port": 5858,
      "address": "localhost",
      "restart": false,
      "sourceMaps": false,
      "outFiles": [],
      "localRoot": "${workspaceRoot}",
      "remoteRoot": null
  }
  ]
}

