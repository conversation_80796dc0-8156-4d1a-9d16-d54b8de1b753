/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "esModuleInterop": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "es2022",
    "lib": [
      "es2022",
      "dom"
    ],
    "noUnusedLocals": false,
    "noUnusedParameters": true,
    "alwaysStrict": true,
    "strictPropertyInitialization": false,
    "preserveSymlinks": true,
    "skipLibCheck": true,
    "useDefineForClassFields": false,
    "resolveJsonModule": true
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "fullTemplateTypeCheck": true,
    "strictTemplates": false
  }
}
