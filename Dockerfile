FROM node:18 AS build

WORKDIR /app

COPY package.json package-lock.json ./

ARG NPM_REGISTRY_PROD
ARG NPM_REGISTRY_DEV
RUN npm set //prod-nexus.trendency.hu/repository/npm-hosted/:_authToken $NPM_REGISTRY_PROD
RUN npm set //dev-nexus.trendency.hu/repository/npm-hosted/:_authToken $NPM_REGISTRY_DEV

RUN npm install

COPY . .

ARG ENV
ARG PORTAL
RUN npm run build:ssr-${ENV}:${PORTAL}


FROM node:18

WORKDIR /app

COPY --from=build /app/dist ./dist

EXPOSE 4000/tcp
CMD node dist/server/server.mjs > /dev/stdout
