#!/bin/bash
BASEPORTAL=feol

if [ "$PORTALS" == "" ]; then
  if [ "$ENV" == "prod" ]; then
    PORTALS="bama baon beol boon delmagyar duol erdon feol haon heol kemma kisalfold kpi nool sonline szoljon szon teol vaol veol zaol"
  elif [ "$ENV" == "teszt" ]; then
    PORTALS="bama beol feol veol"
  elif [ "$ENV" == "dev" ]; then
    PORTALS="feol veol"
  else
    echo "Unkown environment: $ENV"
    exit 1
  fi
fi
echo "Running build for $ENV environment"
echo "Building portals: $PORTALS"

PATH=$PATH:./node_modules/.bin

# Prepare portal config json
function generateConfig() {
  local PORTAL=$1
  cp -f "src/environments/portal-configs-$ENV/$PORTAL.js" src/environments/portal-config.js
  echo "   + converting \"${PORTAL}\" config to json..."
  node ./convert-config.js ./src/environments/portal-config.js
}

# Copy portal config, ads, robots to target dir
function copyConfig() {
  local PORTAL=$1
  local TARGET=$2

  mv src/environments/portal-config.json ${TARGET}/server/environments/
  cp src/environments/ads/ads-${PORTAL}.txt ${TARGET}/browser/ads.txt
  cp src/environments/robots/robots-${PORTAL}.txt ${TARGET}/browser/robots.txt
  cp src/assets/images/favicons/bama.ico dist/browser/favicon.ico
}

# Build base portal and compile all configs
function buildBasePortal() {
  echo " + Generating portal configs"
  tsc ./src/environments/portal-configs-$ENV/*.ts --sourceMap false --declarationMap false
  generateConfig $BASEPORTAL

  echo " + Copying base config txts..."
  cp src/environments/ads/ads-$BASEPORTAL.txt src/ads.txt
  cp src/environments/robots/robots-$BASEPORTAL.txt src/robots.txt

  case $ENV in
    dev)
      BUILD_ENV=devsrv
    ;;

    teszt)
      BUILD_ENV=teszt
    ;;

    prod)
      BUILD_ENV=production
    ;;

    *)
      echo 'Error: unknown environment "$ENV"!'
      exit 1
    ;;
  esac

  # npm run build:ssr-prod
  echo " + Building client..."
  npx ng build --configuration $BUILD_ENV
  echo " + Building server..."
  npx ng run megyei-lapok:server:$BUILD_ENV
  mkdir ./dist/server/environments/
}

buildBasePortal

echo "Preparation done - starting to package..."

for CURR_PORTAL in $PORTALS; do
  PROD_PATH=./target/$CURR_PORTAL
  echo "Processing portal: ${CURR_PORTAL} to ${PROD_PATH}"

  mkdir -p $PROD_PATH
  npm run favicon:${CURR_PORTAL}
  cp -R ./dist/* $PROD_PATH/
  generateConfig $CURR_PORTAL
  copyConfig $CURR_PORTAL $PROD_PATH
  echo " + Prod deploy for ${CURR_PORTAL} created at ${PROD_PATH}."
done

echo "Done."
